<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: Vector types</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a>  </div>
  <div class="headertitle">
<div class="title">Vector types<div class="ingroups"><a class="el" href="a00280.html">Core features</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Vector types of two to four components with an exhaustive set of operators.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga0b6123e03653cc1bbe366fc55238a934"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, bool, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00281.html#ga0b6123e03653cc1bbe366fc55238a934">bvec2</a></td></tr>
<tr class="memdesc:ga0b6123e03653cc1bbe366fc55238a934"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector of boolean.  <a href="a00281.html#ga0b6123e03653cc1bbe366fc55238a934">More...</a><br /></td></tr>
<tr class="separator:ga0b6123e03653cc1bbe366fc55238a934"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga197151b72dfaf289daf98b361760ffe7"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, bool, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00281.html#ga197151b72dfaf289daf98b361760ffe7">bvec3</a></td></tr>
<tr class="memdesc:ga197151b72dfaf289daf98b361760ffe7"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector of boolean.  <a href="a00281.html#ga197151b72dfaf289daf98b361760ffe7">More...</a><br /></td></tr>
<tr class="separator:ga197151b72dfaf289daf98b361760ffe7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9f7b9712373ff4342d9114619b55f5e3"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 4, bool, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00281.html#ga9f7b9712373ff4342d9114619b55f5e3">bvec4</a></td></tr>
<tr class="memdesc:ga9f7b9712373ff4342d9114619b55f5e3"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector of boolean.  <a href="a00281.html#ga9f7b9712373ff4342d9114619b55f5e3">More...</a><br /></td></tr>
<tr class="separator:ga9f7b9712373ff4342d9114619b55f5e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8b09c71aaac7da7867ae58377fe219a8"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, double, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00281.html#ga8b09c71aaac7da7867ae58377fe219a8">dvec2</a></td></tr>
<tr class="memdesc:ga8b09c71aaac7da7867ae58377fe219a8"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector of double-precision floating-point numbers.  <a href="a00281.html#ga8b09c71aaac7da7867ae58377fe219a8">More...</a><br /></td></tr>
<tr class="separator:ga8b09c71aaac7da7867ae58377fe219a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5b83ae3d0fdec519c038e4d2cf967cf0"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, double, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00281.html#ga5b83ae3d0fdec519c038e4d2cf967cf0">dvec3</a></td></tr>
<tr class="memdesc:ga5b83ae3d0fdec519c038e4d2cf967cf0"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector of double-precision floating-point numbers.  <a href="a00281.html#ga5b83ae3d0fdec519c038e4d2cf967cf0">More...</a><br /></td></tr>
<tr class="separator:ga5b83ae3d0fdec519c038e4d2cf967cf0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga57debab5d98ce618f7b2a97fe26eb3ac"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 4, double, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00281.html#ga57debab5d98ce618f7b2a97fe26eb3ac">dvec4</a></td></tr>
<tr class="memdesc:ga57debab5d98ce618f7b2a97fe26eb3ac"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector of double-precision floating-point numbers.  <a href="a00281.html#ga57debab5d98ce618f7b2a97fe26eb3ac">More...</a><br /></td></tr>
<tr class="separator:ga57debab5d98ce618f7b2a97fe26eb3ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6f9269106d91b2d2b91bcf27cd5f5560"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, int, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00281.html#ga6f9269106d91b2d2b91bcf27cd5f5560">ivec2</a></td></tr>
<tr class="memdesc:ga6f9269106d91b2d2b91bcf27cd5f5560"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector of signed integer numbers.  <a href="a00281.html#ga6f9269106d91b2d2b91bcf27cd5f5560">More...</a><br /></td></tr>
<tr class="separator:ga6f9269106d91b2d2b91bcf27cd5f5560"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad0d784d8eee201aca362484d2daee46c"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, int, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00281.html#gad0d784d8eee201aca362484d2daee46c">ivec3</a></td></tr>
<tr class="memdesc:gad0d784d8eee201aca362484d2daee46c"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector of signed integer numbers.  <a href="a00281.html#gad0d784d8eee201aca362484d2daee46c">More...</a><br /></td></tr>
<tr class="separator:gad0d784d8eee201aca362484d2daee46c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5abb4603dae0ce58c595e66d9123d812"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 4, int, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00281.html#ga5abb4603dae0ce58c595e66d9123d812">ivec4</a></td></tr>
<tr class="memdesc:ga5abb4603dae0ce58c595e66d9123d812"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector of signed integer numbers.  <a href="a00281.html#ga5abb4603dae0ce58c595e66d9123d812">More...</a><br /></td></tr>
<tr class="separator:ga5abb4603dae0ce58c595e66d9123d812"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2f6d9ec3ae14813ade37d6aee3715fdb"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, unsigned int, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00281.html#ga2f6d9ec3ae14813ade37d6aee3715fdb">uvec2</a></td></tr>
<tr class="memdesc:ga2f6d9ec3ae14813ade37d6aee3715fdb"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector of unsigned integer numbers.  <a href="a00281.html#ga2f6d9ec3ae14813ade37d6aee3715fdb">More...</a><br /></td></tr>
<tr class="separator:ga2f6d9ec3ae14813ade37d6aee3715fdb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3d3e55874babd4bf93baa7bbc83ae418"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, unsigned int, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00281.html#ga3d3e55874babd4bf93baa7bbc83ae418">uvec3</a></td></tr>
<tr class="memdesc:ga3d3e55874babd4bf93baa7bbc83ae418"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector of unsigned integer numbers.  <a href="a00281.html#ga3d3e55874babd4bf93baa7bbc83ae418">More...</a><br /></td></tr>
<tr class="separator:ga3d3e55874babd4bf93baa7bbc83ae418"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa57e96bb337867329d5f43bcc27c1095"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 4, unsigned int, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00281.html#gaa57e96bb337867329d5f43bcc27c1095">uvec4</a></td></tr>
<tr class="memdesc:gaa57e96bb337867329d5f43bcc27c1095"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector of unsigned integer numbers.  <a href="a00281.html#gaa57e96bb337867329d5f43bcc27c1095">More...</a><br /></td></tr>
<tr class="separator:gaa57e96bb337867329d5f43bcc27c1095"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabe65c061834f61b4f7cb6037b19006a4"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, float, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">vec2</a></td></tr>
<tr class="memdesc:gabe65c061834f61b4f7cb6037b19006a4"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector of single-precision floating-point numbers.  <a href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">More...</a><br /></td></tr>
<tr class="separator:gabe65c061834f61b4f7cb6037b19006a4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9c3019b13faf179e4ad3626ea66df334"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, float, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00281.html#ga9c3019b13faf179e4ad3626ea66df334">vec3</a></td></tr>
<tr class="memdesc:ga9c3019b13faf179e4ad3626ea66df334"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector of single-precision floating-point numbers.  <a href="a00281.html#ga9c3019b13faf179e4ad3626ea66df334">More...</a><br /></td></tr>
<tr class="separator:ga9c3019b13faf179e4ad3626ea66df334"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac215a35481a6597d1bf622a382e9d6e2"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 4, float, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a></td></tr>
<tr class="memdesc:gac215a35481a6597d1bf622a382e9d6e2"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector of single-precision floating-point numbers.  <a href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">More...</a><br /></td></tr>
<tr class="separator:gac215a35481a6597d1bf622a382e9d6e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Vector types of two to four components with an exhaustive set of operators. </p>
<h2 class="groupheader">Typedef Documentation</h2>
<a class="anchor" id="ga0b6123e03653cc1bbe366fc55238a934"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, bool, defaultp &gt; bvec2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 components vector of boolean. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd></dl>

<p>Definition at line <a class="el" href="a00191_source.html#l00015">15</a> of file <a class="el" href="a00191_source.html">vector_bool2.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga197151b72dfaf289daf98b361760ffe7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, bool, defaultp &gt; bvec3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 components vector of boolean. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd></dl>

<p>Definition at line <a class="el" href="a00193_source.html#l00015">15</a> of file <a class="el" href="a00193_source.html">vector_bool3.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga9f7b9712373ff4342d9114619b55f5e3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 4, bool, defaultp &gt; bvec4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 components vector of boolean. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd></dl>

<p>Definition at line <a class="el" href="a00195_source.html#l00015">15</a> of file <a class="el" href="a00195_source.html">vector_bool4.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga8b09c71aaac7da7867ae58377fe219a8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, f64, defaultp &gt; dvec2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 components vector of double-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd></dl>

<p>Definition at line <a class="el" href="a00200_source.html#l00015">15</a> of file <a class="el" href="a00200_source.html">vector_double2.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga5b83ae3d0fdec519c038e4d2cf967cf0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, f64, defaultp &gt; dvec3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 components vector of double-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd></dl>

<p>Definition at line <a class="el" href="a00202_source.html#l00015">15</a> of file <a class="el" href="a00202_source.html">vector_double3.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga57debab5d98ce618f7b2a97fe26eb3ac"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 4, f64, defaultp &gt; dvec4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 components vector of double-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd></dl>

<p>Definition at line <a class="el" href="a00204_source.html#l00015">15</a> of file <a class="el" href="a00204_source.html">vector_double4.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga6f9269106d91b2d2b91bcf27cd5f5560"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, i32, defaultp &gt; ivec2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 components vector of signed integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd></dl>

<p>Definition at line <a class="el" href="a00216_source.html#l00015">15</a> of file <a class="el" href="a00216_source.html">vector_int2.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gad0d784d8eee201aca362484d2daee46c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, i32, defaultp &gt; ivec3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 components vector of signed integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd></dl>

<p>Definition at line <a class="el" href="a00218_source.html#l00015">15</a> of file <a class="el" href="a00218_source.html">vector_int3.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga5abb4603dae0ce58c595e66d9123d812"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 4, i32, defaultp &gt; ivec4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 components vector of signed integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd></dl>

<p>Definition at line <a class="el" href="a00220_source.html#l00015">15</a> of file <a class="el" href="a00220_source.html">vector_int4.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga2f6d9ec3ae14813ade37d6aee3715fdb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, u32, defaultp &gt; uvec2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 components vector of unsigned integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd></dl>

<p>Definition at line <a class="el" href="a00228_source.html#l00015">15</a> of file <a class="el" href="a00228_source.html">vector_uint2.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga3d3e55874babd4bf93baa7bbc83ae418"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, u32, defaultp &gt; uvec3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 components vector of unsigned integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd></dl>

<p>Definition at line <a class="el" href="a00230_source.html#l00015">15</a> of file <a class="el" href="a00230_source.html">vector_uint3.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaa57e96bb337867329d5f43bcc27c1095"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 4, u32, defaultp &gt; uvec4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 components vector of unsigned integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd></dl>

<p>Definition at line <a class="el" href="a00232_source.html#l00015">15</a> of file <a class="el" href="a00232_source.html">vector_uint4.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gabe65c061834f61b4f7cb6037b19006a4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, float, defaultp &gt; vec2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 components vector of single-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd></dl>

<p>Definition at line <a class="el" href="a00208_source.html#l00015">15</a> of file <a class="el" href="a00208_source.html">vector_float2.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga9c3019b13faf179e4ad3626ea66df334"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, float, defaultp &gt; vec3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 components vector of single-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd></dl>

<p>Definition at line <a class="el" href="a00210_source.html#l00015">15</a> of file <a class="el" href="a00210_source.html">vector_float3.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gac215a35481a6597d1bf622a382e9d6e2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 4, float, defaultp &gt; vec4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 components vector of single-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd></dl>

<p>Definition at line <a class="el" href="a00212_source.html#l00015">15</a> of file <a class="el" href="a00212_source.html">vector_float4.hpp</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
