<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_color_space_YCoCg</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_color_space_YCoCg<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00014.html" title="GLM_GTX_color_space_YCoCg ">glm/gtx/color_space_YCoCg.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga0606353ec2a9b9eaa84f1b02ec391bc5"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga0606353ec2a9b9eaa84f1b02ec391bc5"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00313.html#ga0606353ec2a9b9eaa84f1b02ec391bc5">rgb2YCoCg</a> (vec&lt; 3, T, Q &gt; const &amp;rgbColor)</td></tr>
<tr class="memdesc:ga0606353ec2a9b9eaa84f1b02ec391bc5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a color from RGB color space to YCoCg color space.  <a href="a00313.html#ga0606353ec2a9b9eaa84f1b02ec391bc5">More...</a><br /></td></tr>
<tr class="separator:ga0606353ec2a9b9eaa84f1b02ec391bc5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0389772e44ca0fd2ba4a79bdd8efe898"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga0389772e44ca0fd2ba4a79bdd8efe898"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00313.html#ga0389772e44ca0fd2ba4a79bdd8efe898">rgb2YCoCgR</a> (vec&lt; 3, T, Q &gt; const &amp;rgbColor)</td></tr>
<tr class="memdesc:ga0389772e44ca0fd2ba4a79bdd8efe898"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a color from RGB color space to YCoCgR color space.  <a href="a00313.html#ga0389772e44ca0fd2ba4a79bdd8efe898">More...</a><br /></td></tr>
<tr class="separator:ga0389772e44ca0fd2ba4a79bdd8efe898"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga163596b804c7241810b2534a99eb1343"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga163596b804c7241810b2534a99eb1343"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00313.html#ga163596b804c7241810b2534a99eb1343">YCoCg2rgb</a> (vec&lt; 3, T, Q &gt; const &amp;YCoCgColor)</td></tr>
<tr class="memdesc:ga163596b804c7241810b2534a99eb1343"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a color from YCoCg color space to RGB color space.  <a href="a00313.html#ga163596b804c7241810b2534a99eb1343">More...</a><br /></td></tr>
<tr class="separator:ga163596b804c7241810b2534a99eb1343"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf8d30574c8576838097d8e20c295384a"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf8d30574c8576838097d8e20c295384a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00313.html#gaf8d30574c8576838097d8e20c295384a">YCoCgR2rgb</a> (vec&lt; 3, T, Q &gt; const &amp;YCoCgColor)</td></tr>
<tr class="memdesc:gaf8d30574c8576838097d8e20c295384a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a color from YCoCgR color space to RGB color space.  <a href="a00313.html#gaf8d30574c8576838097d8e20c295384a">More...</a><br /></td></tr>
<tr class="separator:gaf8d30574c8576838097d8e20c295384a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00014.html" title="GLM_GTX_color_space_YCoCg ">glm/gtx/color_space_YCoCg.hpp</a>&gt; to use the features of this extension. </p>
<p>RGB to YCoCg conversions and operations </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga0606353ec2a9b9eaa84f1b02ec391bc5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::rgb2YCoCg </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>rgbColor</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a color from RGB color space to YCoCg color space. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00313.html" title="Include <glm/gtx/color_space_YCoCg.hpp> to use the features of this extension. ">GLM_GTX_color_space_YCoCg</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga0389772e44ca0fd2ba4a79bdd8efe898"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::rgb2YCoCgR </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>rgbColor</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a color from RGB color space to YCoCgR color space. </p>
<dl class="section see"><dt>See also</dt><dd>"YCoCg-R: A Color Space with RGB Reversibility and Low Dynamic Range" </dd>
<dd>
<a class="el" href="a00313.html" title="Include <glm/gtx/color_space_YCoCg.hpp> to use the features of this extension. ">GLM_GTX_color_space_YCoCg</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga163596b804c7241810b2534a99eb1343"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::YCoCg2rgb </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>YCoCgColor</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a color from YCoCg color space to RGB color space. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00313.html" title="Include <glm/gtx/color_space_YCoCg.hpp> to use the features of this extension. ">GLM_GTX_color_space_YCoCg</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaf8d30574c8576838097d8e20c295384a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::YCoCgR2rgb </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>YCoCgColor</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a color from YCoCgR color space to RGB color space. </p>
<dl class="section see"><dt>See also</dt><dd>"YCoCg-R: A Color Space with RGB Reversibility and Low Dynamic Range" </dd>
<dd>
<a class="el" href="a00313.html" title="Include <glm/gtx/color_space_YCoCg.hpp> to use the features of this extension. ">GLM_GTX_color_space_YCoCg</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
