<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: ulp.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_4c6bd29c73fa4e5a2509e1c15f846751.html">gtc</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">ulp.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00306.html">GLM_GTC_ulp</a>  
<a href="#details">More...</a></p>

<p><a href="a00182_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a2358fa840554fa36531aee28f3e14d6b"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int&#160;</td><td class="memItemRight" valign="bottom"><b>float_distance</b> (float x, float y)</td></tr>
<tr class="memdesc:a2358fa840554fa36531aee28f3e14d6b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the distance in the number of ULP between 2 single-precision floating-point scalars.  <a href="a00236.html#a2358fa840554fa36531aee28f3e14d6b">More...</a><br /></td></tr>
<tr class="separator:a2358fa840554fa36531aee28f3e14d6b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a464d5c96158df04d96a11d97b00c51a7"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int64&#160;</td><td class="memItemRight" valign="bottom"><b>float_distance</b> (double x, double y)</td></tr>
<tr class="memdesc:a464d5c96158df04d96a11d97b00c51a7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the distance in the number of ULP between 2 double-precision floating-point scalars.  <a href="a00236.html#a464d5c96158df04d96a11d97b00c51a7">More...</a><br /></td></tr>
<tr class="separator:a464d5c96158df04d96a11d97b00c51a7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a15349749edb8373079f4dcd518cc3d02"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:a15349749edb8373079f4dcd518cc3d02"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, int, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><b>float_distance</b> (vec&lt; L, float, Q &gt; const &amp;x, vec&lt; L, float, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:a15349749edb8373079f4dcd518cc3d02"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the distance in the number of ULP between 2 single-precision floating-point scalars.  <a href="a00236.html#a15349749edb8373079f4dcd518cc3d02">More...</a><br /></td></tr>
<tr class="separator:a15349749edb8373079f4dcd518cc3d02"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac0726cf2e5ce7d03b0ac4c81438c07fb"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ac0726cf2e5ce7d03b0ac4c81438c07fb"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, int64, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><b>float_distance</b> (vec&lt; L, double, Q &gt; const &amp;x, vec&lt; L, double, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:ac0726cf2e5ce7d03b0ac4c81438c07fb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the distance in the number of ULP between 2 double-precision floating-point scalars.  <a href="a00236.html#ac0726cf2e5ce7d03b0ac4c81438c07fb">More...</a><br /></td></tr>
<tr class="separator:ac0726cf2e5ce7d03b0ac4c81438c07fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab21fbe69182da4f378862feeffe24b16"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ab21fbe69182da4f378862feeffe24b16"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><b>next_float</b> (genType x)</td></tr>
<tr class="memdesc:ab21fbe69182da4f378862feeffe24b16"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the next ULP value(s) after the input value(s).  <a href="a00236.html#ab21fbe69182da4f378862feeffe24b16">More...</a><br /></td></tr>
<tr class="separator:ab21fbe69182da4f378862feeffe24b16"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af8540f4caeba5037dee6506184f360b0"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:af8540f4caeba5037dee6506184f360b0"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><b>next_float</b> (genType x, int ULPs)</td></tr>
<tr class="memdesc:af8540f4caeba5037dee6506184f360b0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the value(s) ULP distance after the input value(s).  <a href="a00236.html#af8540f4caeba5037dee6506184f360b0">More...</a><br /></td></tr>
<tr class="separator:af8540f4caeba5037dee6506184f360b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a72c18d50df8ef360960ddf1f5d09c728"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:a72c18d50df8ef360960ddf1f5d09c728"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><b>next_float</b> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:a72c18d50df8ef360960ddf1f5d09c728"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the next ULP value(s) after the input value(s).  <a href="a00236.html#a72c18d50df8ef360960ddf1f5d09c728">More...</a><br /></td></tr>
<tr class="separator:a72c18d50df8ef360960ddf1f5d09c728"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a78b63ddacacb9e0e8f4172d85f4373aa"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:a78b63ddacacb9e0e8f4172d85f4373aa"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><b>next_float</b> (vec&lt; L, T, Q &gt; const &amp;x, int ULPs)</td></tr>
<tr class="memdesc:a78b63ddacacb9e0e8f4172d85f4373aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the value(s) ULP distance after the input value(s).  <a href="a00236.html#a78b63ddacacb9e0e8f4172d85f4373aa">More...</a><br /></td></tr>
<tr class="separator:a78b63ddacacb9e0e8f4172d85f4373aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a48e17607989d47bc99e16cce74543e19"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:a48e17607989d47bc99e16cce74543e19"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><b>next_float</b> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, int, Q &gt; const &amp;ULPs)</td></tr>
<tr class="memdesc:a48e17607989d47bc99e16cce74543e19"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the value(s) ULP distance after the input value(s).  <a href="a00236.html#a48e17607989d47bc99e16cce74543e19">More...</a><br /></td></tr>
<tr class="separator:a48e17607989d47bc99e16cce74543e19"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af2a8466ad7470fcafaf91b24b43d1d4d"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:af2a8466ad7470fcafaf91b24b43d1d4d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><b>prev_float</b> (genType x)</td></tr>
<tr class="memdesc:af2a8466ad7470fcafaf91b24b43d1d4d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the previous ULP value(s) before the input value(s).  <a href="a00236.html#af2a8466ad7470fcafaf91b24b43d1d4d">More...</a><br /></td></tr>
<tr class="separator:af2a8466ad7470fcafaf91b24b43d1d4d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a71d68bb1fff11ac1c757d44cd23ddf50"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:a71d68bb1fff11ac1c757d44cd23ddf50"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><b>prev_float</b> (genType x, int ULPs)</td></tr>
<tr class="memdesc:a71d68bb1fff11ac1c757d44cd23ddf50"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the value(s) ULP distance before the input value(s).  <a href="a00236.html#a71d68bb1fff11ac1c757d44cd23ddf50">More...</a><br /></td></tr>
<tr class="separator:a71d68bb1fff11ac1c757d44cd23ddf50"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af2268a89effe42c4d6952085fa616cee"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:af2268a89effe42c4d6952085fa616cee"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><b>prev_float</b> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:af2268a89effe42c4d6952085fa616cee"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the previous ULP value(s) before the input value(s).  <a href="a00236.html#af2268a89effe42c4d6952085fa616cee">More...</a><br /></td></tr>
<tr class="separator:af2268a89effe42c4d6952085fa616cee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa761d18f8e3a93752550c9ce9556749c"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:aa761d18f8e3a93752550c9ce9556749c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><b>prev_float</b> (vec&lt; L, T, Q &gt; const &amp;x, int ULPs)</td></tr>
<tr class="memdesc:aa761d18f8e3a93752550c9ce9556749c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the value(s) ULP distance before the input value(s).  <a href="a00236.html#aa761d18f8e3a93752550c9ce9556749c">More...</a><br /></td></tr>
<tr class="separator:aa761d18f8e3a93752550c9ce9556749c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a471608a1ffbf4472dc5c84216ea937e8"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:a471608a1ffbf4472dc5c84216ea937e8"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><b>prev_float</b> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, int, Q &gt; const &amp;ULPs)</td></tr>
<tr class="memdesc:a471608a1ffbf4472dc5c84216ea937e8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the value(s) ULP distance before the input value(s).  <a href="a00236.html#a471608a1ffbf4472dc5c84216ea937e8">More...</a><br /></td></tr>
<tr class="separator:a471608a1ffbf4472dc5c84216ea937e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00306.html">GLM_GTC_ulp</a> </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00280.html" title="Features that implement in C++ the GLSL specification as closely as possible. ">Core features</a> (dependence) </dd></dl>

<p>Definition in file <a class="el" href="a00182_source.html">ulp.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
