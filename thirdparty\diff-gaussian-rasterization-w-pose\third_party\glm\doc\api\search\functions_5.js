var searchData=
[
  ['faceforward',['faceforward',['../a00279.html#ga7aed0a36c738169402404a3a5d54e43b',1,'glm']]],
  ['factorial',['factorial',['../a00330.html#ga8cbd3120905f398ec321b5d1836e08fb',1,'glm']]],
  ['fastacos',['fastAcos',['../a00325.html#ga9721d63356e5d94fdc4b393a426ab26b',1,'glm']]],
  ['fastasin',['fastAsin',['../a00325.html#ga562cb62c51fbfe7fac7db0bce706b81f',1,'glm']]],
  ['fastatan',['fastAtan',['../a00325.html#ga8d197c6ef564f5e5d59af3b3f8adcc2c',1,'glm::fastAtan(T y, T x)'],['../a00325.html#gae25de86a968490ff56856fa425ec9d30',1,'glm::fastAtan(T angle)']]],
  ['fastcos',['fastCos',['../a00325.html#gab34c8b45c23c0165a64dcecfcc3b302a',1,'glm']]],
  ['fastdistance',['fastDistance',['../a00324.html#gaac333418d0c4e0cc6d3d219ed606c238',1,'glm::fastDistance(genType x, genType y)'],['../a00324.html#ga42d3e771fa7cb3c60d828e315829df19',1,'glm::fastDistance(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)']]],
  ['fastexp',['fastExp',['../a00323.html#gaa3180ac8f96ab37ab96e0cacaf608e10',1,'glm::fastExp(T x)'],['../a00323.html#ga3ba6153aec6bd74628f8b00530aa8d58',1,'glm::fastExp(vec&lt; L, T, Q &gt; const &amp;x)']]],
  ['fastexp2',['fastExp2',['../a00323.html#ga0af50585955eb14c60bb286297fabab2',1,'glm::fastExp2(T x)'],['../a00323.html#gacaaed8b67d20d244b7de217e7816c1b6',1,'glm::fastExp2(vec&lt; L, T, Q &gt; const &amp;x)']]],
  ['fastinversesqrt',['fastInverseSqrt',['../a00324.html#ga7f081b14d9c7035c8714eba5f7f75a8f',1,'glm::fastInverseSqrt(genType x)'],['../a00324.html#gadcd7be12b1e5ee182141359d4c45dd24',1,'glm::fastInverseSqrt(vec&lt; L, T, Q &gt; const &amp;x)']]],
  ['fastlength',['fastLength',['../a00324.html#gafe697d6287719538346bbdf8b1367c59',1,'glm::fastLength(genType x)'],['../a00324.html#ga90f66be92ef61e705c005e7b3209edb8',1,'glm::fastLength(vec&lt; L, T, Q &gt; const &amp;x)']]],
  ['fastlog',['fastLog',['../a00323.html#gae1bdc97b7f96a600e29c753f1cd4388a',1,'glm::fastLog(T x)'],['../a00323.html#ga937256993a7219e73f186bb348fe6be8',1,'glm::fastLog(vec&lt; L, T, Q &gt; const &amp;x)']]],
  ['fastlog2',['fastLog2',['../a00323.html#ga6e98118685f6dc9e05fbb13dd5e5234e',1,'glm::fastLog2(T x)'],['../a00323.html#ga7562043539194ccc24649f8475bc5584',1,'glm::fastLog2(vec&lt; L, T, Q &gt; const &amp;x)']]],
  ['fastmix',['fastMix',['../a00352.html#ga264e10708d58dd0ff53b7902a2bd2561',1,'glm']]],
  ['fastnormalize',['fastNormalize',['../a00324.html#ga3b02c1d6e0c754144e2f1e110bf9f16c',1,'glm']]],
  ['fastnormalizedot',['fastNormalizeDot',['../a00345.html#ga2746fb9b5bd22b06b2f7c8babba5de9e',1,'glm']]],
  ['fastpow',['fastPow',['../a00323.html#ga5340e98a11fcbbd936ba6e983a154d50',1,'glm::fastPow(genType x, genType y)'],['../a00323.html#ga15325a8ed2d1c4ed2412c4b3b3927aa2',1,'glm::fastPow(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)'],['../a00323.html#ga7f2562db9c3e02ae76169c36b086c3f6',1,'glm::fastPow(genTypeT x, genTypeU y)'],['../a00323.html#ga1abe488c0829da5b9de70ac64aeaa7e5',1,'glm::fastPow(vec&lt; L, T, Q &gt; const &amp;x)']]],
  ['fastsin',['fastSin',['../a00325.html#ga0aab3257bb3b628d10a1e0483e2c6915',1,'glm']]],
  ['fastsqrt',['fastSqrt',['../a00324.html#ga6c460e9414a50b2fc455c8f64c86cdc9',1,'glm::fastSqrt(genType x)'],['../a00324.html#gae83f0c03614f73eae5478c5b6274ee6d',1,'glm::fastSqrt(vec&lt; L, T, Q &gt; const &amp;x)']]],
  ['fasttan',['fastTan',['../a00325.html#gaf29b9c1101a10007b4f79ee89df27ba2',1,'glm']]],
  ['fclamp',['fclamp',['../a00321.html#ga1e28539d3a46965ed9ef92ec7cb3b18a',1,'glm::fclamp(genType x, genType minVal, genType maxVal)'],['../a00321.html#ga60796d08903489ee185373593bc16b9d',1,'glm::fclamp(vec&lt; L, T, Q &gt; const &amp;x, T minVal, T maxVal)'],['../a00321.html#ga5c15fa4709763c269c86c0b8b3aa2297',1,'glm::fclamp(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;minVal, vec&lt; L, T, Q &gt; const &amp;maxVal)']]],
  ['findlsb',['findLSB',['../a00370.html#gaf74c4d969fa34ab8acb9d390f5ca5274',1,'glm::findLSB(genIUType x)'],['../a00370.html#ga4454c0331d6369888c28ab677f4810c7',1,'glm::findLSB(vec&lt; L, T, Q &gt; const &amp;v)']]],
  ['findmsb',['findMSB',['../a00370.html#ga7e4a794d766861c70bc961630f8ef621',1,'glm::findMSB(genIUType x)'],['../a00370.html#ga39ac4d52028bb6ab08db5ad6562c2872',1,'glm::findMSB(vec&lt; L, T, Q &gt; const &amp;v)']]],
  ['findnsb',['findNSB',['../a00261.html#ga2777901e41ad6e1e9d0ad6cc855d1075',1,'glm::findNSB(genIUType x, int significantBitCount)'],['../a00274.html#gaff61eca266da315002a3db92ff0dd604',1,'glm::findNSB(vec&lt; L, T, Q &gt; const &amp;Source, vec&lt; L, int, Q &gt; SignificantBitCount)']]],
  ['fliplr',['fliplr',['../a00336.html#gaf39f4e5f78eb29c1a90277d45b9b3feb',1,'glm']]],
  ['flipud',['flipud',['../a00336.html#ga85003371f0ba97380dd25e8905de1870',1,'glm']]],
  ['floatbitstoint',['floatBitsToInt',['../a00241.html#ga1425c1c3160ec51214b03a0469a3013d',1,'glm::floatBitsToInt(float const &amp;v)'],['../a00241.html#ga99f7d62f78ac5ea3b49bae715c9488ed',1,'glm::floatBitsToInt(vec&lt; L, float, Q &gt; const &amp;v)']]],
  ['floatbitstouint',['floatBitsToUint',['../a00241.html#ga70e0271c34af52f3100c7960e18c3f2b',1,'glm::floatBitsToUint(float const &amp;v)'],['../a00241.html#ga49418ba4c8a60fbbb5d57b705f3e26db',1,'glm::floatBitsToUint(vec&lt; L, float, Q &gt; const &amp;v)']]],
  ['floor',['floor',['../a00241.html#gaa9d0742639e85b29c7c5de11cfd6840d',1,'glm']]],
  ['floor_5flog2',['floor_log2',['../a00330.html#ga7011b4e1c1e1ed492149b028feacc00e',1,'glm']]],
  ['floormultiple',['floorMultiple',['../a00302.html#ga2ffa3cd5f2ea746ee1bf57c46da6315e',1,'glm::floorMultiple(genType v, genType Multiple)'],['../a00302.html#gacdd8901448f51f0b192380e422fae3e4',1,'glm::floorMultiple(vec&lt; L, T, Q &gt; const &amp;v, vec&lt; L, T, Q &gt; const &amp;Multiple)']]],
  ['floorpoweroftwo',['floorPowerOfTwo',['../a00302.html#gafe273a57935d04c9db677bf67f9a71f4',1,'glm::floorPowerOfTwo(genIUType v)'],['../a00302.html#gaf0d591a8fca8ddb9289cdeb44b989c2d',1,'glm::floorPowerOfTwo(vec&lt; L, T, Q &gt; const &amp;v)']]],
  ['fma',['fma',['../a00241.html#gad0f444d4b81cc53c3b6edf5aa25078c2',1,'glm']]],
  ['fmax',['fmax',['../a00258.html#ga36920478565cf608e93064283ce06421',1,'glm::fmax(T a, T b)'],['../a00258.html#ga0007bba71ca451ac70e99d28dfbeaab9',1,'glm::fmax(T a, T b, T C)'],['../a00258.html#ga27e260b1ff4d04c3ad4b864d26cbaf08',1,'glm::fmax(T a, T b, T C, T D)'],['../a00267.html#gad66b6441f7200db16c9f341711733c56',1,'glm::fmax(vec&lt; L, T, Q &gt; const &amp;a, T b)'],['../a00267.html#ga8df4be3f48d6717c40ea788fd30deebf',1,'glm::fmax(vec&lt; L, T, Q &gt; const &amp;a, vec&lt; L, T, Q &gt; const &amp;b)'],['../a00267.html#ga0f04ba924294dae4234ca93ede23229a',1,'glm::fmax(vec&lt; L, T, Q &gt; const &amp;a, vec&lt; L, T, Q &gt; const &amp;b, vec&lt; L, T, Q &gt; const &amp;c)'],['../a00267.html#ga4ed3eb250ccbe17bfe8ded8a6b72d230',1,'glm::fmax(vec&lt; L, T, Q &gt; const &amp;a, vec&lt; L, T, Q &gt; const &amp;b, vec&lt; L, T, Q &gt; const &amp;c, vec&lt; L, T, Q &gt; const &amp;d)'],['../a00321.html#gae5792cb2b51190057e4aea027eb56f81',1,'glm::fmax(genType x, genType y)']]],
  ['fmin',['fmin',['../a00258.html#ga7b2b438a765e2a62098c79eb212f28f0',1,'glm::fmin(T a, T b)'],['../a00258.html#ga1a95fe4cf5437e8133f1093fe9726a64',1,'glm::fmin(T a, T b, T c)'],['../a00258.html#ga3d6f9c6c16bfd6f38f2c4f8076e8b661',1,'glm::fmin(T a, T b, T c, T d)'],['../a00267.html#gae989203363cff9eab5093630df4fe071',1,'glm::fmin(vec&lt; L, T, Q &gt; const &amp;x, T y)'],['../a00267.html#ga7c42e93cd778c9181d1cdeea4d3e43bd',1,'glm::fmin(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)'],['../a00267.html#ga7e62739055b49189d9355471f78fe000',1,'glm::fmin(vec&lt; L, T, Q &gt; const &amp;a, vec&lt; L, T, Q &gt; const &amp;b, vec&lt; L, T, Q &gt; const &amp;c)'],['../a00267.html#ga4a543dd7d22ad1f3b8b839f808a9d93c',1,'glm::fmin(vec&lt; L, T, Q &gt; const &amp;a, vec&lt; L, T, Q &gt; const &amp;b, vec&lt; L, T, Q &gt; const &amp;c, vec&lt; L, T, Q &gt; const &amp;d)'],['../a00321.html#gaa3200559611ac5b9b9ae7283547916a7',1,'glm::fmin(genType x, genType y)']]],
  ['fmod',['fmod',['../a00314.html#gae5e80425df9833164ad469e83b475fb4',1,'glm']]],
  ['four_5fover_5fpi',['four_over_pi',['../a00290.html#ga753950e5140e4ea6a88e4a18ba61dc09',1,'glm']]],
  ['fract',['fract',['../a00241.html#ga8ba89e40e55ae5cdf228548f9b7639c7',1,'glm::fract(genType x)'],['../a00241.html#ga2df623004f634b440d61e018d62c751b',1,'glm::fract(vec&lt; L, T, Q &gt; const &amp;x)']]],
  ['frexp',['frexp',['../a00241.html#gaddf5ef73283c171730e0bcc11833fa81',1,'glm']]],
  ['frustum',['frustum',['../a00243.html#ga0bcd4542e0affc63a0b8c08fcb839ea9',1,'glm']]],
  ['frustumlh',['frustumLH',['../a00243.html#gae4277c37f61d81da01bc9db14ea90296',1,'glm']]],
  ['frustumlh_5fno',['frustumLH_NO',['../a00243.html#ga259520cad03b3f8bca9417920035ed01',1,'glm']]],
  ['frustumlh_5fzo',['frustumLH_ZO',['../a00243.html#ga94218b094862d17798370242680b9030',1,'glm']]],
  ['frustumno',['frustumNO',['../a00243.html#gae34ec664ad44860bf4b5ba631f0e0e90',1,'glm']]],
  ['frustumrh',['frustumRH',['../a00243.html#ga4366ab45880c6c5f8b3e8c371ca4b136',1,'glm']]],
  ['frustumrh_5fno',['frustumRH_NO',['../a00243.html#ga9236c8439f21be186b79c97b588836b9',1,'glm']]],
  ['frustumrh_5fzo',['frustumRH_ZO',['../a00243.html#ga7654a9227f14d5382786b9fc0eb5692d',1,'glm']]],
  ['frustumzo',['frustumZO',['../a00243.html#gaa73322e152edf50cf30a6edac342a757',1,'glm']]]
];
