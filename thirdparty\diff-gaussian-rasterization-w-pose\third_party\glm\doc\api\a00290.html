<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTC_constants</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTC_constants<div class="ingroups"><a class="el" href="a00286.html">Recommended extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00021.html" title="GLM_GTC_constants ">glm/gtc/constants.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga4b7956eb6e2fbedfc7cf2e46e85c5139"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga4b7956eb6e2fbedfc7cf2e46e85c5139"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#ga4b7956eb6e2fbedfc7cf2e46e85c5139">e</a> ()</td></tr>
<tr class="memdesc:ga4b7956eb6e2fbedfc7cf2e46e85c5139"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return e constant.  <a href="a00290.html#ga4b7956eb6e2fbedfc7cf2e46e85c5139">More...</a><br /></td></tr>
<tr class="separator:ga4b7956eb6e2fbedfc7cf2e46e85c5139"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad8fe2e6f90bce9d829e9723b649fbd42"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gad8fe2e6f90bce9d829e9723b649fbd42"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#gad8fe2e6f90bce9d829e9723b649fbd42">euler</a> ()</td></tr>
<tr class="memdesc:gad8fe2e6f90bce9d829e9723b649fbd42"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return Euler's constant.  <a href="a00290.html#gad8fe2e6f90bce9d829e9723b649fbd42">More...</a><br /></td></tr>
<tr class="separator:gad8fe2e6f90bce9d829e9723b649fbd42"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga753950e5140e4ea6a88e4a18ba61dc09"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga753950e5140e4ea6a88e4a18ba61dc09"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#ga753950e5140e4ea6a88e4a18ba61dc09">four_over_pi</a> ()</td></tr>
<tr class="memdesc:ga753950e5140e4ea6a88e4a18ba61dc09"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return 4 / pi.  <a href="a00290.html#ga753950e5140e4ea6a88e4a18ba61dc09">More...</a><br /></td></tr>
<tr class="separator:ga753950e5140e4ea6a88e4a18ba61dc09"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga748cf8642830657c5b7eae04d0a80899"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga748cf8642830657c5b7eae04d0a80899"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#ga748cf8642830657c5b7eae04d0a80899">golden_ratio</a> ()</td></tr>
<tr class="memdesc:ga748cf8642830657c5b7eae04d0a80899"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the golden ratio constant.  <a href="a00290.html#ga748cf8642830657c5b7eae04d0a80899">More...</a><br /></td></tr>
<tr class="separator:ga748cf8642830657c5b7eae04d0a80899"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0c36b41d462e45641faf7d7938948bac"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga0c36b41d462e45641faf7d7938948bac"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#ga0c36b41d462e45641faf7d7938948bac">half_pi</a> ()</td></tr>
<tr class="memdesc:ga0c36b41d462e45641faf7d7938948bac"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return pi / 2.  <a href="a00290.html#ga0c36b41d462e45641faf7d7938948bac">More...</a><br /></td></tr>
<tr class="separator:ga0c36b41d462e45641faf7d7938948bac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaca94292c839ed31a405ab7a81ae7e850"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gaca94292c839ed31a405ab7a81ae7e850"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#gaca94292c839ed31a405ab7a81ae7e850">ln_ln_two</a> ()</td></tr>
<tr class="memdesc:gaca94292c839ed31a405ab7a81ae7e850"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return ln(ln(2)).  <a href="a00290.html#gaca94292c839ed31a405ab7a81ae7e850">More...</a><br /></td></tr>
<tr class="separator:gaca94292c839ed31a405ab7a81ae7e850"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf97ebc6c059ffd788e6c4946f71ef66c"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gaf97ebc6c059ffd788e6c4946f71ef66c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#gaf97ebc6c059ffd788e6c4946f71ef66c">ln_ten</a> ()</td></tr>
<tr class="memdesc:gaf97ebc6c059ffd788e6c4946f71ef66c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return ln(10).  <a href="a00290.html#gaf97ebc6c059ffd788e6c4946f71ef66c">More...</a><br /></td></tr>
<tr class="separator:gaf97ebc6c059ffd788e6c4946f71ef66c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga24f4d27765678116f41a2f336ab7975c"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga24f4d27765678116f41a2f336ab7975c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#ga24f4d27765678116f41a2f336ab7975c">ln_two</a> ()</td></tr>
<tr class="memdesc:ga24f4d27765678116f41a2f336ab7975c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return ln(2).  <a href="a00290.html#ga24f4d27765678116f41a2f336ab7975c">More...</a><br /></td></tr>
<tr class="separator:ga24f4d27765678116f41a2f336ab7975c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga39c2fb227631ca25894326529bdd1ee5"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga39c2fb227631ca25894326529bdd1ee5"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#ga39c2fb227631ca25894326529bdd1ee5">one</a> ()</td></tr>
<tr class="memdesc:ga39c2fb227631ca25894326529bdd1ee5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return 1.  <a href="a00290.html#ga39c2fb227631ca25894326529bdd1ee5">More...</a><br /></td></tr>
<tr class="separator:ga39c2fb227631ca25894326529bdd1ee5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga555150da2b06d23c8738981d5013e0eb"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga555150da2b06d23c8738981d5013e0eb"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#ga555150da2b06d23c8738981d5013e0eb">one_over_pi</a> ()</td></tr>
<tr class="memdesc:ga555150da2b06d23c8738981d5013e0eb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return 1 / pi.  <a href="a00290.html#ga555150da2b06d23c8738981d5013e0eb">More...</a><br /></td></tr>
<tr class="separator:ga555150da2b06d23c8738981d5013e0eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga788fa23a0939bac4d1d0205fb4f35818"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga788fa23a0939bac4d1d0205fb4f35818"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#ga788fa23a0939bac4d1d0205fb4f35818">one_over_root_two</a> ()</td></tr>
<tr class="memdesc:ga788fa23a0939bac4d1d0205fb4f35818"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return 1 / sqrt(2).  <a href="a00290.html#ga788fa23a0939bac4d1d0205fb4f35818">More...</a><br /></td></tr>
<tr class="separator:ga788fa23a0939bac4d1d0205fb4f35818"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7c922b427986cbb2e4c6ac69874eefbc"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga7c922b427986cbb2e4c6ac69874eefbc"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#ga7c922b427986cbb2e4c6ac69874eefbc">one_over_two_pi</a> ()</td></tr>
<tr class="memdesc:ga7c922b427986cbb2e4c6ac69874eefbc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return 1 / (pi * 2).  <a href="a00290.html#ga7c922b427986cbb2e4c6ac69874eefbc">More...</a><br /></td></tr>
<tr class="separator:ga7c922b427986cbb2e4c6ac69874eefbc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3c9df42bd73c519a995c43f0f99e77e0"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga3c9df42bd73c519a995c43f0f99e77e0"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#ga3c9df42bd73c519a995c43f0f99e77e0">quarter_pi</a> ()</td></tr>
<tr class="memdesc:ga3c9df42bd73c519a995c43f0f99e77e0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return pi / 4.  <a href="a00290.html#ga3c9df42bd73c519a995c43f0f99e77e0">More...</a><br /></td></tr>
<tr class="separator:ga3c9df42bd73c519a995c43f0f99e77e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae9ebbded75b53d4faeb1e4ef8b3347a2"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gae9ebbded75b53d4faeb1e4ef8b3347a2"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#gae9ebbded75b53d4faeb1e4ef8b3347a2">root_five</a> ()</td></tr>
<tr class="memdesc:gae9ebbded75b53d4faeb1e4ef8b3347a2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return sqrt(5).  <a href="a00290.html#gae9ebbded75b53d4faeb1e4ef8b3347a2">More...</a><br /></td></tr>
<tr class="separator:gae9ebbded75b53d4faeb1e4ef8b3347a2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4e276cb823cc5e612d4f89ed99c75039"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga4e276cb823cc5e612d4f89ed99c75039"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#ga4e276cb823cc5e612d4f89ed99c75039">root_half_pi</a> ()</td></tr>
<tr class="memdesc:ga4e276cb823cc5e612d4f89ed99c75039"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return sqrt(pi / 2).  <a href="a00290.html#ga4e276cb823cc5e612d4f89ed99c75039">More...</a><br /></td></tr>
<tr class="separator:ga4e276cb823cc5e612d4f89ed99c75039"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4129412e96b33707a77c1a07652e23e2"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga4129412e96b33707a77c1a07652e23e2"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#ga4129412e96b33707a77c1a07652e23e2">root_ln_four</a> ()</td></tr>
<tr class="memdesc:ga4129412e96b33707a77c1a07652e23e2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return sqrt(ln(4)).  <a href="a00290.html#ga4129412e96b33707a77c1a07652e23e2">More...</a><br /></td></tr>
<tr class="separator:ga4129412e96b33707a77c1a07652e23e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga261380796b2cd496f68d2cf1d08b8eb9"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga261380796b2cd496f68d2cf1d08b8eb9"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#ga261380796b2cd496f68d2cf1d08b8eb9">root_pi</a> ()</td></tr>
<tr class="memdesc:ga261380796b2cd496f68d2cf1d08b8eb9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return square root of pi.  <a href="a00290.html#ga261380796b2cd496f68d2cf1d08b8eb9">More...</a><br /></td></tr>
<tr class="separator:ga261380796b2cd496f68d2cf1d08b8eb9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4f286be4abe88be1eed7d2a9f6cb193e"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga4f286be4abe88be1eed7d2a9f6cb193e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#ga4f286be4abe88be1eed7d2a9f6cb193e">root_three</a> ()</td></tr>
<tr class="memdesc:ga4f286be4abe88be1eed7d2a9f6cb193e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return sqrt(3).  <a href="a00290.html#ga4f286be4abe88be1eed7d2a9f6cb193e">More...</a><br /></td></tr>
<tr class="separator:ga4f286be4abe88be1eed7d2a9f6cb193e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga74e607d29020f100c0d0dc46ce2ca950"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga74e607d29020f100c0d0dc46ce2ca950"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#ga74e607d29020f100c0d0dc46ce2ca950">root_two</a> ()</td></tr>
<tr class="memdesc:ga74e607d29020f100c0d0dc46ce2ca950"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return sqrt(2).  <a href="a00290.html#ga74e607d29020f100c0d0dc46ce2ca950">More...</a><br /></td></tr>
<tr class="separator:ga74e607d29020f100c0d0dc46ce2ca950"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2bcedc575039fe0cd765742f8bbb0bd3"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga2bcedc575039fe0cd765742f8bbb0bd3"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#ga2bcedc575039fe0cd765742f8bbb0bd3">root_two_pi</a> ()</td></tr>
<tr class="memdesc:ga2bcedc575039fe0cd765742f8bbb0bd3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return sqrt(2 * pi).  <a href="a00290.html#ga2bcedc575039fe0cd765742f8bbb0bd3">More...</a><br /></td></tr>
<tr class="separator:ga2bcedc575039fe0cd765742f8bbb0bd3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3077c6311010a214b69ddc8214ec13b5"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga3077c6311010a214b69ddc8214ec13b5"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#ga3077c6311010a214b69ddc8214ec13b5">third</a> ()</td></tr>
<tr class="memdesc:ga3077c6311010a214b69ddc8214ec13b5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return 1 / 3.  <a href="a00290.html#ga3077c6311010a214b69ddc8214ec13b5">More...</a><br /></td></tr>
<tr class="separator:ga3077c6311010a214b69ddc8214ec13b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae94950df74b0ce382b1fc1d978ef7394"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gae94950df74b0ce382b1fc1d978ef7394"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#gae94950df74b0ce382b1fc1d978ef7394">three_over_two_pi</a> ()</td></tr>
<tr class="memdesc:gae94950df74b0ce382b1fc1d978ef7394"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return pi / 2 * 3.  <a href="a00290.html#gae94950df74b0ce382b1fc1d978ef7394">More...</a><br /></td></tr>
<tr class="separator:gae94950df74b0ce382b1fc1d978ef7394"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga74eadc8a211253079683219a3ea0462a"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga74eadc8a211253079683219a3ea0462a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#ga74eadc8a211253079683219a3ea0462a">two_over_pi</a> ()</td></tr>
<tr class="memdesc:ga74eadc8a211253079683219a3ea0462a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return 2 / pi.  <a href="a00290.html#ga74eadc8a211253079683219a3ea0462a">More...</a><br /></td></tr>
<tr class="separator:ga74eadc8a211253079683219a3ea0462a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5827301817640843cf02026a8d493894"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga5827301817640843cf02026a8d493894"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#ga5827301817640843cf02026a8d493894">two_over_root_pi</a> ()</td></tr>
<tr class="memdesc:ga5827301817640843cf02026a8d493894"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return 2 / sqrt(pi).  <a href="a00290.html#ga5827301817640843cf02026a8d493894">More...</a><br /></td></tr>
<tr class="separator:ga5827301817640843cf02026a8d493894"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa5276a4617566abcfe49286f40e3a256"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gaa5276a4617566abcfe49286f40e3a256"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#gaa5276a4617566abcfe49286f40e3a256">two_pi</a> ()</td></tr>
<tr class="memdesc:gaa5276a4617566abcfe49286f40e3a256"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return pi * 2.  <a href="a00290.html#gaa5276a4617566abcfe49286f40e3a256">More...</a><br /></td></tr>
<tr class="separator:gaa5276a4617566abcfe49286f40e3a256"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9b4d2f4322edcf63a6737b92a29dd1f5"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga9b4d2f4322edcf63a6737b92a29dd1f5"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#ga9b4d2f4322edcf63a6737b92a29dd1f5">two_thirds</a> ()</td></tr>
<tr class="memdesc:ga9b4d2f4322edcf63a6737b92a29dd1f5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return 2 / 3.  <a href="a00290.html#ga9b4d2f4322edcf63a6737b92a29dd1f5">More...</a><br /></td></tr>
<tr class="separator:ga9b4d2f4322edcf63a6737b92a29dd1f5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga788f5a421fc0f40a1296ebc094cbaa8a"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga788f5a421fc0f40a1296ebc094cbaa8a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00290.html#ga788f5a421fc0f40a1296ebc094cbaa8a">zero</a> ()</td></tr>
<tr class="memdesc:ga788f5a421fc0f40a1296ebc094cbaa8a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return 0.  <a href="a00290.html#ga788f5a421fc0f40a1296ebc094cbaa8a">More...</a><br /></td></tr>
<tr class="separator:ga788f5a421fc0f40a1296ebc094cbaa8a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00021.html" title="GLM_GTC_constants ">glm/gtc/constants.hpp</a>&gt; to use the features of this extension. </p>
<p>Provide a list of constants and precomputed useful values. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga4b7956eb6e2fbedfc7cf2e46e85c5139"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::e </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return e constant. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gad8fe2e6f90bce9d829e9723b649fbd42"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::euler </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return Euler's constant. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga753950e5140e4ea6a88e4a18ba61dc09"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::four_over_pi </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return 4 / pi. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga748cf8642830657c5b7eae04d0a80899"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::golden_ratio </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the golden ratio constant. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga0c36b41d462e45641faf7d7938948bac"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::half_pi </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return pi / 2. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaca94292c839ed31a405ab7a81ae7e850"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::ln_ln_two </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return ln(ln(2)). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaf97ebc6c059ffd788e6c4946f71ef66c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::ln_ten </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return ln(10). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga24f4d27765678116f41a2f336ab7975c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::ln_two </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return ln(2). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga39c2fb227631ca25894326529bdd1ee5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::one </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return 1. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga555150da2b06d23c8738981d5013e0eb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::one_over_pi </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return 1 / pi. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga788fa23a0939bac4d1d0205fb4f35818"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::one_over_root_two </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return 1 / sqrt(2). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga7c922b427986cbb2e4c6ac69874eefbc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::one_over_two_pi </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return 1 / (pi * 2). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga3c9df42bd73c519a995c43f0f99e77e0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::quarter_pi </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return pi / 4. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae9ebbded75b53d4faeb1e4ef8b3347a2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::root_five </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return sqrt(5). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga4e276cb823cc5e612d4f89ed99c75039"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::root_half_pi </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return sqrt(pi / 2). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga4129412e96b33707a77c1a07652e23e2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::root_ln_four </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return sqrt(ln(4)). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga261380796b2cd496f68d2cf1d08b8eb9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::root_pi </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return square root of pi. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga4f286be4abe88be1eed7d2a9f6cb193e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::root_three </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return sqrt(3). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga74e607d29020f100c0d0dc46ce2ca950"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::root_two </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return sqrt(2). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2bcedc575039fe0cd765742f8bbb0bd3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::root_two_pi </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return sqrt(2 * pi). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga3077c6311010a214b69ddc8214ec13b5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::third </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return 1 / 3. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae94950df74b0ce382b1fc1d978ef7394"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::three_over_two_pi </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return pi / 2 * 3. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga74eadc8a211253079683219a3ea0462a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::two_over_pi </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return 2 / pi. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5827301817640843cf02026a8d493894"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::two_over_root_pi </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return 2 / sqrt(pi). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaa5276a4617566abcfe49286f40e3a256"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::two_pi </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return pi * 2. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga9b4d2f4322edcf63a6737b92a29dd1f5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::two_thirds </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return 2 / 3. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga788f5a421fc0f40a1296ebc094cbaa8a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR genType glm::zero </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return 0. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00290.html" title="Include <glm/gtc/constants.hpp> to use the features of this extension. ">GLM_GTC_constants</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
