# GaVS Video Preprocessing Pipeline

This document provides comprehensive instructions for using the automated preprocessing pipeline to prepare custom videos for GaVS (3D-Grounded Video Stabilization).

## Overview

The `preprocess_video_for_gavs.py` script automates the entire preprocessing pipeline, converting a raw video file into the format required by GaVS training. It handles:

- Frame extraction from video files
- COLMAP camera pose estimation
- Directory structure setup
- Configuration file generation
- Placeholder mask generation
- Dataset validation

## Prerequisites

### Required Software

1. **FFmpeg** - For video processing
   ```bash
   # Ubuntu/Debian
   sudo apt update && sudo apt install ffmpeg
   
   # macOS (with Homebrew)
   brew install ffmpeg
   
   # Windows: Download from https://ffmpeg.org/download.html
   ```

2. **COLMAP** - For camera pose estimation
   ```bash
   # Ubuntu/Debian
   sudo apt install colmap
   
   # macOS (with Homebrew)
   brew install colmap
   
   # Windows: Download from https://colmap.github.io/install.html
   ```

### Required Python Packages

```bash
pip install opencv-python numpy pillow
```

### Optional Dependencies

For better mask generation (recommended):
```bash
# Grounded SAM 2 for dynamic object segmentation
git clone https://github.com/IDEA-Research/Grounded-SAM-2.git
cd Grounded-SAM-2
pip install -e .
```

## Usage

### Basic Usage

```bash
python preprocess_video_for_gavs.py --input video.mp4 --output ./dataset/my_video
```

### Advanced Usage

```bash
# Custom frame rate
python preprocess_video_for_gavs.py --input video.mp4 --output ./dataset/my_video --fps 25

# Generate GaVS config file
python preprocess_video_for_gavs.py --input video.mp4 --output ./dataset/my_video --create-config

# Verbose logging
python preprocess_video_for_gavs.py --input video.mp4 --output ./dataset/my_video --verbose
```

### Command Line Arguments

- `--input, -i`: Path to input video file (required)
- `--output, -o`: Output directory for processed dataset (required)
- `--fps`: Frame rate for extraction (default: 30.0)
- `--create-config`: Create a GaVS dataset configuration file
- `--verbose, -v`: Enable verbose logging

## Processing Pipeline

The script performs the following steps automatically:

### 1. Dependency Check
- Verifies FFmpeg and COLMAP are installed
- Checks for required Python packages

### 2. Frame Extraction
- Extracts frames at specified FPS using FFmpeg
- Saves frames as sequential PNG files (00001.png, 00002.png, etc.)

### 3. COLMAP Pipeline
- **Feature Extraction**: Detects SIFT features in all frames
- **Feature Matching**: Matches features between frames
- **Sparse Reconstruction**: Estimates camera poses and 3D structure
- **Format Conversion**: Converts to binary format for efficiency

### 4. Configuration Generation
- Parses COLMAP camera parameters and poses
- Creates `blender.json` with camera intrinsics and transformation matrices
- Converts COLMAP coordinate system to OpenGL/Blender format

### 5. Mask Generation
- Creates placeholder foreground masks (all white)
- Generates instructions for proper mask creation
- Sets up directory structure for optional inpainting

### 6. Validation
- Verifies all required files and directories exist
- Checks data consistency between components
- Validates configuration file format

## Output Structure

The script creates the following directory structure:

```
my_video/
├── blender.json              # Camera parameters and poses
├── images/                   # Extracted video frames
│   ├── 00001.png
│   ├── 00002.png
│   └── ...
├── fg_mask/                  # Foreground/dynamic object masks
│   ├── 00001.png
│   ├── 00002.png
│   └── ...
├── sparse/                   # COLMAP reconstruction
│   └── 0/
│       ├── cameras.bin
│       ├── images.bin
│       └── points3D.bin
├── inpainted/               # Optional: inpainted images
│   ├── images/
│   └── fg_mask/
├── colmap_mask/             # Optional: masks for COLMAP
├── rs_mask/                 # Optional: rolling shutter masks
├── database.db             # COLMAP feature database
└── MASK_GENERATION_INSTRUCTIONS.md
```

## Improving Results

### 1. Dynamic Object Masks

The script generates placeholder masks (all white). For better results:

1. **Install Grounded SAM 2** (recommended):
   ```bash
   git clone https://github.com/IDEA-Research/Grounded-SAM-2.git
   cd Grounded-SAM-2
   pip install -e .
   ```

2. **Generate proper masks** using text prompts:
   ```python
   # Example for mask generation
   from grounded_sam2 import GroundedSAM2
   
   model = GroundedSAM2()
   prompts = ["person", "car", "bicycle"]  # Adjust for your video
   
   for image_path in image_paths:
       masks = model.generate_masks(image_path, prompts)
       # Save to fg_mask/ directory
   ```

3. **Alternative approaches**:
   - Manual annotation using CVAT or LabelMe
   - YOLO + tracking for object detection
   - Segment Anything Model (SAM) for interactive segmentation

### 2. Video Inpainting (Optional)

For enhanced results, use ProPainter for video inpainting:

```bash
git clone https://github.com/sczhou/ProPainter.git
cd ProPainter
# Follow their installation and usage instructions
```

### 3. Rolling Shutter Compensation (Optional)

If your video has rolling shutter artifacts:
- Use gyroscope data if available
- Apply 2D homography correction
- Save compensation masks in `rs_mask/` directory

## Running GaVS Training

After preprocessing, train GaVS with your dataset:

```bash
# Basic training
python train.py \
    dataset.data_path=./dataset/my_video \
    hydra.run.dir=./exp/my_video \
    +experiment=layered_gavs_overfit

# Evaluation only
python evaluate.py \
    train.mode='eval' \
    hydra.run.dir=./exp/my_video \
    +experiment=layered_gavs_eval \
    dataset.data_path=./dataset/my_video \
    config.eval_dir='my_video_eval'
```

## Troubleshooting

### Common Issues

1. **COLMAP fails**: Ensure sufficient image overlap and texture
2. **No features detected**: Check image quality and lighting
3. **Memory issues**: Reduce video resolution or frame count
4. **GPU not detected**: Install CUDA drivers for GPU acceleration

### Tips for Better Results

1. **Video Quality**: Use high-quality, well-lit videos
2. **Camera Motion**: Moderate shake works best (not too stable/chaotic)
3. **Scene Content**: Include both static background and dynamic objects
4. **Frame Rate**: 30 FPS is recommended
5. **Duration**: 10-30 seconds works well for initial testing

## Logging

The script creates detailed logs in `gavs_preprocessing.log` for debugging and monitoring progress.

## Support

For issues with the preprocessing script, check:
1. Log files for detailed error messages
2. Dependency installation and versions
3. Input video format and quality
4. Available disk space and memory

For GaVS-specific issues, refer to the main project documentation and repository.
