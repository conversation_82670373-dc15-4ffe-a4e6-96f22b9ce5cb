<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: Exponential functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Exponential functions<div class="ingroups"><a class="el" href="a00280.html">Core features</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Provides GLSL exponential functions.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga071566cadc7505455e611f2a0353f4d4"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga071566cadc7505455e611f2a0353f4d4"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00242.html#ga071566cadc7505455e611f2a0353f4d4">exp</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga071566cadc7505455e611f2a0353f4d4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the natural exponentiation of v, i.e., e^v.  <a href="a00242.html#ga071566cadc7505455e611f2a0353f4d4">More...</a><br /></td></tr>
<tr class="separator:ga071566cadc7505455e611f2a0353f4d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaff17ace6b579a03bf223ed4d1ed2cd16"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaff17ace6b579a03bf223ed4d1ed2cd16"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00242.html#gaff17ace6b579a03bf223ed4d1ed2cd16">exp2</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gaff17ace6b579a03bf223ed4d1ed2cd16"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns 2 raised to the v power.  <a href="a00242.html#gaff17ace6b579a03bf223ed4d1ed2cd16">More...</a><br /></td></tr>
<tr class="separator:gaff17ace6b579a03bf223ed4d1ed2cd16"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga523dd6bd0ad9f75ae2d24c8e4b017b7a"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga523dd6bd0ad9f75ae2d24c8e4b017b7a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00242.html#ga523dd6bd0ad9f75ae2d24c8e4b017b7a">inversesqrt</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga523dd6bd0ad9f75ae2d24c8e4b017b7a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the reciprocal of the positive square root of v.  <a href="a00242.html#ga523dd6bd0ad9f75ae2d24c8e4b017b7a">More...</a><br /></td></tr>
<tr class="separator:ga523dd6bd0ad9f75ae2d24c8e4b017b7a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga918c9f3fd086ce20e6760c903bd30fa9"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga918c9f3fd086ce20e6760c903bd30fa9"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00242.html#ga918c9f3fd086ce20e6760c903bd30fa9">log</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga918c9f3fd086ce20e6760c903bd30fa9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the natural logarithm of v, i.e., returns the value y which satisfies the equation x = e^y.  <a href="a00242.html#ga918c9f3fd086ce20e6760c903bd30fa9">More...</a><br /></td></tr>
<tr class="separator:ga918c9f3fd086ce20e6760c903bd30fa9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga82831c7d9cca777cebedfe03a19c8d75"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga82831c7d9cca777cebedfe03a19c8d75"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00242.html#ga82831c7d9cca777cebedfe03a19c8d75">log2</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga82831c7d9cca777cebedfe03a19c8d75"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the base 2 log of x, i.e., returns the value y, which satisfies the equation x = 2 ^ y.  <a href="a00242.html#ga82831c7d9cca777cebedfe03a19c8d75">More...</a><br /></td></tr>
<tr class="separator:ga82831c7d9cca777cebedfe03a19c8d75"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2254981952d4f333b900a6bf5167a6c4"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga2254981952d4f333b900a6bf5167a6c4"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00242.html#ga2254981952d4f333b900a6bf5167a6c4">pow</a> (vec&lt; L, T, Q &gt; const &amp;base, vec&lt; L, T, Q &gt; const &amp;exponent)</td></tr>
<tr class="memdesc:ga2254981952d4f333b900a6bf5167a6c4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns 'base' raised to the power 'exponent'.  <a href="a00242.html#ga2254981952d4f333b900a6bf5167a6c4">More...</a><br /></td></tr>
<tr class="separator:ga2254981952d4f333b900a6bf5167a6c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa83e5f1648b7ccdf33b87c07c76cb77c"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaa83e5f1648b7ccdf33b87c07c76cb77c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00242.html#gaa83e5f1648b7ccdf33b87c07c76cb77c">sqrt</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gaa83e5f1648b7ccdf33b87c07c76cb77c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the positive square root of v.  <a href="a00242.html#gaa83e5f1648b7ccdf33b87c07c76cb77c">More...</a><br /></td></tr>
<tr class="separator:gaa83e5f1648b7ccdf33b87c07c76cb77c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Provides GLSL exponential functions. </p>
<p>These all operate component-wise. The description is per component.</p>
<p>Include &lt;<a class="el" href="a00026.html" title="Core features ">glm/exponential.hpp</a>&gt; to use these core features. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga071566cadc7505455e611f2a0353f4d4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::exp </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the natural exponentiation of x, i.e., e^x. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">v</td><td>exp function is defined for input values of v defined in the range (inf-, inf+) in the limit of the type qualifier. </td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>An integer between 1 and 4 included that qualify the dimension of the vector. </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/exp.xml">GLSL exp man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.2 Exponential Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaff17ace6b579a03bf223ed4d1ed2cd16"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::exp2 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns 2 raised to the v power. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">v</td><td>exp2 function is defined for input values of v defined in the range (inf-, inf+) in the limit of the type qualifier. </td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>An integer between 1 and 4 included that qualify the dimension of the vector. </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/exp2.xml">GLSL exp2 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.2 Exponential Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga523dd6bd0ad9f75ae2d24c8e4b017b7a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::inversesqrt </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the reciprocal of the positive square root of v. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">v</td><td>inversesqrt function is defined for input values of v defined in the range [0, inf+) in the limit of the type qualifier. </td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>An integer between 1 and 4 included that qualify the dimension of the vector. </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/inversesqrt.xml">GLSL inversesqrt man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.2 Exponential Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga918c9f3fd086ce20e6760c903bd30fa9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::log </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the natural logarithm of v, i.e., returns the value y which satisfies the equation x = e^y. </p>
<p>Results are undefined if v &lt;= 0.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">v</td><td>log function is defined for input values of v defined in the range (0, inf+) in the limit of the type qualifier. </td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>An integer between 1 and 4 included that qualify the dimension of the vector. </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/log.xml">GLSL log man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.2 Exponential Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga82831c7d9cca777cebedfe03a19c8d75"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::log2 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the base 2 log of x, i.e., returns the value y, which satisfies the equation x = 2 ^ y. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">v</td><td>log2 function is defined for input values of v defined in the range (0, inf+) in the limit of the type qualifier. </td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>An integer between 1 and 4 included that qualify the dimension of the vector. </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/log2.xml">GLSL log2 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.2 Exponential Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2254981952d4f333b900a6bf5167a6c4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::pow </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>base</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>exponent</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns 'base' raised to the power 'exponent'. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">base</td><td>Floating point value. pow function is defined for input values of 'base' defined in the range (inf-, inf+) in the limit of the type qualifier. </td></tr>
    <tr><td class="paramname">exponent</td><td>Floating point value representing the 'exponent'.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/pow.xml">GLSL pow man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.2 Exponential Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaa83e5f1648b7ccdf33b87c07c76cb77c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::sqrt </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the positive square root of v. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">v</td><td>sqrt function is defined for input values of v defined in the range [0, inf+) in the limit of the type qualifier. </td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>An integer between 1 and 4 included that qualify the dimension of the vector. </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/sqrt.xml">GLSL sqrt man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.2 Exponential Functions</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
