<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: setup.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_033f5edb0915b828d2c46ed4804e5503.html">detail</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">setup.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="preprocessor">#ifndef GLM_SETUP_INCLUDED</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;</div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="preprocessor">#include &lt;cassert&gt;</span></div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="preprocessor">#include &lt;cstddef&gt;</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;</div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="preprocessor">#define GLM_VERSION_MAJOR                       0</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="preprocessor">#define GLM_VERSION_MINOR                       9</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="preprocessor">#define GLM_VERSION_PATCH                       9</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="preprocessor">#define GLM_VERSION_REVISION            6</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="preprocessor">#define GLM_VERSION                                     996</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="preprocessor">#define GLM_VERSION_MESSAGE                     &quot;GLM: version 0.9.9.6&quot;</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;</div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#define GLM_SETUP_INCLUDED                      GLM_VERSION</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;</div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment">// Active states</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;</div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#define GLM_DISABLE             0</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#define GLM_ENABLE              1</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;</div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment">// Messages</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;</div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#if defined(GLM_FORCE_MESSAGES)</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#       define GLM_MESSAGES GLM_ENABLE</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#       define GLM_MESSAGES GLM_DISABLE</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="comment">// Detect the platform</span></div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#include &quot;../simd/platform.h&quot;</span></div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="comment">// Build model</span></div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="preprocessor">#if defined(__arch64__) || defined(__LP64__) || defined(_M_X64) || defined(__ppc64__) || defined(__x86_64__)</span></div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#       define GLM_MODEL        GLM_MODEL_64</span></div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#elif defined(__i386__) || defined(__ppc__)</span></div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="preprocessor">#       define GLM_MODEL        GLM_MODEL_32</span></div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="preprocessor">#       define GLM_MODEL        GLM_MODEL_32</span></div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="preprocessor">#endif//</span></div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="preprocessor">#if !defined(GLM_MODEL) &amp;&amp; GLM_COMPILER != 0</span></div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="preprocessor">#       error &quot;GLM_MODEL undefined, your compiler may not be supported by GLM. Add #define GLM_MODEL 0 to ignore this message.&quot;</span></div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="preprocessor">#endif//GLM_MODEL</span></div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;<span class="comment">// C++ Version</span></div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;<span class="comment">// User defines: GLM_FORCE_CXX98, GLM_FORCE_CXX03, GLM_FORCE_CXX11, GLM_FORCE_CXX14, GLM_FORCE_CXX17, GLM_FORCE_CXX2A</span></div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;<span class="preprocessor">#define GLM_LANG_CXX98_FLAG                     (1 &lt;&lt; 1)</span></div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;<span class="preprocessor">#define GLM_LANG_CXX03_FLAG                     (1 &lt;&lt; 2)</span></div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;<span class="preprocessor">#define GLM_LANG_CXX0X_FLAG                     (1 &lt;&lt; 3)</span></div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;<span class="preprocessor">#define GLM_LANG_CXX11_FLAG                     (1 &lt;&lt; 4)</span></div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;<span class="preprocessor">#define GLM_LANG_CXX14_FLAG                     (1 &lt;&lt; 5)</span></div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;<span class="preprocessor">#define GLM_LANG_CXX17_FLAG                     (1 &lt;&lt; 6)</span></div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;<span class="preprocessor">#define GLM_LANG_CXX2A_FLAG                     (1 &lt;&lt; 7)</span></div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;<span class="preprocessor">#define GLM_LANG_CXXMS_FLAG                     (1 &lt;&lt; 8)</span></div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;<span class="preprocessor">#define GLM_LANG_CXXGNU_FLAG            (1 &lt;&lt; 9)</span></div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;<span class="preprocessor">#define GLM_LANG_CXX98                  GLM_LANG_CXX98_FLAG</span></div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;<span class="preprocessor">#define GLM_LANG_CXX03                  (GLM_LANG_CXX98 | GLM_LANG_CXX03_FLAG)</span></div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;<span class="preprocessor">#define GLM_LANG_CXX0X                  (GLM_LANG_CXX03 | GLM_LANG_CXX0X_FLAG)</span></div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;<span class="preprocessor">#define GLM_LANG_CXX11                  (GLM_LANG_CXX0X | GLM_LANG_CXX11_FLAG)</span></div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;<span class="preprocessor">#define GLM_LANG_CXX14                  (GLM_LANG_CXX11 | GLM_LANG_CXX14_FLAG)</span></div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;<span class="preprocessor">#define GLM_LANG_CXX17                  (GLM_LANG_CXX14 | GLM_LANG_CXX17_FLAG)</span></div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;<span class="preprocessor">#define GLM_LANG_CXX2A                  (GLM_LANG_CXX17 | GLM_LANG_CXX2A_FLAG)</span></div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;<span class="preprocessor">#define GLM_LANG_CXXMS                  GLM_LANG_CXXMS_FLAG</span></div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;<span class="preprocessor">#define GLM_LANG_CXXGNU                 GLM_LANG_CXXGNU_FLAG</span></div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;<span class="preprocessor">#if (defined(_MSC_EXTENSIONS))</span></div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;<span class="preprocessor">#       define GLM_LANG_EXT GLM_LANG_CXXMS_FLAG</span></div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;<span class="preprocessor">#elif ((GLM_COMPILER &amp; (GLM_COMPILER_CLANG | GLM_COMPILER_GCC)) &amp;&amp; (GLM_ARCH &amp; GLM_ARCH_SIMD_BIT))</span></div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;<span class="preprocessor">#       define GLM_LANG_EXT GLM_LANG_CXXMS_FLAG</span></div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;<span class="preprocessor">#       define GLM_LANG_EXT 0</span></div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;<span class="preprocessor">#if (defined(GLM_FORCE_CXX_UNKNOWN))</span></div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;<span class="preprocessor">#       define GLM_LANG 0</span></div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;<span class="preprocessor">#elif defined(GLM_FORCE_CXX2A)</span></div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;<span class="preprocessor">#       define GLM_LANG (GLM_LANG_CXX2A | GLM_LANG_EXT)</span></div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;<span class="preprocessor">#       define GLM_LANG_STL11_FORCED</span></div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;<span class="preprocessor">#elif defined(GLM_FORCE_CXX17)</span></div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;<span class="preprocessor">#       define GLM_LANG (GLM_LANG_CXX17 | GLM_LANG_EXT)</span></div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;<span class="preprocessor">#       define GLM_LANG_STL11_FORCED</span></div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;<span class="preprocessor">#elif defined(GLM_FORCE_CXX14)</span></div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;<span class="preprocessor">#       define GLM_LANG (GLM_LANG_CXX14 | GLM_LANG_EXT)</span></div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;<span class="preprocessor">#       define GLM_LANG_STL11_FORCED</span></div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;<span class="preprocessor">#elif defined(GLM_FORCE_CXX11)</span></div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;<span class="preprocessor">#       define GLM_LANG (GLM_LANG_CXX11 | GLM_LANG_EXT)</span></div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;<span class="preprocessor">#       define GLM_LANG_STL11_FORCED</span></div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;<span class="preprocessor">#elif defined(GLM_FORCE_CXX03)</span></div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;<span class="preprocessor">#       define GLM_LANG (GLM_LANG_CXX03 | GLM_LANG_EXT)</span></div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;<span class="preprocessor">#elif defined(GLM_FORCE_CXX98)</span></div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;<span class="preprocessor">#       define GLM_LANG (GLM_LANG_CXX98 | GLM_LANG_EXT)</span></div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;<span class="preprocessor">#       if GLM_COMPILER &amp; GLM_COMPILER_VC &amp;&amp; defined(_MSVC_LANG)</span></div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;<span class="preprocessor">#               if GLM_COMPILER &gt;= GLM_COMPILER_VC15_7</span></div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;<span class="preprocessor">#                       define GLM_LANG_PLATFORM _MSVC_LANG</span></div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;<span class="preprocessor">#               elif GLM_COMPILER &gt;= GLM_COMPILER_VC15</span></div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;<span class="preprocessor">#                       if _MSVC_LANG &gt; 201402L</span></div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;<span class="preprocessor">#                               define GLM_LANG_PLATFORM 201402L</span></div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;<span class="preprocessor">#                       else</span></div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;<span class="preprocessor">#                               define GLM_LANG_PLATFORM _MSVC_LANG</span></div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;<span class="preprocessor">#                       endif</span></div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;<span class="preprocessor">#               else</span></div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;<span class="preprocessor">#                       define GLM_LANG_PLATFORM 0</span></div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;<span class="preprocessor">#               endif</span></div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;<span class="preprocessor">#               define GLM_LANG_PLATFORM 0</span></div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;<span class="preprocessor">#       if __cplusplus &gt; 201703L || GLM_LANG_PLATFORM &gt; 201703L</span></div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;<span class="preprocessor">#               define GLM_LANG (GLM_LANG_CXX2A | GLM_LANG_EXT)</span></div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;<span class="preprocessor">#       elif __cplusplus == 201703L || GLM_LANG_PLATFORM == 201703L</span></div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;<span class="preprocessor">#               define GLM_LANG (GLM_LANG_CXX17 | GLM_LANG_EXT)</span></div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;<span class="preprocessor">#       elif __cplusplus == 201402L || __cplusplus == 201500L || GLM_LANG_PLATFORM == 201402L</span></div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;<span class="preprocessor">#               define GLM_LANG (GLM_LANG_CXX14 | GLM_LANG_EXT)</span></div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;<span class="preprocessor">#       elif __cplusplus == 201103L || GLM_LANG_PLATFORM == 201103L</span></div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;<span class="preprocessor">#               define GLM_LANG (GLM_LANG_CXX11 | GLM_LANG_EXT)</span></div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;<span class="preprocessor">#       elif defined(__INTEL_CXX11_MODE__) || defined(_MSC_VER) || defined(__GXX_EXPERIMENTAL_CXX0X__)</span></div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;<span class="preprocessor">#               define GLM_LANG (GLM_LANG_CXX0X | GLM_LANG_EXT)</span></div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;<span class="preprocessor">#       elif __cplusplus == 199711L</span></div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;<span class="preprocessor">#               define GLM_LANG (GLM_LANG_CXX98 | GLM_LANG_EXT)</span></div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;<span class="preprocessor">#               define GLM_LANG (0 | GLM_LANG_EXT)</span></div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;<span class="comment">// Has of C++ features</span></div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;<span class="comment">// http://clang.llvm.org/cxx_status.html</span></div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;<span class="comment">// http://gcc.gnu.org/projects/cxx0x.html</span></div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;<span class="comment">// http://msdn.microsoft.com/en-us/library/vstudio/hh567368(v=vs.120).aspx</span></div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;<span class="comment">// Android has multiple STLs but C++11 STL detection doesn&#39;t always work #284 #564</span></div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;<span class="preprocessor">#if GLM_PLATFORM == GLM_PLATFORM_ANDROID &amp;&amp; !defined(GLM_LANG_STL11_FORCED)</span></div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;<span class="preprocessor">#       define GLM_HAS_CXX11_STL 0</span></div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;<span class="preprocessor">#elif GLM_COMPILER &amp; GLM_COMPILER_CLANG</span></div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;<span class="preprocessor">#       if (defined(_LIBCPP_VERSION) || (GLM_LANG &amp; GLM_LANG_CXX11_FLAG) || defined(GLM_LANG_STL11_FORCED))</span></div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;<span class="preprocessor">#               define GLM_HAS_CXX11_STL 1</span></div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;<span class="preprocessor">#               define GLM_HAS_CXX11_STL 0</span></div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;<span class="preprocessor">#elif GLM_LANG &amp; GLM_LANG_CXX11_FLAG</span></div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;<span class="preprocessor">#       define GLM_HAS_CXX11_STL 1</span></div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;<span class="preprocessor">#       define GLM_HAS_CXX11_STL ((GLM_LANG &amp; GLM_LANG_CXX0X_FLAG) &amp;&amp; (\</span></div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_GCC) &amp;&amp; (GLM_COMPILER &gt;= GLM_COMPILER_GCC48)) || \</span></div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_VC) &amp;&amp; (GLM_COMPILER &gt;= GLM_COMPILER_VC12)) || \</span></div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;<span class="preprocessor">                ((GLM_PLATFORM != GLM_PLATFORM_WINDOWS) &amp;&amp; (GLM_COMPILER &amp; GLM_COMPILER_INTEL) &amp;&amp; (GLM_COMPILER &gt;= GLM_COMPILER_INTEL15))))</span></div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;<span class="comment">// N1720</span></div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;<span class="preprocessor">#if GLM_COMPILER &amp; GLM_COMPILER_CLANG</span></div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;<span class="preprocessor">#       define GLM_HAS_STATIC_ASSERT __has_feature(cxx_static_assert)</span></div>
<div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;<span class="preprocessor">#elif GLM_LANG &amp; GLM_LANG_CXX11_FLAG</span></div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;<span class="preprocessor">#       define GLM_HAS_STATIC_ASSERT 1</span></div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;<span class="preprocessor">#       define GLM_HAS_STATIC_ASSERT ((GLM_LANG &amp; GLM_LANG_CXX0X_FLAG) &amp;&amp; (\</span></div>
<div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_CUDA)) || \</span></div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_VC))))</span></div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;<span class="comment">// N1988</span></div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;<span class="preprocessor">#if GLM_LANG &amp; GLM_LANG_CXX11_FLAG</span></div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;<span class="preprocessor">#       define GLM_HAS_EXTENDED_INTEGER_TYPE 1</span></div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;<span class="preprocessor">#       define GLM_HAS_EXTENDED_INTEGER_TYPE (\</span></div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;<span class="preprocessor">                ((GLM_LANG &amp; GLM_LANG_CXX0X_FLAG) &amp;&amp; (GLM_COMPILER &amp; GLM_COMPILER_VC)) || \</span></div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;<span class="preprocessor">                ((GLM_LANG &amp; GLM_LANG_CXX0X_FLAG) &amp;&amp; (GLM_COMPILER &amp; GLM_COMPILER_CUDA)) || \</span></div>
<div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;<span class="preprocessor">                ((GLM_LANG &amp; GLM_LANG_CXX0X_FLAG) &amp;&amp; (GLM_COMPILER &amp; GLM_COMPILER_CLANG)))</span></div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;</div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;<span class="comment">// N2672 Initializer lists http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2008/n2672.htm</span></div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;<span class="preprocessor">#if GLM_COMPILER &amp; GLM_COMPILER_CLANG</span></div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;<span class="preprocessor">#       define GLM_HAS_INITIALIZER_LISTS __has_feature(cxx_generalized_initializers)</span></div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;<span class="preprocessor">#elif GLM_LANG &amp; GLM_LANG_CXX11_FLAG</span></div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;<span class="preprocessor">#       define GLM_HAS_INITIALIZER_LISTS 1</span></div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;<span class="preprocessor">#       define GLM_HAS_INITIALIZER_LISTS ((GLM_LANG &amp; GLM_LANG_CXX0X_FLAG) &amp;&amp; (\</span></div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_VC) &amp;&amp; (GLM_COMPILER &gt;= GLM_COMPILER_VC15)) || \</span></div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_INTEL) &amp;&amp; (GLM_COMPILER &gt;= GLM_COMPILER_INTEL14)) || \</span></div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_CUDA))))</span></div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;<span class="comment">// N2544 Unrestricted unions http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2008/n2544.pdf</span></div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;<span class="preprocessor">#if GLM_COMPILER &amp; GLM_COMPILER_CLANG</span></div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;<span class="preprocessor">#       define GLM_HAS_UNRESTRICTED_UNIONS __has_feature(cxx_unrestricted_unions)</span></div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;<span class="preprocessor">#elif GLM_LANG &amp; GLM_LANG_CXX11_FLAG</span></div>
<div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;<span class="preprocessor">#       define GLM_HAS_UNRESTRICTED_UNIONS 1</span></div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;<span class="preprocessor">#       define GLM_HAS_UNRESTRICTED_UNIONS (GLM_LANG &amp; GLM_LANG_CXX0X_FLAG) &amp;&amp; (\</span></div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;<span class="preprocessor">                (GLM_COMPILER &amp; GLM_COMPILER_VC) || \</span></div>
<div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_CUDA)))</span></div>
<div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;</div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;<span class="comment">// N2346</span></div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;<span class="preprocessor">#if GLM_COMPILER &amp; GLM_COMPILER_CLANG</span></div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;<span class="preprocessor">#       define GLM_HAS_DEFAULTED_FUNCTIONS __has_feature(cxx_defaulted_functions)</span></div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;<span class="preprocessor">#elif GLM_LANG &amp; GLM_LANG_CXX11_FLAG</span></div>
<div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;<span class="preprocessor">#       define GLM_HAS_DEFAULTED_FUNCTIONS 1</span></div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;<span class="preprocessor">#       define GLM_HAS_DEFAULTED_FUNCTIONS ((GLM_LANG &amp; GLM_LANG_CXX0X_FLAG) &amp;&amp; (\</span></div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_VC) &amp;&amp; (GLM_COMPILER &gt;= GLM_COMPILER_VC12)) || \</span></div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_INTEL)) || \</span></div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;<span class="preprocessor">                (GLM_COMPILER &amp; GLM_COMPILER_CUDA)))</span></div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;</div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;<span class="comment">// N2118</span></div>
<div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;<span class="preprocessor">#if GLM_COMPILER &amp; GLM_COMPILER_CLANG</span></div>
<div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;<span class="preprocessor">#       define GLM_HAS_RVALUE_REFERENCES __has_feature(cxx_rvalue_references)</span></div>
<div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;<span class="preprocessor">#elif GLM_LANG &amp; GLM_LANG_CXX11_FLAG</span></div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;<span class="preprocessor">#       define GLM_HAS_RVALUE_REFERENCES 1</span></div>
<div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;<span class="preprocessor">#       define GLM_HAS_RVALUE_REFERENCES ((GLM_LANG &amp; GLM_LANG_CXX0X_FLAG) &amp;&amp; (\</span></div>
<div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_VC)) || \</span></div>
<div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_CUDA))))</span></div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;</div>
<div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;<span class="comment">// N2437 http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2007/n2437.pdf</span></div>
<div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;<span class="preprocessor">#if GLM_COMPILER &amp; GLM_COMPILER_CLANG</span></div>
<div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;<span class="preprocessor">#       define GLM_HAS_EXPLICIT_CONVERSION_OPERATORS __has_feature(cxx_explicit_conversions)</span></div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;<span class="preprocessor">#elif GLM_LANG &amp; GLM_LANG_CXX11_FLAG</span></div>
<div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;<span class="preprocessor">#       define GLM_HAS_EXPLICIT_CONVERSION_OPERATORS 1</span></div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;<span class="preprocessor">#       define GLM_HAS_EXPLICIT_CONVERSION_OPERATORS ((GLM_LANG &amp; GLM_LANG_CXX0X_FLAG) &amp;&amp; (\</span></div>
<div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_INTEL) &amp;&amp; (GLM_COMPILER &gt;= GLM_COMPILER_INTEL14)) || \</span></div>
<div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_VC) &amp;&amp; (GLM_COMPILER &gt;= GLM_COMPILER_VC12)) || \</span></div>
<div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_CUDA))))</span></div>
<div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;</div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;<span class="comment">// N2258 http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2007/n2258.pdf</span></div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;<span class="preprocessor">#if GLM_COMPILER &amp; GLM_COMPILER_CLANG</span></div>
<div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;<span class="preprocessor">#       define GLM_HAS_TEMPLATE_ALIASES __has_feature(cxx_alias_templates)</span></div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;<span class="preprocessor">#elif GLM_LANG &amp; GLM_LANG_CXX11_FLAG</span></div>
<div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;<span class="preprocessor">#       define GLM_HAS_TEMPLATE_ALIASES 1</span></div>
<div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;<span class="preprocessor">#       define GLM_HAS_TEMPLATE_ALIASES ((GLM_LANG &amp; GLM_LANG_CXX0X_FLAG) &amp;&amp; (\</span></div>
<div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_INTEL)) || \</span></div>
<div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_VC) &amp;&amp; (GLM_COMPILER &gt;= GLM_COMPILER_VC12)) || \</span></div>
<div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_CUDA))))</span></div>
<div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;</div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;<span class="comment">// N2930 http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2009/n2930.html</span></div>
<div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;<span class="preprocessor">#if GLM_COMPILER &amp; GLM_COMPILER_CLANG</span></div>
<div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;<span class="preprocessor">#       define GLM_HAS_RANGE_FOR __has_feature(cxx_range_for)</span></div>
<div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;<span class="preprocessor">#elif GLM_LANG &amp; GLM_LANG_CXX11_FLAG</span></div>
<div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;<span class="preprocessor">#       define GLM_HAS_RANGE_FOR 1</span></div>
<div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;<span class="preprocessor">#       define GLM_HAS_RANGE_FOR ((GLM_LANG &amp; GLM_LANG_CXX0X_FLAG) &amp;&amp; (\</span></div>
<div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_INTEL)) || \</span></div>
<div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_VC)) || \</span></div>
<div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_CUDA))))</span></div>
<div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;</div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;<span class="comment">// N2341 http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2007/n2341.pdf</span></div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;<span class="preprocessor">#if GLM_COMPILER &amp; GLM_COMPILER_CLANG</span></div>
<div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;<span class="preprocessor">#       define GLM_HAS_ALIGNOF __has_feature(cxx_alignas)</span></div>
<div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;<span class="preprocessor">#elif GLM_LANG &amp; GLM_LANG_CXX11_FLAG</span></div>
<div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;<span class="preprocessor">#       define GLM_HAS_ALIGNOF 1</span></div>
<div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;<span class="preprocessor">#       define GLM_HAS_ALIGNOF ((GLM_LANG &amp; GLM_LANG_CXX0X_FLAG) &amp;&amp; (\</span></div>
<div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_INTEL) &amp;&amp; (GLM_COMPILER &gt;= GLM_COMPILER_INTEL15)) || \</span></div>
<div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_VC) &amp;&amp; (GLM_COMPILER &gt;= GLM_COMPILER_VC14)) || \</span></div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_CUDA))))</span></div>
<div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;</div>
<div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;<span class="comment">// N2235 Generalized Constant Expressions http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2007/n2235.pdf</span></div>
<div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;<span class="comment">// N3652 Extended Constant Expressions http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2013/n3652.html</span></div>
<div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;<span class="preprocessor">#if (GLM_ARCH &amp; GLM_ARCH_SIMD_BIT) // Compiler SIMD intrinsics don&#39;t support constexpr...</span></div>
<div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;<span class="preprocessor">#       define GLM_HAS_CONSTEXPR 0</span></div>
<div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;<span class="preprocessor">#elif (GLM_COMPILER &amp; GLM_COMPILER_CLANG)</span></div>
<div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;<span class="preprocessor">#       define GLM_HAS_CONSTEXPR __has_feature(cxx_relaxed_constexpr)</span></div>
<div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;<span class="preprocessor">#elif (GLM_LANG &amp; GLM_LANG_CXX14_FLAG)</span></div>
<div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;<span class="preprocessor">#       define GLM_HAS_CONSTEXPR 1</span></div>
<div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;<span class="preprocessor">#       define GLM_HAS_CONSTEXPR ((GLM_LANG &amp; GLM_LANG_CXX0X_FLAG) &amp;&amp; GLM_HAS_INITIALIZER_LISTS &amp;&amp; (\</span></div>
<div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_INTEL) &amp;&amp; (GLM_COMPILER &gt;= GLM_COMPILER_INTEL17)) || \</span></div>
<div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_VC) &amp;&amp; (GLM_COMPILER &gt;= GLM_COMPILER_VC15))))</span></div>
<div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;</div>
<div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;<span class="preprocessor">#if GLM_HAS_CONSTEXPR</span></div>
<div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;<span class="preprocessor">#       define GLM_CONSTEXPR constexpr</span></div>
<div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;<span class="preprocessor">#       define GLM_CONSTEXPR</span></div>
<div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;</div>
<div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;<span class="preprocessor">#if GLM_HAS_CONSTEXPR</span></div>
<div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;<span class="preprocessor"># if (GLM_COMPILER &amp; GLM_COMPILER_CLANG)</span></div>
<div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;<span class="preprocessor">#       if __has_feature(cxx_if_constexpr)</span></div>
<div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;<span class="preprocessor">#               define GLM_HAS_IF_CONSTEXPR 1</span></div>
<div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;<span class="preprocessor">#               define GLM_HAS_IF_CONSTEXPR 0</span></div>
<div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;<span class="preprocessor"># elif (GLM_LANG &amp; GLM_LANG_CXX17_FLAG)</span></div>
<div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;<span class="preprocessor">#       define GLM_HAS_IF_CONSTEXPR 1</span></div>
<div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;<span class="preprocessor"># else</span></div>
<div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;<span class="preprocessor">#       define GLM_HAS_IF_CONSTEXPR 0</span></div>
<div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;<span class="preprocessor"># endif</span></div>
<div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;<span class="preprocessor">#       define GLM_HAS_IF_CONSTEXPR 0</span></div>
<div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;</div>
<div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;<span class="preprocessor">#if GLM_HAS_IF_CONSTEXPR</span></div>
<div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160;<span class="preprocessor">#       define GLM_IF_CONSTEXPR if constexpr</span></div>
<div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;<span class="preprocessor">#       define GLM_IF_CONSTEXPR if</span></div>
<div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;</div>
<div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;<span class="preprocessor">#if GLM_LANG &amp; GLM_LANG_CXX11_FLAG</span></div>
<div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;<span class="preprocessor">#       define GLM_HAS_ASSIGNABLE 1</span></div>
<div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;<span class="preprocessor">#       define GLM_HAS_ASSIGNABLE ((GLM_LANG &amp; GLM_LANG_CXX0X_FLAG) &amp;&amp; (\</span></div>
<div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_VC) &amp;&amp; (GLM_COMPILER &gt;= GLM_COMPILER_VC15)) || \</span></div>
<div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_GCC) &amp;&amp; (GLM_COMPILER &gt;= GLM_COMPILER_GCC49))))</span></div>
<div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;</div>
<div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;<span class="preprocessor">#define GLM_HAS_TRIVIAL_QUERIES 0</span></div>
<div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160;</div>
<div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;<span class="preprocessor">#if GLM_LANG &amp; GLM_LANG_CXX11_FLAG</span></div>
<div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;<span class="preprocessor">#       define GLM_HAS_MAKE_SIGNED 1</span></div>
<div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;<span class="preprocessor">#       define GLM_HAS_MAKE_SIGNED ((GLM_LANG &amp; GLM_LANG_CXX0X_FLAG) &amp;&amp; (\</span></div>
<div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_VC) &amp;&amp; (GLM_COMPILER &gt;= GLM_COMPILER_VC12)) || \</span></div>
<div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_CUDA))))</span></div>
<div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;</div>
<div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;<span class="preprocessor">#if defined(GLM_FORCE_INTRINSICS)</span></div>
<div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;<span class="preprocessor">#       define GLM_HAS_BITSCAN_WINDOWS ((GLM_PLATFORM &amp; GLM_PLATFORM_WINDOWS) &amp;&amp; (\</span></div>
<div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_INTEL)) || \</span></div>
<div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;<span class="preprocessor">                ((GLM_COMPILER &amp; GLM_COMPILER_VC) &amp;&amp; (GLM_COMPILER &gt;= GLM_COMPILER_VC14) &amp;&amp; (GLM_ARCH &amp; GLM_ARCH_X86_BIT))))</span></div>
<div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;<span class="preprocessor">#       define GLM_HAS_BITSCAN_WINDOWS 0</span></div>
<div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;</div>
<div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;<span class="comment">// OpenMP</span></div>
<div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160;<span class="preprocessor">#ifdef _OPENMP</span></div>
<div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;<span class="preprocessor">#       if GLM_COMPILER &amp; GLM_COMPILER_GCC</span></div>
<div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;<span class="preprocessor">#               if GLM_COMPILER &gt;= GLM_COMPILER_GCC61</span></div>
<div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;<span class="preprocessor">#                       define GLM_HAS_OPENMP 45</span></div>
<div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;<span class="preprocessor">#               elif GLM_COMPILER &gt;= GLM_COMPILER_GCC49</span></div>
<div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;<span class="preprocessor">#                       define GLM_HAS_OPENMP 40</span></div>
<div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;<span class="preprocessor">#               elif GLM_COMPILER &gt;= GLM_COMPILER_GCC47</span></div>
<div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;<span class="preprocessor">#                       define GLM_HAS_OPENMP 31</span></div>
<div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;<span class="preprocessor">#               else</span></div>
<div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;<span class="preprocessor">#                       define GLM_HAS_OPENMP 0</span></div>
<div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;<span class="preprocessor">#               endif</span></div>
<div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;<span class="preprocessor">#       elif GLM_COMPILER &amp; GLM_COMPILER_CLANG</span></div>
<div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;<span class="preprocessor">#               if GLM_COMPILER &gt;= GLM_COMPILER_CLANG38</span></div>
<div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160;<span class="preprocessor">#                       define GLM_HAS_OPENMP 31</span></div>
<div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;<span class="preprocessor">#               else</span></div>
<div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;<span class="preprocessor">#                       define GLM_HAS_OPENMP 0</span></div>
<div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;<span class="preprocessor">#               endif</span></div>
<div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;<span class="preprocessor">#       elif GLM_COMPILER &amp; GLM_COMPILER_VC</span></div>
<div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;<span class="preprocessor">#               define GLM_HAS_OPENMP 20</span></div>
<div class="line"><a name="l00369"></a><span class="lineno">  369</span>&#160;<span class="preprocessor">#       elif GLM_COMPILER &amp; GLM_COMPILER_INTEL</span></div>
<div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;<span class="preprocessor">#               if GLM_COMPILER &gt;= GLM_COMPILER_INTEL16</span></div>
<div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;<span class="preprocessor">#                       define GLM_HAS_OPENMP 40</span></div>
<div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;<span class="preprocessor">#               else</span></div>
<div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;<span class="preprocessor">#                       define GLM_HAS_OPENMP 0</span></div>
<div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160;<span class="preprocessor">#               endif</span></div>
<div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00376"></a><span class="lineno">  376</span>&#160;<span class="preprocessor">#               define GLM_HAS_OPENMP 0</span></div>
<div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160;<span class="preprocessor">#       define GLM_HAS_OPENMP 0</span></div>
<div class="line"><a name="l00380"></a><span class="lineno">  380</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00381"></a><span class="lineno">  381</span>&#160;</div>
<div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160;<span class="comment">// nullptr</span></div>
<div class="line"><a name="l00384"></a><span class="lineno">  384</span>&#160;</div>
<div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160;<span class="preprocessor">#if GLM_LANG &amp; GLM_LANG_CXX0X_FLAG</span></div>
<div class="line"><a name="l00386"></a><span class="lineno">  386</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_NULLPTR GLM_ENABLE</span></div>
<div class="line"><a name="l00387"></a><span class="lineno">  387</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00388"></a><span class="lineno">  388</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_NULLPTR GLM_DISABLE</span></div>
<div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160;</div>
<div class="line"><a name="l00391"></a><span class="lineno">  391</span>&#160;<span class="preprocessor">#if GLM_CONFIG_NULLPTR == GLM_ENABLE</span></div>
<div class="line"><a name="l00392"></a><span class="lineno">  392</span>&#160;<span class="preprocessor">#       define GLM_NULLPTR nullptr</span></div>
<div class="line"><a name="l00393"></a><span class="lineno">  393</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00394"></a><span class="lineno">  394</span>&#160;<span class="preprocessor">#       define GLM_NULLPTR 0</span></div>
<div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00396"></a><span class="lineno">  396</span>&#160;</div>
<div class="line"><a name="l00398"></a><span class="lineno">  398</span>&#160;<span class="comment">// Static assert</span></div>
<div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160;</div>
<div class="line"><a name="l00400"></a><span class="lineno">  400</span>&#160;<span class="preprocessor">#if GLM_HAS_STATIC_ASSERT</span></div>
<div class="line"><a name="l00401"></a><span class="lineno">  401</span>&#160;<span class="preprocessor">#       define GLM_STATIC_ASSERT(x, message) static_assert(x, message)</span></div>
<div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;<span class="preprocessor">#elif GLM_COMPILER &amp; GLM_COMPILER_VC</span></div>
<div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160;<span class="preprocessor">#       define GLM_STATIC_ASSERT(x, message) typedef char __CASSERT__##__LINE__[(x) ? 1 : -1]</span></div>
<div class="line"><a name="l00404"></a><span class="lineno">  404</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00405"></a><span class="lineno">  405</span>&#160;<span class="preprocessor">#       define GLM_STATIC_ASSERT(x, message) assert(x)</span></div>
<div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160;<span class="preprocessor">#endif//GLM_LANG</span></div>
<div class="line"><a name="l00407"></a><span class="lineno">  407</span>&#160;</div>
<div class="line"><a name="l00409"></a><span class="lineno">  409</span>&#160;<span class="comment">// Qualifiers</span></div>
<div class="line"><a name="l00410"></a><span class="lineno">  410</span>&#160;</div>
<div class="line"><a name="l00411"></a><span class="lineno">  411</span>&#160;<span class="preprocessor">#if GLM_COMPILER &amp; GLM_COMPILER_CUDA</span></div>
<div class="line"><a name="l00412"></a><span class="lineno">  412</span>&#160;<span class="preprocessor">#       define GLM_CUDA_FUNC_DEF __device__ __host__</span></div>
<div class="line"><a name="l00413"></a><span class="lineno">  413</span>&#160;<span class="preprocessor">#       define GLM_CUDA_FUNC_DECL __device__ __host__</span></div>
<div class="line"><a name="l00414"></a><span class="lineno">  414</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;<span class="preprocessor">#       define GLM_CUDA_FUNC_DEF</span></div>
<div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160;<span class="preprocessor">#       define GLM_CUDA_FUNC_DECL</span></div>
<div class="line"><a name="l00417"></a><span class="lineno">  417</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00418"></a><span class="lineno">  418</span>&#160;</div>
<div class="line"><a name="l00419"></a><span class="lineno">  419</span>&#160;<span class="preprocessor">#if defined(GLM_FORCE_INLINE)</span></div>
<div class="line"><a name="l00420"></a><span class="lineno">  420</span>&#160;<span class="preprocessor">#       if GLM_COMPILER &amp; GLM_COMPILER_VC</span></div>
<div class="line"><a name="l00421"></a><span class="lineno">  421</span>&#160;<span class="preprocessor">#               define GLM_INLINE __forceinline</span></div>
<div class="line"><a name="l00422"></a><span class="lineno">  422</span>&#160;<span class="preprocessor">#               define GLM_NEVER_INLINE __declspec((noinline))</span></div>
<div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160;<span class="preprocessor">#       elif GLM_COMPILER &amp; (GLM_COMPILER_GCC | GLM_COMPILER_CLANG)</span></div>
<div class="line"><a name="l00424"></a><span class="lineno">  424</span>&#160;<span class="preprocessor">#               define GLM_INLINE inline __attribute__((__always_inline__))</span></div>
<div class="line"><a name="l00425"></a><span class="lineno">  425</span>&#160;<span class="preprocessor">#               define GLM_NEVER_INLINE __attribute__((__noinline__))</span></div>
<div class="line"><a name="l00426"></a><span class="lineno">  426</span>&#160;<span class="preprocessor">#       elif GLM_COMPILER &amp; GLM_COMPILER_CUDA</span></div>
<div class="line"><a name="l00427"></a><span class="lineno">  427</span>&#160;<span class="preprocessor">#               define GLM_INLINE __forceinline__</span></div>
<div class="line"><a name="l00428"></a><span class="lineno">  428</span>&#160;<span class="preprocessor">#               define GLM_NEVER_INLINE __noinline__</span></div>
<div class="line"><a name="l00429"></a><span class="lineno">  429</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00430"></a><span class="lineno">  430</span>&#160;<span class="preprocessor">#               define GLM_INLINE inline</span></div>
<div class="line"><a name="l00431"></a><span class="lineno">  431</span>&#160;<span class="preprocessor">#               define GLM_NEVER_INLINE</span></div>
<div class="line"><a name="l00432"></a><span class="lineno">  432</span>&#160;<span class="preprocessor">#       endif//GLM_COMPILER</span></div>
<div class="line"><a name="l00433"></a><span class="lineno">  433</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00434"></a><span class="lineno">  434</span>&#160;<span class="preprocessor">#       define GLM_INLINE inline</span></div>
<div class="line"><a name="l00435"></a><span class="lineno">  435</span>&#160;<span class="preprocessor">#       define GLM_NEVER_INLINE</span></div>
<div class="line"><a name="l00436"></a><span class="lineno">  436</span>&#160;<span class="preprocessor">#endif//defined(GLM_FORCE_INLINE)</span></div>
<div class="line"><a name="l00437"></a><span class="lineno">  437</span>&#160;</div>
<div class="line"><a name="l00438"></a><span class="lineno">  438</span>&#160;<span class="preprocessor">#define GLM_FUNC_DECL GLM_CUDA_FUNC_DECL</span></div>
<div class="line"><a name="l00439"></a><span class="lineno">  439</span>&#160;<span class="preprocessor">#define GLM_FUNC_QUALIFIER GLM_CUDA_FUNC_DEF GLM_INLINE</span></div>
<div class="line"><a name="l00440"></a><span class="lineno">  440</span>&#160;</div>
<div class="line"><a name="l00442"></a><span class="lineno">  442</span>&#160;<span class="comment">// Swizzle operators</span></div>
<div class="line"><a name="l00443"></a><span class="lineno">  443</span>&#160;</div>
<div class="line"><a name="l00444"></a><span class="lineno">  444</span>&#160;<span class="comment">// User defines: GLM_FORCE_SWIZZLE</span></div>
<div class="line"><a name="l00445"></a><span class="lineno">  445</span>&#160;</div>
<div class="line"><a name="l00446"></a><span class="lineno">  446</span>&#160;<span class="preprocessor">#define GLM_SWIZZLE_DISABLED            0</span></div>
<div class="line"><a name="l00447"></a><span class="lineno">  447</span>&#160;<span class="preprocessor">#define GLM_SWIZZLE_OPERATOR            1</span></div>
<div class="line"><a name="l00448"></a><span class="lineno">  448</span>&#160;<span class="preprocessor">#define GLM_SWIZZLE_FUNCTION            2</span></div>
<div class="line"><a name="l00449"></a><span class="lineno">  449</span>&#160;</div>
<div class="line"><a name="l00450"></a><span class="lineno">  450</span>&#160;<span class="preprocessor">#if defined(GLM_FORCE_XYZW_ONLY)</span></div>
<div class="line"><a name="l00451"></a><span class="lineno">  451</span>&#160;<span class="preprocessor">#       undef GLM_FORCE_SWIZZLE</span></div>
<div class="line"><a name="l00452"></a><span class="lineno">  452</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00453"></a><span class="lineno">  453</span>&#160;</div>
<div class="line"><a name="l00454"></a><span class="lineno">  454</span>&#160;<span class="preprocessor">#if defined(GLM_SWIZZLE)</span></div>
<div class="line"><a name="l00455"></a><span class="lineno">  455</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_SWIZZLE is deprecated, use GLM_FORCE_SWIZZLE instead.&quot;)</span></div>
<div class="line"><a name="l00456"></a><span class="lineno">  456</span>&#160;<span class="preprocessor">#       define GLM_FORCE_SWIZZLE</span></div>
<div class="line"><a name="l00457"></a><span class="lineno">  457</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00458"></a><span class="lineno">  458</span>&#160;</div>
<div class="line"><a name="l00459"></a><span class="lineno">  459</span>&#160;<span class="preprocessor">#if defined(GLM_FORCE_SWIZZLE) &amp;&amp; (GLM_LANG &amp; GLM_LANG_CXXMS_FLAG)</span></div>
<div class="line"><a name="l00460"></a><span class="lineno">  460</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_SWIZZLE GLM_SWIZZLE_OPERATOR</span></div>
<div class="line"><a name="l00461"></a><span class="lineno">  461</span>&#160;<span class="preprocessor">#elif defined(GLM_FORCE_SWIZZLE)</span></div>
<div class="line"><a name="l00462"></a><span class="lineno">  462</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_SWIZZLE GLM_SWIZZLE_FUNCTION</span></div>
<div class="line"><a name="l00463"></a><span class="lineno">  463</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00464"></a><span class="lineno">  464</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_SWIZZLE GLM_SWIZZLE_DISABLED</span></div>
<div class="line"><a name="l00465"></a><span class="lineno">  465</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00466"></a><span class="lineno">  466</span>&#160;</div>
<div class="line"><a name="l00468"></a><span class="lineno">  468</span>&#160;<span class="comment">// Allows using not basic types as genType</span></div>
<div class="line"><a name="l00469"></a><span class="lineno">  469</span>&#160;</div>
<div class="line"><a name="l00470"></a><span class="lineno">  470</span>&#160;<span class="comment">// #define GLM_FORCE_UNRESTRICTED_GENTYPE</span></div>
<div class="line"><a name="l00471"></a><span class="lineno">  471</span>&#160;</div>
<div class="line"><a name="l00472"></a><span class="lineno">  472</span>&#160;<span class="preprocessor">#ifdef GLM_FORCE_UNRESTRICTED_GENTYPE</span></div>
<div class="line"><a name="l00473"></a><span class="lineno">  473</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_UNRESTRICTED_GENTYPE GLM_ENABLE</span></div>
<div class="line"><a name="l00474"></a><span class="lineno">  474</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00475"></a><span class="lineno">  475</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_UNRESTRICTED_GENTYPE GLM_DISABLE</span></div>
<div class="line"><a name="l00476"></a><span class="lineno">  476</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00477"></a><span class="lineno">  477</span>&#160;</div>
<div class="line"><a name="l00479"></a><span class="lineno">  479</span>&#160;<span class="comment">// Clip control, define GLM_FORCE_DEPTH_ZERO_TO_ONE before including GLM</span></div>
<div class="line"><a name="l00480"></a><span class="lineno">  480</span>&#160;<span class="comment">// to use a clip space between 0 to 1.</span></div>
<div class="line"><a name="l00481"></a><span class="lineno">  481</span>&#160;<span class="comment">// Coordinate system, define GLM_FORCE_LEFT_HANDED before including GLM</span></div>
<div class="line"><a name="l00482"></a><span class="lineno">  482</span>&#160;<span class="comment">// to use left handed coordinate system by default.</span></div>
<div class="line"><a name="l00483"></a><span class="lineno">  483</span>&#160;</div>
<div class="line"><a name="l00484"></a><span class="lineno">  484</span>&#160;<span class="preprocessor">#define GLM_CLIP_CONTROL_ZO_BIT         (1 &lt;&lt; 0) // ZERO_TO_ONE</span></div>
<div class="line"><a name="l00485"></a><span class="lineno">  485</span>&#160;<span class="preprocessor">#define GLM_CLIP_CONTROL_NO_BIT         (1 &lt;&lt; 1) // NEGATIVE_ONE_TO_ONE</span></div>
<div class="line"><a name="l00486"></a><span class="lineno">  486</span>&#160;<span class="preprocessor">#define GLM_CLIP_CONTROL_LH_BIT         (1 &lt;&lt; 2) // LEFT_HANDED, For DirectX, Metal, Vulkan</span></div>
<div class="line"><a name="l00487"></a><span class="lineno">  487</span>&#160;<span class="preprocessor">#define GLM_CLIP_CONTROL_RH_BIT         (1 &lt;&lt; 3) // RIGHT_HANDED, For OpenGL, default in GLM</span></div>
<div class="line"><a name="l00488"></a><span class="lineno">  488</span>&#160;</div>
<div class="line"><a name="l00489"></a><span class="lineno">  489</span>&#160;<span class="preprocessor">#define GLM_CLIP_CONTROL_LH_ZO (GLM_CLIP_CONTROL_LH_BIT | GLM_CLIP_CONTROL_ZO_BIT)</span></div>
<div class="line"><a name="l00490"></a><span class="lineno">  490</span>&#160;<span class="preprocessor">#define GLM_CLIP_CONTROL_LH_NO (GLM_CLIP_CONTROL_LH_BIT | GLM_CLIP_CONTROL_NO_BIT)</span></div>
<div class="line"><a name="l00491"></a><span class="lineno">  491</span>&#160;<span class="preprocessor">#define GLM_CLIP_CONTROL_RH_ZO (GLM_CLIP_CONTROL_RH_BIT | GLM_CLIP_CONTROL_ZO_BIT)</span></div>
<div class="line"><a name="l00492"></a><span class="lineno">  492</span>&#160;<span class="preprocessor">#define GLM_CLIP_CONTROL_RH_NO (GLM_CLIP_CONTROL_RH_BIT | GLM_CLIP_CONTROL_NO_BIT)</span></div>
<div class="line"><a name="l00493"></a><span class="lineno">  493</span>&#160;</div>
<div class="line"><a name="l00494"></a><span class="lineno">  494</span>&#160;<span class="preprocessor">#ifdef GLM_FORCE_DEPTH_ZERO_TO_ONE</span></div>
<div class="line"><a name="l00495"></a><span class="lineno">  495</span>&#160;<span class="preprocessor">#       ifdef GLM_FORCE_LEFT_HANDED</span></div>
<div class="line"><a name="l00496"></a><span class="lineno">  496</span>&#160;<span class="preprocessor">#               define GLM_CONFIG_CLIP_CONTROL GLM_CLIP_CONTROL_LH_ZO</span></div>
<div class="line"><a name="l00497"></a><span class="lineno">  497</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00498"></a><span class="lineno">  498</span>&#160;<span class="preprocessor">#               define GLM_CONFIG_CLIP_CONTROL GLM_CLIP_CONTROL_RH_ZO</span></div>
<div class="line"><a name="l00499"></a><span class="lineno">  499</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00500"></a><span class="lineno">  500</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00501"></a><span class="lineno">  501</span>&#160;<span class="preprocessor">#       ifdef GLM_FORCE_LEFT_HANDED</span></div>
<div class="line"><a name="l00502"></a><span class="lineno">  502</span>&#160;<span class="preprocessor">#               define GLM_CONFIG_CLIP_CONTROL GLM_CLIP_CONTROL_LH_NO</span></div>
<div class="line"><a name="l00503"></a><span class="lineno">  503</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00504"></a><span class="lineno">  504</span>&#160;<span class="preprocessor">#               define GLM_CONFIG_CLIP_CONTROL GLM_CLIP_CONTROL_RH_NO</span></div>
<div class="line"><a name="l00505"></a><span class="lineno">  505</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00506"></a><span class="lineno">  506</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00507"></a><span class="lineno">  507</span>&#160;</div>
<div class="line"><a name="l00509"></a><span class="lineno">  509</span>&#160;<span class="comment">// Qualifiers</span></div>
<div class="line"><a name="l00510"></a><span class="lineno">  510</span>&#160;</div>
<div class="line"><a name="l00511"></a><span class="lineno">  511</span>&#160;<span class="preprocessor">#if (GLM_COMPILER &amp; GLM_COMPILER_VC) || ((GLM_COMPILER &amp; GLM_COMPILER_INTEL) &amp;&amp; (GLM_PLATFORM &amp; GLM_PLATFORM_WINDOWS))</span></div>
<div class="line"><a name="l00512"></a><span class="lineno">  512</span>&#160;<span class="preprocessor">#       define GLM_DEPRECATED __declspec(deprecated)</span></div>
<div class="line"><a name="l00513"></a><span class="lineno">  513</span>&#160;<span class="preprocessor">#       define GLM_ALIGNED_TYPEDEF(type, name, alignment) typedef __declspec(align(alignment)) type name</span></div>
<div class="line"><a name="l00514"></a><span class="lineno">  514</span>&#160;<span class="preprocessor">#elif GLM_COMPILER &amp; (GLM_COMPILER_GCC | GLM_COMPILER_CLANG | GLM_COMPILER_INTEL)</span></div>
<div class="line"><a name="l00515"></a><span class="lineno">  515</span>&#160;<span class="preprocessor">#       define GLM_DEPRECATED __attribute__((__deprecated__))</span></div>
<div class="line"><a name="l00516"></a><span class="lineno">  516</span>&#160;<span class="preprocessor">#       define GLM_ALIGNED_TYPEDEF(type, name, alignment) typedef type name __attribute__((aligned(alignment)))</span></div>
<div class="line"><a name="l00517"></a><span class="lineno">  517</span>&#160;<span class="preprocessor">#elif GLM_COMPILER &amp; GLM_COMPILER_CUDA</span></div>
<div class="line"><a name="l00518"></a><span class="lineno">  518</span>&#160;<span class="preprocessor">#       define GLM_DEPRECATED</span></div>
<div class="line"><a name="l00519"></a><span class="lineno">  519</span>&#160;<span class="preprocessor">#       define GLM_ALIGNED_TYPEDEF(type, name, alignment) typedef type name __align__(x)</span></div>
<div class="line"><a name="l00520"></a><span class="lineno">  520</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00521"></a><span class="lineno">  521</span>&#160;<span class="preprocessor">#       define GLM_DEPRECATED</span></div>
<div class="line"><a name="l00522"></a><span class="lineno">  522</span>&#160;<span class="preprocessor">#       define GLM_ALIGNED_TYPEDEF(type, name, alignment) typedef type name</span></div>
<div class="line"><a name="l00523"></a><span class="lineno">  523</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00524"></a><span class="lineno">  524</span>&#160;</div>
<div class="line"><a name="l00526"></a><span class="lineno">  526</span>&#160;</div>
<div class="line"><a name="l00527"></a><span class="lineno">  527</span>&#160;<span class="preprocessor">#ifdef GLM_FORCE_EXPLICIT_CTOR</span></div>
<div class="line"><a name="l00528"></a><span class="lineno">  528</span>&#160;<span class="preprocessor">#       define GLM_EXPLICIT explicit</span></div>
<div class="line"><a name="l00529"></a><span class="lineno">  529</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00530"></a><span class="lineno">  530</span>&#160;<span class="preprocessor">#       define GLM_EXPLICIT</span></div>
<div class="line"><a name="l00531"></a><span class="lineno">  531</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00532"></a><span class="lineno">  532</span>&#160;</div>
<div class="line"><a name="l00534"></a><span class="lineno">  534</span>&#160;<span class="comment">// SYCL</span></div>
<div class="line"><a name="l00535"></a><span class="lineno">  535</span>&#160;</div>
<div class="line"><a name="l00536"></a><span class="lineno">  536</span>&#160;<span class="preprocessor">#if GLM_COMPILER==GLM_COMPILER_SYCL</span></div>
<div class="line"><a name="l00537"></a><span class="lineno">  537</span>&#160;</div>
<div class="line"><a name="l00538"></a><span class="lineno">  538</span>&#160;<span class="preprocessor">#include &lt;CL/sycl.hpp&gt;</span></div>
<div class="line"><a name="l00539"></a><span class="lineno">  539</span>&#160;<span class="preprocessor">#include &lt;limits&gt;</span></div>
<div class="line"><a name="l00540"></a><span class="lineno">  540</span>&#160;</div>
<div class="line"><a name="l00541"></a><span class="lineno">  541</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a> {</div>
<div class="line"><a name="l00542"></a><span class="lineno">  542</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00240.html">std</a> {</div>
<div class="line"><a name="l00543"></a><span class="lineno">  543</span>&#160;        <span class="comment">// Import SYCL&#39;s functions into the namespace glm::std to force their usages.</span></div>
<div class="line"><a name="l00544"></a><span class="lineno">  544</span>&#160;        <span class="comment">// It&#39;s important to use the math built-in function (sin, exp, ...)</span></div>
<div class="line"><a name="l00545"></a><span class="lineno">  545</span>&#160;        <span class="comment">// of SYCL instead the std ones.</span></div>
<div class="line"><a name="l00546"></a><span class="lineno">  546</span>&#160;        <span class="keyword">using namespace </span>cl::sycl;</div>
<div class="line"><a name="l00547"></a><span class="lineno">  547</span>&#160;</div>
<div class="line"><a name="l00549"></a><span class="lineno">  549</span>&#160;        <span class="comment">// Import some &quot;harmless&quot; std&#39;s stuffs used by glm into</span></div>
<div class="line"><a name="l00550"></a><span class="lineno">  550</span>&#160;        <span class="comment">// the new glm::std namespace.</span></div>
<div class="line"><a name="l00551"></a><span class="lineno">  551</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00552"></a><span class="lineno">  552</span>&#160;        <span class="keyword">using</span> numeric_limits = ::std::numeric_limits&lt;T&gt;;</div>
<div class="line"><a name="l00553"></a><span class="lineno">  553</span>&#160;</div>
<div class="line"><a name="l00554"></a><span class="lineno">  554</span>&#160;        using ::std::size_t;</div>
<div class="line"><a name="l00555"></a><span class="lineno">  555</span>&#160;</div>
<div class="line"><a name="l00556"></a><span class="lineno">  556</span>&#160;        <a class="code" href="a00304.html#ga28d97808322d3c92186e4a0c067d7e8e">using ::std::uint8_t</a>;</div>
<div class="line"><a name="l00557"></a><span class="lineno">  557</span>&#160;        <a class="code" href="a00304.html#ga91f91f411080c37730856ff5887f5bcf">using ::std::uint16_t</a>;</div>
<div class="line"><a name="l00558"></a><span class="lineno">  558</span>&#160;        <a class="code" href="a00304.html#ga2171d9dc1fefb1c82e2817f45b622eac">using ::std::uint32_t</a>;</div>
<div class="line"><a name="l00559"></a><span class="lineno">  559</span>&#160;        <a class="code" href="a00304.html#ga3999d3e7ff22025c16ddb601e14dfdee">using ::std::uint64_t</a>;</div>
<div class="line"><a name="l00560"></a><span class="lineno">  560</span>&#160;</div>
<div class="line"><a name="l00561"></a><span class="lineno">  561</span>&#160;        <a class="code" href="a00304.html#ga4bf09d8838a86866b39ee6e109341645">using ::std::int8_t</a>;</div>
<div class="line"><a name="l00562"></a><span class="lineno">  562</span>&#160;        <a class="code" href="a00304.html#gae8f5e3e964ca2ae240adc2c0d74adede">using ::std::int16_t</a>;</div>
<div class="line"><a name="l00563"></a><span class="lineno">  563</span>&#160;        <a class="code" href="a00304.html#ga042ef09ff2f0cb24a36f541bcb3a3710">using ::std::int32_t</a>;</div>
<div class="line"><a name="l00564"></a><span class="lineno">  564</span>&#160;        <a class="code" href="a00304.html#ga322a7d7d2c2c68994dc872a33de63c61">using ::std::int64_t</a>;</div>
<div class="line"><a name="l00565"></a><span class="lineno">  565</span>&#160;</div>
<div class="line"><a name="l00566"></a><span class="lineno">  566</span>&#160;        using ::std::make_unsigned;</div>
<div class="line"><a name="l00568"></a><span class="lineno">  568</span>&#160;} <span class="comment">//namespace std</span></div>
<div class="line"><a name="l00569"></a><span class="lineno">  569</span>&#160;} <span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00570"></a><span class="lineno">  570</span>&#160;</div>
<div class="line"><a name="l00571"></a><span class="lineno">  571</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00572"></a><span class="lineno">  572</span>&#160;</div>
<div class="line"><a name="l00574"></a><span class="lineno">  574</span>&#160;</div>
<div class="line"><a name="l00576"></a><span class="lineno">  576</span>&#160;<span class="comment">// Length type: all length functions returns a length_t type.</span></div>
<div class="line"><a name="l00577"></a><span class="lineno">  577</span>&#160;<span class="comment">// When GLM_FORCE_SIZE_T_LENGTH is defined, length_t is a typedef of size_t otherwise</span></div>
<div class="line"><a name="l00578"></a><span class="lineno">  578</span>&#160;<span class="comment">// length_t is a typedef of int like GLSL defines it.</span></div>
<div class="line"><a name="l00579"></a><span class="lineno">  579</span>&#160;</div>
<div class="line"><a name="l00580"></a><span class="lineno">  580</span>&#160;<span class="preprocessor">#define GLM_LENGTH_INT          1</span></div>
<div class="line"><a name="l00581"></a><span class="lineno">  581</span>&#160;<span class="preprocessor">#define GLM_LENGTH_SIZE_T       2</span></div>
<div class="line"><a name="l00582"></a><span class="lineno">  582</span>&#160;</div>
<div class="line"><a name="l00583"></a><span class="lineno">  583</span>&#160;<span class="preprocessor">#ifdef GLM_FORCE_SIZE_T_LENGTH</span></div>
<div class="line"><a name="l00584"></a><span class="lineno">  584</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_LENGTH_TYPE           GLM_LENGTH_SIZE_T</span></div>
<div class="line"><a name="l00585"></a><span class="lineno">  585</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00586"></a><span class="lineno">  586</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_LENGTH_TYPE           GLM_LENGTH_INT</span></div>
<div class="line"><a name="l00587"></a><span class="lineno">  587</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00588"></a><span class="lineno">  588</span>&#160;</div>
<div class="line"><a name="l00589"></a><span class="lineno">  589</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00590"></a><span class="lineno">  590</span>&#160;{</div>
<div class="line"><a name="l00591"></a><span class="lineno">  591</span>&#160;        <span class="keyword">using</span> std::size_t;</div>
<div class="line"><a name="l00592"></a><span class="lineno">  592</span>&#160;<span class="preprocessor">#       if GLM_CONFIG_LENGTH_TYPE == GLM_LENGTH_SIZE_T</span></div>
<div class="line"><a name="l00593"></a><span class="lineno">  593</span>&#160;                <span class="keyword">typedef</span> <span class="keywordtype">size_t</span> length_t;</div>
<div class="line"><a name="l00594"></a><span class="lineno">  594</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00595"></a><span class="lineno">  595</span>&#160;                <span class="keyword">typedef</span> <span class="keywordtype">int</span> length_t;</div>
<div class="line"><a name="l00596"></a><span class="lineno">  596</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00597"></a><span class="lineno">  597</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00598"></a><span class="lineno">  598</span>&#160;</div>
<div class="line"><a name="l00600"></a><span class="lineno">  600</span>&#160;<span class="comment">// constexpr</span></div>
<div class="line"><a name="l00601"></a><span class="lineno">  601</span>&#160;</div>
<div class="line"><a name="l00602"></a><span class="lineno">  602</span>&#160;<span class="preprocessor">#if GLM_HAS_CONSTEXPR</span></div>
<div class="line"><a name="l00603"></a><span class="lineno">  603</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_CONSTEXP GLM_ENABLE</span></div>
<div class="line"><a name="l00604"></a><span class="lineno">  604</span>&#160;</div>
<div class="line"><a name="l00605"></a><span class="lineno">  605</span>&#160;        <span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00606"></a><span class="lineno">  606</span>&#160;        {</div>
<div class="line"><a name="l00607"></a><span class="lineno">  607</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, std::<span class="keywordtype">size_t</span> N&gt;</div>
<div class="line"><a name="l00608"></a><span class="lineno">  608</span>&#160;                constexpr std::size_t countof(T <span class="keyword">const</span> (&amp;)[N])</div>
<div class="line"><a name="l00609"></a><span class="lineno">  609</span>&#160;                {</div>
<div class="line"><a name="l00610"></a><span class="lineno">  610</span>&#160;                        <span class="keywordflow">return</span> N;</div>
<div class="line"><a name="l00611"></a><span class="lineno">  611</span>&#160;                }</div>
<div class="line"><a name="l00612"></a><span class="lineno">  612</span>&#160;        }<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00613"></a><span class="lineno">  613</span>&#160;<span class="preprocessor">#       define GLM_COUNTOF(arr) glm::countof(arr)</span></div>
<div class="line"><a name="l00614"></a><span class="lineno">  614</span>&#160;<span class="preprocessor">#elif defined(_MSC_VER)</span></div>
<div class="line"><a name="l00615"></a><span class="lineno">  615</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_CONSTEXP GLM_DISABLE</span></div>
<div class="line"><a name="l00616"></a><span class="lineno">  616</span>&#160;</div>
<div class="line"><a name="l00617"></a><span class="lineno">  617</span>&#160;<span class="preprocessor">#       define GLM_COUNTOF(arr) _countof(arr)</span></div>
<div class="line"><a name="l00618"></a><span class="lineno">  618</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00619"></a><span class="lineno">  619</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_CONSTEXP GLM_DISABLE</span></div>
<div class="line"><a name="l00620"></a><span class="lineno">  620</span>&#160;</div>
<div class="line"><a name="l00621"></a><span class="lineno">  621</span>&#160;<span class="preprocessor">#       define GLM_COUNTOF(arr) sizeof(arr) / sizeof(arr[0])</span></div>
<div class="line"><a name="l00622"></a><span class="lineno">  622</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00623"></a><span class="lineno">  623</span>&#160;</div>
<div class="line"><a name="l00625"></a><span class="lineno">  625</span>&#160;<span class="comment">// uint</span></div>
<div class="line"><a name="l00626"></a><span class="lineno">  626</span>&#160;</div>
<div class="line"><a name="l00627"></a><span class="lineno">  627</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a>{</div>
<div class="line"><a name="l00628"></a><span class="lineno">  628</span>&#160;<span class="keyword">namespace </span>detail</div>
<div class="line"><a name="l00629"></a><span class="lineno">  629</span>&#160;{</div>
<div class="line"><a name="l00630"></a><span class="lineno">  630</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00631"></a><span class="lineno">  631</span>&#160;        <span class="keyword">struct </span>is_int</div>
<div class="line"><a name="l00632"></a><span class="lineno">  632</span>&#160;        {</div>
<div class="line"><a name="l00633"></a><span class="lineno">  633</span>&#160;                <span class="keyword">enum</span> test {value = 0};</div>
<div class="line"><a name="l00634"></a><span class="lineno">  634</span>&#160;        };</div>
<div class="line"><a name="l00635"></a><span class="lineno">  635</span>&#160;</div>
<div class="line"><a name="l00636"></a><span class="lineno">  636</span>&#160;        <span class="keyword">template</span>&lt;&gt;</div>
<div class="line"><a name="l00637"></a><span class="lineno">  637</span>&#160;        <span class="keyword">struct </span>is_int&lt;unsigned int&gt;</div>
<div class="line"><a name="l00638"></a><span class="lineno">  638</span>&#160;        {</div>
<div class="line"><a name="l00639"></a><span class="lineno">  639</span>&#160;                <span class="keyword">enum</span> test {value = ~0};</div>
<div class="line"><a name="l00640"></a><span class="lineno">  640</span>&#160;        };</div>
<div class="line"><a name="l00641"></a><span class="lineno">  641</span>&#160;</div>
<div class="line"><a name="l00642"></a><span class="lineno">  642</span>&#160;        <span class="keyword">template</span>&lt;&gt;</div>
<div class="line"><a name="l00643"></a><span class="lineno">  643</span>&#160;        <span class="keyword">struct </span>is_int&lt;signed int&gt;</div>
<div class="line"><a name="l00644"></a><span class="lineno">  644</span>&#160;        {</div>
<div class="line"><a name="l00645"></a><span class="lineno">  645</span>&#160;                <span class="keyword">enum</span> test {value = ~0};</div>
<div class="line"><a name="l00646"></a><span class="lineno">  646</span>&#160;        };</div>
<div class="line"><a name="l00647"></a><span class="lineno">  647</span>&#160;}<span class="comment">//namespace detail</span></div>
<div class="line"><a name="l00648"></a><span class="lineno">  648</span>&#160;</div>
<div class="line"><a name="l00649"></a><span class="lineno">  649</span>&#160;        <span class="keyword">typedef</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span>    uint;</div>
<div class="line"><a name="l00650"></a><span class="lineno">  650</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00651"></a><span class="lineno">  651</span>&#160;</div>
<div class="line"><a name="l00653"></a><span class="lineno">  653</span>&#160;<span class="comment">// 64-bit int</span></div>
<div class="line"><a name="l00654"></a><span class="lineno">  654</span>&#160;</div>
<div class="line"><a name="l00655"></a><span class="lineno">  655</span>&#160;<span class="preprocessor">#if GLM_HAS_EXTENDED_INTEGER_TYPE</span></div>
<div class="line"><a name="l00656"></a><span class="lineno">  656</span>&#160;<span class="preprocessor">#       include &lt;cstdint&gt;</span></div>
<div class="line"><a name="l00657"></a><span class="lineno">  657</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00658"></a><span class="lineno">  658</span>&#160;</div>
<div class="line"><a name="l00659"></a><span class="lineno">  659</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a>{</div>
<div class="line"><a name="l00660"></a><span class="lineno">  660</span>&#160;<span class="keyword">namespace </span>detail</div>
<div class="line"><a name="l00661"></a><span class="lineno">  661</span>&#160;{</div>
<div class="line"><a name="l00662"></a><span class="lineno">  662</span>&#160;<span class="preprocessor">#       if GLM_HAS_EXTENDED_INTEGER_TYPE</span></div>
<div class="line"><a name="l00663"></a><span class="lineno">  663</span>&#160;                <span class="keyword">typedef</span> <a class="code" href="a00304.html#ga3999d3e7ff22025c16ddb601e14dfdee">std::uint64_t</a>                                           <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a>;</div>
<div class="line"><a name="l00664"></a><span class="lineno">  664</span>&#160;                <span class="keyword">typedef</span> <a class="code" href="a00304.html#ga322a7d7d2c2c68994dc872a33de63c61">std::int64_t</a>                                            <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">int64</a>;</div>
<div class="line"><a name="l00665"></a><span class="lineno">  665</span>&#160;<span class="preprocessor">#       elif (defined(__STDC_VERSION__) &amp;&amp; (__STDC_VERSION__ &gt;= 199901L)) // C99 detected, 64 bit types available</span></div>
<div class="line"><a name="l00666"></a><span class="lineno">  666</span>&#160;                <span class="keyword">typedef</span> <a class="code" href="a00304.html#ga3999d3e7ff22025c16ddb601e14dfdee">uint64_t</a>                                                        <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a>;</div>
<div class="line"><a name="l00667"></a><span class="lineno">  667</span>&#160;                <span class="keyword">typedef</span> <a class="code" href="a00304.html#ga322a7d7d2c2c68994dc872a33de63c61">int64_t</a>                                                         <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">int64</a>;</div>
<div class="line"><a name="l00668"></a><span class="lineno">  668</span>&#160;<span class="preprocessor">#       elif GLM_COMPILER &amp; GLM_COMPILER_VC</span></div>
<div class="line"><a name="l00669"></a><span class="lineno">  669</span>&#160;                <span class="keyword">typedef</span> <span class="keywordtype">unsigned</span> __int64                                        <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a>;</div>
<div class="line"><a name="l00670"></a><span class="lineno">  670</span>&#160;                <span class="keyword">typedef</span> <span class="keywordtype">signed</span> __int64                                          <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">int64</a>;</div>
<div class="line"><a name="l00671"></a><span class="lineno">  671</span>&#160;<span class="preprocessor">#       elif GLM_COMPILER &amp; GLM_COMPILER_GCC</span></div>
<div class="line"><a name="l00672"></a><span class="lineno">  672</span>&#160;<span class="preprocessor">#               pragma GCC diagnostic ignored &quot;-Wlong-long&quot;</span></div>
<div class="line"><a name="l00673"></a><span class="lineno">  673</span>&#160;                __extension__ <span class="keyword">typedef</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">long</span> <span class="keywordtype">long</span>        <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a>;</div>
<div class="line"><a name="l00674"></a><span class="lineno">  674</span>&#160;                __extension__ <span class="keyword">typedef</span> <span class="keywordtype">signed</span> <span class="keywordtype">long</span> <span class="keywordtype">long</span>          <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">int64</a>;</div>
<div class="line"><a name="l00675"></a><span class="lineno">  675</span>&#160;<span class="preprocessor">#       elif (GLM_COMPILER &amp; GLM_COMPILER_CLANG)</span></div>
<div class="line"><a name="l00676"></a><span class="lineno">  676</span>&#160;<span class="preprocessor">#               pragma clang diagnostic ignored &quot;-Wc++11-long-long&quot;</span></div>
<div class="line"><a name="l00677"></a><span class="lineno">  677</span>&#160;                <span class="keyword">typedef</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">long</span> <span class="keywordtype">long</span>                                      <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a>;</div>
<div class="line"><a name="l00678"></a><span class="lineno">  678</span>&#160;                <span class="keyword">typedef</span> <span class="keywordtype">signed</span> <span class="keywordtype">long</span> <span class="keywordtype">long</span>                                        <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">int64</a>;</div>
<div class="line"><a name="l00679"></a><span class="lineno">  679</span>&#160;<span class="preprocessor">#       else//unknown compiler</span></div>
<div class="line"><a name="l00680"></a><span class="lineno">  680</span>&#160;                <span class="keyword">typedef</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">long</span> <span class="keywordtype">long</span>                                      <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a>;</div>
<div class="line"><a name="l00681"></a><span class="lineno">  681</span>&#160;                <span class="keyword">typedef</span> <span class="keywordtype">signed</span> <span class="keywordtype">long</span> <span class="keywordtype">long</span>                                        <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">int64</a>;</div>
<div class="line"><a name="l00682"></a><span class="lineno">  682</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00683"></a><span class="lineno">  683</span>&#160;}<span class="comment">//namespace detail</span></div>
<div class="line"><a name="l00684"></a><span class="lineno">  684</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00685"></a><span class="lineno">  685</span>&#160;</div>
<div class="line"><a name="l00687"></a><span class="lineno">  687</span>&#160;<span class="comment">// make_unsigned</span></div>
<div class="line"><a name="l00688"></a><span class="lineno">  688</span>&#160;</div>
<div class="line"><a name="l00689"></a><span class="lineno">  689</span>&#160;<span class="preprocessor">#if GLM_HAS_MAKE_SIGNED</span></div>
<div class="line"><a name="l00690"></a><span class="lineno">  690</span>&#160;<span class="preprocessor">#       include &lt;type_traits&gt;</span></div>
<div class="line"><a name="l00691"></a><span class="lineno">  691</span>&#160;</div>
<div class="line"><a name="l00692"></a><span class="lineno">  692</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a>{</div>
<div class="line"><a name="l00693"></a><span class="lineno">  693</span>&#160;<span class="keyword">namespace </span>detail</div>
<div class="line"><a name="l00694"></a><span class="lineno">  694</span>&#160;{</div>
<div class="line"><a name="l00695"></a><span class="lineno">  695</span>&#160;        <span class="keyword">using</span> std::make_unsigned;</div>
<div class="line"><a name="l00696"></a><span class="lineno">  696</span>&#160;}<span class="comment">//namespace detail</span></div>
<div class="line"><a name="l00697"></a><span class="lineno">  697</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00698"></a><span class="lineno">  698</span>&#160;</div>
<div class="line"><a name="l00699"></a><span class="lineno">  699</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00700"></a><span class="lineno">  700</span>&#160;</div>
<div class="line"><a name="l00701"></a><span class="lineno">  701</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a>{</div>
<div class="line"><a name="l00702"></a><span class="lineno">  702</span>&#160;<span class="keyword">namespace </span>detail</div>
<div class="line"><a name="l00703"></a><span class="lineno">  703</span>&#160;{</div>
<div class="line"><a name="l00704"></a><span class="lineno">  704</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00705"></a><span class="lineno">  705</span>&#160;        <span class="keyword">struct </span>make_unsigned</div>
<div class="line"><a name="l00706"></a><span class="lineno">  706</span>&#160;        {};</div>
<div class="line"><a name="l00707"></a><span class="lineno">  707</span>&#160;</div>
<div class="line"><a name="l00708"></a><span class="lineno">  708</span>&#160;        <span class="keyword">template</span>&lt;&gt;</div>
<div class="line"><a name="l00709"></a><span class="lineno">  709</span>&#160;        <span class="keyword">struct </span>make_unsigned&lt;char&gt;</div>
<div class="line"><a name="l00710"></a><span class="lineno">  710</span>&#160;        {</div>
<div class="line"><a name="l00711"></a><span class="lineno">  711</span>&#160;                <span class="keyword">typedef</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span> type;</div>
<div class="line"><a name="l00712"></a><span class="lineno">  712</span>&#160;        };</div>
<div class="line"><a name="l00713"></a><span class="lineno">  713</span>&#160;</div>
<div class="line"><a name="l00714"></a><span class="lineno">  714</span>&#160;        <span class="keyword">template</span>&lt;&gt;</div>
<div class="line"><a name="l00715"></a><span class="lineno">  715</span>&#160;        <span class="keyword">struct </span>make_unsigned&lt;signed char&gt;</div>
<div class="line"><a name="l00716"></a><span class="lineno">  716</span>&#160;        {</div>
<div class="line"><a name="l00717"></a><span class="lineno">  717</span>&#160;                <span class="keyword">typedef</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span> type;</div>
<div class="line"><a name="l00718"></a><span class="lineno">  718</span>&#160;        };</div>
<div class="line"><a name="l00719"></a><span class="lineno">  719</span>&#160;</div>
<div class="line"><a name="l00720"></a><span class="lineno">  720</span>&#160;        <span class="keyword">template</span>&lt;&gt;</div>
<div class="line"><a name="l00721"></a><span class="lineno">  721</span>&#160;        <span class="keyword">struct </span>make_unsigned&lt;short&gt;</div>
<div class="line"><a name="l00722"></a><span class="lineno">  722</span>&#160;        {</div>
<div class="line"><a name="l00723"></a><span class="lineno">  723</span>&#160;                <span class="keyword">typedef</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">short</span> type;</div>
<div class="line"><a name="l00724"></a><span class="lineno">  724</span>&#160;        };</div>
<div class="line"><a name="l00725"></a><span class="lineno">  725</span>&#160;</div>
<div class="line"><a name="l00726"></a><span class="lineno">  726</span>&#160;        <span class="keyword">template</span>&lt;&gt;</div>
<div class="line"><a name="l00727"></a><span class="lineno">  727</span>&#160;        <span class="keyword">struct </span>make_unsigned&lt;int&gt;</div>
<div class="line"><a name="l00728"></a><span class="lineno">  728</span>&#160;        {</div>
<div class="line"><a name="l00729"></a><span class="lineno">  729</span>&#160;                <span class="keyword">typedef</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> type;</div>
<div class="line"><a name="l00730"></a><span class="lineno">  730</span>&#160;        };</div>
<div class="line"><a name="l00731"></a><span class="lineno">  731</span>&#160;</div>
<div class="line"><a name="l00732"></a><span class="lineno">  732</span>&#160;        <span class="keyword">template</span>&lt;&gt;</div>
<div class="line"><a name="l00733"></a><span class="lineno">  733</span>&#160;        <span class="keyword">struct </span>make_unsigned&lt;long&gt;</div>
<div class="line"><a name="l00734"></a><span class="lineno">  734</span>&#160;        {</div>
<div class="line"><a name="l00735"></a><span class="lineno">  735</span>&#160;                <span class="keyword">typedef</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">long</span> type;</div>
<div class="line"><a name="l00736"></a><span class="lineno">  736</span>&#160;        };</div>
<div class="line"><a name="l00737"></a><span class="lineno">  737</span>&#160;</div>
<div class="line"><a name="l00738"></a><span class="lineno">  738</span>&#160;        <span class="keyword">template</span>&lt;&gt;</div>
<div class="line"><a name="l00739"></a><span class="lineno">  739</span>&#160;        <span class="keyword">struct </span>make_unsigned&lt;<a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">int64</a>&gt;</div>
<div class="line"><a name="l00740"></a><span class="lineno">  740</span>&#160;        {</div>
<div class="line"><a name="l00741"></a><span class="lineno">  741</span>&#160;                <span class="keyword">typedef</span> <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a> type;</div>
<div class="line"><a name="l00742"></a><span class="lineno">  742</span>&#160;        };</div>
<div class="line"><a name="l00743"></a><span class="lineno">  743</span>&#160;</div>
<div class="line"><a name="l00744"></a><span class="lineno">  744</span>&#160;        <span class="keyword">template</span>&lt;&gt;</div>
<div class="line"><a name="l00745"></a><span class="lineno">  745</span>&#160;        <span class="keyword">struct </span>make_unsigned&lt;unsigned char&gt;</div>
<div class="line"><a name="l00746"></a><span class="lineno">  746</span>&#160;        {</div>
<div class="line"><a name="l00747"></a><span class="lineno">  747</span>&#160;                <span class="keyword">typedef</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span> type;</div>
<div class="line"><a name="l00748"></a><span class="lineno">  748</span>&#160;        };</div>
<div class="line"><a name="l00749"></a><span class="lineno">  749</span>&#160;</div>
<div class="line"><a name="l00750"></a><span class="lineno">  750</span>&#160;        <span class="keyword">template</span>&lt;&gt;</div>
<div class="line"><a name="l00751"></a><span class="lineno">  751</span>&#160;        <span class="keyword">struct </span>make_unsigned&lt;unsigned short&gt;</div>
<div class="line"><a name="l00752"></a><span class="lineno">  752</span>&#160;        {</div>
<div class="line"><a name="l00753"></a><span class="lineno">  753</span>&#160;                <span class="keyword">typedef</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">short</span> type;</div>
<div class="line"><a name="l00754"></a><span class="lineno">  754</span>&#160;        };</div>
<div class="line"><a name="l00755"></a><span class="lineno">  755</span>&#160;</div>
<div class="line"><a name="l00756"></a><span class="lineno">  756</span>&#160;        <span class="keyword">template</span>&lt;&gt;</div>
<div class="line"><a name="l00757"></a><span class="lineno">  757</span>&#160;        <span class="keyword">struct </span>make_unsigned&lt;unsigned int&gt;</div>
<div class="line"><a name="l00758"></a><span class="lineno">  758</span>&#160;        {</div>
<div class="line"><a name="l00759"></a><span class="lineno">  759</span>&#160;                <span class="keyword">typedef</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> type;</div>
<div class="line"><a name="l00760"></a><span class="lineno">  760</span>&#160;        };</div>
<div class="line"><a name="l00761"></a><span class="lineno">  761</span>&#160;</div>
<div class="line"><a name="l00762"></a><span class="lineno">  762</span>&#160;        <span class="keyword">template</span>&lt;&gt;</div>
<div class="line"><a name="l00763"></a><span class="lineno">  763</span>&#160;        <span class="keyword">struct </span>make_unsigned&lt;unsigned long&gt;</div>
<div class="line"><a name="l00764"></a><span class="lineno">  764</span>&#160;        {</div>
<div class="line"><a name="l00765"></a><span class="lineno">  765</span>&#160;                <span class="keyword">typedef</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">long</span> type;</div>
<div class="line"><a name="l00766"></a><span class="lineno">  766</span>&#160;        };</div>
<div class="line"><a name="l00767"></a><span class="lineno">  767</span>&#160;</div>
<div class="line"><a name="l00768"></a><span class="lineno">  768</span>&#160;        <span class="keyword">template</span>&lt;&gt;</div>
<div class="line"><a name="l00769"></a><span class="lineno">  769</span>&#160;        <span class="keyword">struct </span>make_unsigned&lt;<a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a>&gt;</div>
<div class="line"><a name="l00770"></a><span class="lineno">  770</span>&#160;        {</div>
<div class="line"><a name="l00771"></a><span class="lineno">  771</span>&#160;                <span class="keyword">typedef</span> <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a> type;</div>
<div class="line"><a name="l00772"></a><span class="lineno">  772</span>&#160;        };</div>
<div class="line"><a name="l00773"></a><span class="lineno">  773</span>&#160;}<span class="comment">//namespace detail</span></div>
<div class="line"><a name="l00774"></a><span class="lineno">  774</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00775"></a><span class="lineno">  775</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00776"></a><span class="lineno">  776</span>&#160;</div>
<div class="line"><a name="l00778"></a><span class="lineno">  778</span>&#160;<span class="comment">// Only use x, y, z, w as vector type components</span></div>
<div class="line"><a name="l00779"></a><span class="lineno">  779</span>&#160;</div>
<div class="line"><a name="l00780"></a><span class="lineno">  780</span>&#160;<span class="preprocessor">#ifdef GLM_FORCE_XYZW_ONLY</span></div>
<div class="line"><a name="l00781"></a><span class="lineno">  781</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_XYZW_ONLY GLM_ENABLE</span></div>
<div class="line"><a name="l00782"></a><span class="lineno">  782</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00783"></a><span class="lineno">  783</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_XYZW_ONLY GLM_DISABLE</span></div>
<div class="line"><a name="l00784"></a><span class="lineno">  784</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00785"></a><span class="lineno">  785</span>&#160;</div>
<div class="line"><a name="l00787"></a><span class="lineno">  787</span>&#160;<span class="comment">// Configure the use of defaulted initialized types</span></div>
<div class="line"><a name="l00788"></a><span class="lineno">  788</span>&#160;</div>
<div class="line"><a name="l00789"></a><span class="lineno">  789</span>&#160;<span class="preprocessor">#define GLM_CTOR_INIT_DISABLE           0</span></div>
<div class="line"><a name="l00790"></a><span class="lineno">  790</span>&#160;<span class="preprocessor">#define GLM_CTOR_INITIALIZER_LIST       1</span></div>
<div class="line"><a name="l00791"></a><span class="lineno">  791</span>&#160;<span class="preprocessor">#define GLM_CTOR_INITIALISATION         2</span></div>
<div class="line"><a name="l00792"></a><span class="lineno">  792</span>&#160;</div>
<div class="line"><a name="l00793"></a><span class="lineno">  793</span>&#160;<span class="preprocessor">#if defined(GLM_FORCE_CTOR_INIT) &amp;&amp; GLM_HAS_INITIALIZER_LISTS</span></div>
<div class="line"><a name="l00794"></a><span class="lineno">  794</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_CTOR_INIT GLM_CTOR_INITIALIZER_LIST</span></div>
<div class="line"><a name="l00795"></a><span class="lineno">  795</span>&#160;<span class="preprocessor">#elif defined(GLM_FORCE_CTOR_INIT) &amp;&amp; !GLM_HAS_INITIALIZER_LISTS</span></div>
<div class="line"><a name="l00796"></a><span class="lineno">  796</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_CTOR_INIT GLM_CTOR_INITIALISATION</span></div>
<div class="line"><a name="l00797"></a><span class="lineno">  797</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00798"></a><span class="lineno">  798</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_CTOR_INIT GLM_CTOR_INIT_DISABLE</span></div>
<div class="line"><a name="l00799"></a><span class="lineno">  799</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00800"></a><span class="lineno">  800</span>&#160;</div>
<div class="line"><a name="l00802"></a><span class="lineno">  802</span>&#160;<span class="comment">// Use SIMD instruction sets</span></div>
<div class="line"><a name="l00803"></a><span class="lineno">  803</span>&#160;</div>
<div class="line"><a name="l00804"></a><span class="lineno">  804</span>&#160;<span class="preprocessor">#if GLM_HAS_ALIGNOF &amp;&amp; (GLM_LANG &amp; GLM_LANG_CXXMS_FLAG) &amp;&amp; (GLM_ARCH &amp; GLM_ARCH_SIMD_BIT)</span></div>
<div class="line"><a name="l00805"></a><span class="lineno">  805</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_SIMD GLM_ENABLE</span></div>
<div class="line"><a name="l00806"></a><span class="lineno">  806</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00807"></a><span class="lineno">  807</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_SIMD GLM_DISABLE</span></div>
<div class="line"><a name="l00808"></a><span class="lineno">  808</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00809"></a><span class="lineno">  809</span>&#160;</div>
<div class="line"><a name="l00811"></a><span class="lineno">  811</span>&#160;<span class="comment">// Configure the use of defaulted function</span></div>
<div class="line"><a name="l00812"></a><span class="lineno">  812</span>&#160;</div>
<div class="line"><a name="l00813"></a><span class="lineno">  813</span>&#160;<span class="preprocessor">#if GLM_HAS_DEFAULTED_FUNCTIONS &amp;&amp; GLM_CONFIG_CTOR_INIT == GLM_CTOR_INIT_DISABLE</span></div>
<div class="line"><a name="l00814"></a><span class="lineno">  814</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_DEFAULTED_FUNCTIONS GLM_ENABLE</span></div>
<div class="line"><a name="l00815"></a><span class="lineno">  815</span>&#160;<span class="preprocessor">#       define GLM_DEFAULT = default</span></div>
<div class="line"><a name="l00816"></a><span class="lineno">  816</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00817"></a><span class="lineno">  817</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_DEFAULTED_FUNCTIONS GLM_DISABLE</span></div>
<div class="line"><a name="l00818"></a><span class="lineno">  818</span>&#160;<span class="preprocessor">#       define GLM_DEFAULT</span></div>
<div class="line"><a name="l00819"></a><span class="lineno">  819</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00820"></a><span class="lineno">  820</span>&#160;</div>
<div class="line"><a name="l00822"></a><span class="lineno">  822</span>&#160;<span class="comment">// Configure the use of aligned gentypes</span></div>
<div class="line"><a name="l00823"></a><span class="lineno">  823</span>&#160;</div>
<div class="line"><a name="l00824"></a><span class="lineno">  824</span>&#160;<span class="preprocessor">#ifdef GLM_FORCE_ALIGNED // Legacy define</span></div>
<div class="line"><a name="l00825"></a><span class="lineno">  825</span>&#160;<span class="preprocessor">#       define GLM_FORCE_DEFAULT_ALIGNED_GENTYPES</span></div>
<div class="line"><a name="l00826"></a><span class="lineno">  826</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00827"></a><span class="lineno">  827</span>&#160;</div>
<div class="line"><a name="l00828"></a><span class="lineno">  828</span>&#160;<span class="preprocessor">#ifdef GLM_FORCE_DEFAULT_ALIGNED_GENTYPES</span></div>
<div class="line"><a name="l00829"></a><span class="lineno">  829</span>&#160;<span class="preprocessor">#       define GLM_FORCE_ALIGNED_GENTYPES</span></div>
<div class="line"><a name="l00830"></a><span class="lineno">  830</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00831"></a><span class="lineno">  831</span>&#160;</div>
<div class="line"><a name="l00832"></a><span class="lineno">  832</span>&#160;<span class="preprocessor">#if GLM_HAS_ALIGNOF &amp;&amp; (GLM_LANG &amp; GLM_LANG_CXXMS_FLAG) &amp;&amp; (defined(GLM_FORCE_ALIGNED_GENTYPES) || (GLM_CONFIG_SIMD == GLM_ENABLE))</span></div>
<div class="line"><a name="l00833"></a><span class="lineno">  833</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_ALIGNED_GENTYPES GLM_ENABLE</span></div>
<div class="line"><a name="l00834"></a><span class="lineno">  834</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00835"></a><span class="lineno">  835</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_ALIGNED_GENTYPES GLM_DISABLE</span></div>
<div class="line"><a name="l00836"></a><span class="lineno">  836</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00837"></a><span class="lineno">  837</span>&#160;</div>
<div class="line"><a name="l00839"></a><span class="lineno">  839</span>&#160;<span class="comment">// Configure the use of anonymous structure as implementation detail</span></div>
<div class="line"><a name="l00840"></a><span class="lineno">  840</span>&#160;</div>
<div class="line"><a name="l00841"></a><span class="lineno">  841</span>&#160;<span class="preprocessor">#if ((GLM_CONFIG_SIMD == GLM_ENABLE) || (GLM_CONFIG_SWIZZLE == GLM_SWIZZLE_OPERATOR) || (GLM_CONFIG_ALIGNED_GENTYPES == GLM_ENABLE))</span></div>
<div class="line"><a name="l00842"></a><span class="lineno">  842</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_ANONYMOUS_STRUCT GLM_ENABLE</span></div>
<div class="line"><a name="l00843"></a><span class="lineno">  843</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00844"></a><span class="lineno">  844</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_ANONYMOUS_STRUCT GLM_DISABLE</span></div>
<div class="line"><a name="l00845"></a><span class="lineno">  845</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00846"></a><span class="lineno">  846</span>&#160;</div>
<div class="line"><a name="l00848"></a><span class="lineno">  848</span>&#160;<span class="comment">// Silent warnings</span></div>
<div class="line"><a name="l00849"></a><span class="lineno">  849</span>&#160;</div>
<div class="line"><a name="l00850"></a><span class="lineno">  850</span>&#160;<span class="preprocessor">#ifdef GLM_FORCE_SILENT_WARNINGS</span></div>
<div class="line"><a name="l00851"></a><span class="lineno">  851</span>&#160;<span class="preprocessor">#       define GLM_SILENT_WARNINGS GLM_ENABLE</span></div>
<div class="line"><a name="l00852"></a><span class="lineno">  852</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00853"></a><span class="lineno">  853</span>&#160;<span class="preprocessor">#       define GLM_SILENT_WARNINGS GLM_DISABLE</span></div>
<div class="line"><a name="l00854"></a><span class="lineno">  854</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00855"></a><span class="lineno">  855</span>&#160;</div>
<div class="line"><a name="l00857"></a><span class="lineno">  857</span>&#160;<span class="comment">// Precision</span></div>
<div class="line"><a name="l00858"></a><span class="lineno">  858</span>&#160;</div>
<div class="line"><a name="l00859"></a><span class="lineno">  859</span>&#160;<span class="preprocessor">#define GLM_HIGHP               1</span></div>
<div class="line"><a name="l00860"></a><span class="lineno">  860</span>&#160;<span class="preprocessor">#define GLM_MEDIUMP             2</span></div>
<div class="line"><a name="l00861"></a><span class="lineno">  861</span>&#160;<span class="preprocessor">#define GLM_LOWP                3</span></div>
<div class="line"><a name="l00862"></a><span class="lineno">  862</span>&#160;</div>
<div class="line"><a name="l00863"></a><span class="lineno">  863</span>&#160;<span class="preprocessor">#if defined(GLM_FORCE_PRECISION_HIGHP_BOOL) || defined(GLM_PRECISION_HIGHP_BOOL)</span></div>
<div class="line"><a name="l00864"></a><span class="lineno">  864</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_PRECISION_BOOL                GLM_HIGHP</span></div>
<div class="line"><a name="l00865"></a><span class="lineno">  865</span>&#160;<span class="preprocessor">#elif defined(GLM_FORCE_PRECISION_MEDIUMP_BOOL) || defined(GLM_PRECISION_MEDIUMP_BOOL)</span></div>
<div class="line"><a name="l00866"></a><span class="lineno">  866</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_PRECISION_BOOL                GLM_MEDIUMP</span></div>
<div class="line"><a name="l00867"></a><span class="lineno">  867</span>&#160;<span class="preprocessor">#elif defined(GLM_FORCE_PRECISION_LOWP_BOOL) || defined(GLM_PRECISION_LOWP_BOOL)</span></div>
<div class="line"><a name="l00868"></a><span class="lineno">  868</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_PRECISION_BOOL                GLM_LOWP</span></div>
<div class="line"><a name="l00869"></a><span class="lineno">  869</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00870"></a><span class="lineno">  870</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_PRECISION_BOOL                GLM_HIGHP</span></div>
<div class="line"><a name="l00871"></a><span class="lineno">  871</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00872"></a><span class="lineno">  872</span>&#160;</div>
<div class="line"><a name="l00873"></a><span class="lineno">  873</span>&#160;<span class="preprocessor">#if defined(GLM_FORCE_PRECISION_HIGHP_INT) || defined(GLM_PRECISION_HIGHP_INT)</span></div>
<div class="line"><a name="l00874"></a><span class="lineno">  874</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_PRECISION_INT                 GLM_HIGHP</span></div>
<div class="line"><a name="l00875"></a><span class="lineno">  875</span>&#160;<span class="preprocessor">#elif defined(GLM_FORCE_PRECISION_MEDIUMP_INT) || defined(GLM_PRECISION_MEDIUMP_INT)</span></div>
<div class="line"><a name="l00876"></a><span class="lineno">  876</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_PRECISION_INT                 GLM_MEDIUMP</span></div>
<div class="line"><a name="l00877"></a><span class="lineno">  877</span>&#160;<span class="preprocessor">#elif defined(GLM_FORCE_PRECISION_LOWP_INT) || defined(GLM_PRECISION_LOWP_INT)</span></div>
<div class="line"><a name="l00878"></a><span class="lineno">  878</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_PRECISION_INT                 GLM_LOWP</span></div>
<div class="line"><a name="l00879"></a><span class="lineno">  879</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00880"></a><span class="lineno">  880</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_PRECISION_INT                 GLM_HIGHP</span></div>
<div class="line"><a name="l00881"></a><span class="lineno">  881</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00882"></a><span class="lineno">  882</span>&#160;</div>
<div class="line"><a name="l00883"></a><span class="lineno">  883</span>&#160;<span class="preprocessor">#if defined(GLM_FORCE_PRECISION_HIGHP_UINT) || defined(GLM_PRECISION_HIGHP_UINT)</span></div>
<div class="line"><a name="l00884"></a><span class="lineno">  884</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_PRECISION_UINT                GLM_HIGHP</span></div>
<div class="line"><a name="l00885"></a><span class="lineno">  885</span>&#160;<span class="preprocessor">#elif defined(GLM_FORCE_PRECISION_MEDIUMP_UINT) || defined(GLM_PRECISION_MEDIUMP_UINT)</span></div>
<div class="line"><a name="l00886"></a><span class="lineno">  886</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_PRECISION_UINT                GLM_MEDIUMP</span></div>
<div class="line"><a name="l00887"></a><span class="lineno">  887</span>&#160;<span class="preprocessor">#elif defined(GLM_FORCE_PRECISION_LOWP_UINT) || defined(GLM_PRECISION_LOWP_UINT)</span></div>
<div class="line"><a name="l00888"></a><span class="lineno">  888</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_PRECISION_UINT                GLM_LOWP</span></div>
<div class="line"><a name="l00889"></a><span class="lineno">  889</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00890"></a><span class="lineno">  890</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_PRECISION_UINT                GLM_HIGHP</span></div>
<div class="line"><a name="l00891"></a><span class="lineno">  891</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00892"></a><span class="lineno">  892</span>&#160;</div>
<div class="line"><a name="l00893"></a><span class="lineno">  893</span>&#160;<span class="preprocessor">#if defined(GLM_FORCE_PRECISION_HIGHP_FLOAT) || defined(GLM_PRECISION_HIGHP_FLOAT)</span></div>
<div class="line"><a name="l00894"></a><span class="lineno">  894</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_PRECISION_FLOAT               GLM_HIGHP</span></div>
<div class="line"><a name="l00895"></a><span class="lineno">  895</span>&#160;<span class="preprocessor">#elif defined(GLM_FORCE_PRECISION_MEDIUMP_FLOAT) || defined(GLM_PRECISION_MEDIUMP_FLOAT)</span></div>
<div class="line"><a name="l00896"></a><span class="lineno">  896</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_PRECISION_FLOAT               GLM_MEDIUMP</span></div>
<div class="line"><a name="l00897"></a><span class="lineno">  897</span>&#160;<span class="preprocessor">#elif defined(GLM_FORCE_PRECISION_LOWP_FLOAT) || defined(GLM_PRECISION_LOWP_FLOAT)</span></div>
<div class="line"><a name="l00898"></a><span class="lineno">  898</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_PRECISION_FLOAT               GLM_LOWP</span></div>
<div class="line"><a name="l00899"></a><span class="lineno">  899</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00900"></a><span class="lineno">  900</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_PRECISION_FLOAT               GLM_HIGHP</span></div>
<div class="line"><a name="l00901"></a><span class="lineno">  901</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00902"></a><span class="lineno">  902</span>&#160;</div>
<div class="line"><a name="l00903"></a><span class="lineno">  903</span>&#160;<span class="preprocessor">#if defined(GLM_FORCE_PRECISION_HIGHP_DOUBLE) || defined(GLM_PRECISION_HIGHP_DOUBLE)</span></div>
<div class="line"><a name="l00904"></a><span class="lineno">  904</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_PRECISION_DOUBLE              GLM_HIGHP</span></div>
<div class="line"><a name="l00905"></a><span class="lineno">  905</span>&#160;<span class="preprocessor">#elif defined(GLM_FORCE_PRECISION_MEDIUMP_DOUBLE) || defined(GLM_PRECISION_MEDIUMP_DOUBLE)</span></div>
<div class="line"><a name="l00906"></a><span class="lineno">  906</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_PRECISION_DOUBLE              GLM_MEDIUMP</span></div>
<div class="line"><a name="l00907"></a><span class="lineno">  907</span>&#160;<span class="preprocessor">#elif defined(GLM_FORCE_PRECISION_LOWP_DOUBLE) || defined(GLM_PRECISION_LOWP_DOUBLE)</span></div>
<div class="line"><a name="l00908"></a><span class="lineno">  908</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_PRECISION_DOUBLE              GLM_LOWP</span></div>
<div class="line"><a name="l00909"></a><span class="lineno">  909</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00910"></a><span class="lineno">  910</span>&#160;<span class="preprocessor">#       define GLM_CONFIG_PRECISION_DOUBLE              GLM_HIGHP</span></div>
<div class="line"><a name="l00911"></a><span class="lineno">  911</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00912"></a><span class="lineno">  912</span>&#160;</div>
<div class="line"><a name="l00914"></a><span class="lineno">  914</span>&#160;<span class="comment">// Check inclusions of different versions of GLM</span></div>
<div class="line"><a name="l00915"></a><span class="lineno">  915</span>&#160;</div>
<div class="line"><a name="l00916"></a><span class="lineno">  916</span>&#160;<span class="preprocessor">#elif ((GLM_SETUP_INCLUDED != GLM_VERSION) &amp;&amp; !defined(GLM_FORCE_IGNORE_VERSION))</span></div>
<div class="line"><a name="l00917"></a><span class="lineno">  917</span>&#160;<span class="preprocessor">#       error &quot;GLM error: A different version of GLM is already included. Define GLM_FORCE_IGNORE_VERSION before including GLM headers to ignore this error.&quot;</span></div>
<div class="line"><a name="l00918"></a><span class="lineno">  918</span>&#160;<span class="preprocessor">#elif GLM_SETUP_INCLUDED == GLM_VERSION</span></div>
<div class="line"><a name="l00919"></a><span class="lineno">  919</span>&#160;</div>
<div class="line"><a name="l00921"></a><span class="lineno">  921</span>&#160;<span class="comment">// Messages</span></div>
<div class="line"><a name="l00922"></a><span class="lineno">  922</span>&#160;</div>
<div class="line"><a name="l00923"></a><span class="lineno">  923</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_MESSAGE_DISPLAYED)</span></div>
<div class="line"><a name="l00924"></a><span class="lineno">  924</span>&#160;<span class="preprocessor">#       define GLM_MESSAGE_DISPLAYED</span></div>
<div class="line"><a name="l00925"></a><span class="lineno">  925</span>&#160;<span class="preprocessor">#               define GLM_STR_HELPER(x) #x</span></div>
<div class="line"><a name="l00926"></a><span class="lineno">  926</span>&#160;<span class="preprocessor">#               define GLM_STR(x) GLM_STR_HELPER(x)</span></div>
<div class="line"><a name="l00927"></a><span class="lineno">  927</span>&#160;</div>
<div class="line"><a name="l00928"></a><span class="lineno">  928</span>&#160;        <span class="comment">// Report GLM version</span></div>
<div class="line"><a name="l00929"></a><span class="lineno">  929</span>&#160;<span class="preprocessor">#               pragma message (GLM_STR(GLM_VERSION_MESSAGE))</span></div>
<div class="line"><a name="l00930"></a><span class="lineno">  930</span>&#160;</div>
<div class="line"><a name="l00931"></a><span class="lineno">  931</span>&#160;        <span class="comment">// Report C++ language</span></div>
<div class="line"><a name="l00932"></a><span class="lineno">  932</span>&#160;<span class="preprocessor">#       if (GLM_LANG &amp; GLM_LANG_CXX2A_FLAG) &amp;&amp; (GLM_LANG &amp; GLM_LANG_EXT)</span></div>
<div class="line"><a name="l00933"></a><span class="lineno">  933</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: C++ 2A with extensions&quot;)</span></div>
<div class="line"><a name="l00934"></a><span class="lineno">  934</span>&#160;<span class="preprocessor">#       elif (GLM_LANG &amp; GLM_LANG_CXX2A_FLAG)</span></div>
<div class="line"><a name="l00935"></a><span class="lineno">  935</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: C++ 2A&quot;)</span></div>
<div class="line"><a name="l00936"></a><span class="lineno">  936</span>&#160;<span class="preprocessor">#       elif (GLM_LANG &amp; GLM_LANG_CXX17_FLAG) &amp;&amp; (GLM_LANG &amp; GLM_LANG_EXT)</span></div>
<div class="line"><a name="l00937"></a><span class="lineno">  937</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: C++ 17 with extensions&quot;)</span></div>
<div class="line"><a name="l00938"></a><span class="lineno">  938</span>&#160;<span class="preprocessor">#       elif (GLM_LANG &amp; GLM_LANG_CXX17_FLAG)</span></div>
<div class="line"><a name="l00939"></a><span class="lineno">  939</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: C++ 17&quot;)</span></div>
<div class="line"><a name="l00940"></a><span class="lineno">  940</span>&#160;<span class="preprocessor">#       elif (GLM_LANG &amp; GLM_LANG_CXX14_FLAG) &amp;&amp; (GLM_LANG &amp; GLM_LANG_EXT)</span></div>
<div class="line"><a name="l00941"></a><span class="lineno">  941</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: C++ 14 with extensions&quot;)</span></div>
<div class="line"><a name="l00942"></a><span class="lineno">  942</span>&#160;<span class="preprocessor">#       elif (GLM_LANG &amp; GLM_LANG_CXX14_FLAG)</span></div>
<div class="line"><a name="l00943"></a><span class="lineno">  943</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: C++ 14&quot;)</span></div>
<div class="line"><a name="l00944"></a><span class="lineno">  944</span>&#160;<span class="preprocessor">#       elif (GLM_LANG &amp; GLM_LANG_CXX11_FLAG) &amp;&amp; (GLM_LANG &amp; GLM_LANG_EXT)</span></div>
<div class="line"><a name="l00945"></a><span class="lineno">  945</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: C++ 11 with extensions&quot;)</span></div>
<div class="line"><a name="l00946"></a><span class="lineno">  946</span>&#160;<span class="preprocessor">#       elif (GLM_LANG &amp; GLM_LANG_CXX11_FLAG)</span></div>
<div class="line"><a name="l00947"></a><span class="lineno">  947</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: C++ 11&quot;)</span></div>
<div class="line"><a name="l00948"></a><span class="lineno">  948</span>&#160;<span class="preprocessor">#       elif (GLM_LANG &amp; GLM_LANG_CXX0X_FLAG) &amp;&amp; (GLM_LANG &amp; GLM_LANG_EXT)</span></div>
<div class="line"><a name="l00949"></a><span class="lineno">  949</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: C++ 0x with extensions&quot;)</span></div>
<div class="line"><a name="l00950"></a><span class="lineno">  950</span>&#160;<span class="preprocessor">#       elif (GLM_LANG &amp; GLM_LANG_CXX0X_FLAG)</span></div>
<div class="line"><a name="l00951"></a><span class="lineno">  951</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: C++ 0x&quot;)</span></div>
<div class="line"><a name="l00952"></a><span class="lineno">  952</span>&#160;<span class="preprocessor">#       elif (GLM_LANG &amp; GLM_LANG_CXX03_FLAG) &amp;&amp; (GLM_LANG &amp; GLM_LANG_EXT)</span></div>
<div class="line"><a name="l00953"></a><span class="lineno">  953</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: C++ 03 with extensions&quot;)</span></div>
<div class="line"><a name="l00954"></a><span class="lineno">  954</span>&#160;<span class="preprocessor">#       elif (GLM_LANG &amp; GLM_LANG_CXX03_FLAG)</span></div>
<div class="line"><a name="l00955"></a><span class="lineno">  955</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: C++ 03&quot;)</span></div>
<div class="line"><a name="l00956"></a><span class="lineno">  956</span>&#160;<span class="preprocessor">#       elif (GLM_LANG &amp; GLM_LANG_CXX98_FLAG) &amp;&amp; (GLM_LANG &amp; GLM_LANG_EXT)</span></div>
<div class="line"><a name="l00957"></a><span class="lineno">  957</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: C++ 98 with extensions&quot;)</span></div>
<div class="line"><a name="l00958"></a><span class="lineno">  958</span>&#160;<span class="preprocessor">#       elif (GLM_LANG &amp; GLM_LANG_CXX98_FLAG)</span></div>
<div class="line"><a name="l00959"></a><span class="lineno">  959</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: C++ 98&quot;)</span></div>
<div class="line"><a name="l00960"></a><span class="lineno">  960</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00961"></a><span class="lineno">  961</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: C++ language undetected&quot;)</span></div>
<div class="line"><a name="l00962"></a><span class="lineno">  962</span>&#160;<span class="preprocessor">#       endif//GLM_LANG</span></div>
<div class="line"><a name="l00963"></a><span class="lineno">  963</span>&#160;</div>
<div class="line"><a name="l00964"></a><span class="lineno">  964</span>&#160;        <span class="comment">// Report compiler detection</span></div>
<div class="line"><a name="l00965"></a><span class="lineno">  965</span>&#160;<span class="preprocessor">#       if GLM_COMPILER &amp; GLM_COMPILER_CUDA</span></div>
<div class="line"><a name="l00966"></a><span class="lineno">  966</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: CUDA compiler detected&quot;)</span></div>
<div class="line"><a name="l00967"></a><span class="lineno">  967</span>&#160;<span class="preprocessor">#       elif GLM_COMPILER &amp; GLM_COMPILER_VC</span></div>
<div class="line"><a name="l00968"></a><span class="lineno">  968</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: Visual C++ compiler detected&quot;)</span></div>
<div class="line"><a name="l00969"></a><span class="lineno">  969</span>&#160;<span class="preprocessor">#       elif GLM_COMPILER &amp; GLM_COMPILER_CLANG</span></div>
<div class="line"><a name="l00970"></a><span class="lineno">  970</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: Clang compiler detected&quot;)</span></div>
<div class="line"><a name="l00971"></a><span class="lineno">  971</span>&#160;<span class="preprocessor">#       elif GLM_COMPILER &amp; GLM_COMPILER_INTEL</span></div>
<div class="line"><a name="l00972"></a><span class="lineno">  972</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: Intel Compiler detected&quot;)</span></div>
<div class="line"><a name="l00973"></a><span class="lineno">  973</span>&#160;<span class="preprocessor">#       elif GLM_COMPILER &amp; GLM_COMPILER_GCC</span></div>
<div class="line"><a name="l00974"></a><span class="lineno">  974</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GCC compiler detected&quot;)</span></div>
<div class="line"><a name="l00975"></a><span class="lineno">  975</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00976"></a><span class="lineno">  976</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: Compiler not detected&quot;)</span></div>
<div class="line"><a name="l00977"></a><span class="lineno">  977</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00978"></a><span class="lineno">  978</span>&#160;</div>
<div class="line"><a name="l00979"></a><span class="lineno">  979</span>&#160;        <span class="comment">// Report build target</span></div>
<div class="line"><a name="l00980"></a><span class="lineno">  980</span>&#160;<span class="preprocessor">#       if (GLM_ARCH &amp; GLM_ARCH_AVX2_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_64)</span></div>
<div class="line"><a name="l00981"></a><span class="lineno">  981</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: x86 64 bits with AVX2 instruction set build target&quot;)</span></div>
<div class="line"><a name="l00982"></a><span class="lineno">  982</span>&#160;<span class="preprocessor">#       elif (GLM_ARCH &amp; GLM_ARCH_AVX2_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_32)</span></div>
<div class="line"><a name="l00983"></a><span class="lineno">  983</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: x86 32 bits with AVX2 instruction set build target&quot;)</span></div>
<div class="line"><a name="l00984"></a><span class="lineno">  984</span>&#160;</div>
<div class="line"><a name="l00985"></a><span class="lineno">  985</span>&#160;<span class="preprocessor">#       elif (GLM_ARCH &amp; GLM_ARCH_AVX_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_64)</span></div>
<div class="line"><a name="l00986"></a><span class="lineno">  986</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: x86 64 bits with AVX instruction set build target&quot;)</span></div>
<div class="line"><a name="l00987"></a><span class="lineno">  987</span>&#160;<span class="preprocessor">#       elif (GLM_ARCH &amp; GLM_ARCH_AVX_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_32)</span></div>
<div class="line"><a name="l00988"></a><span class="lineno">  988</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: x86 32 bits with AVX instruction set build target&quot;)</span></div>
<div class="line"><a name="l00989"></a><span class="lineno">  989</span>&#160;</div>
<div class="line"><a name="l00990"></a><span class="lineno">  990</span>&#160;<span class="preprocessor">#       elif (GLM_ARCH &amp; GLM_ARCH_SSE42_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_64)</span></div>
<div class="line"><a name="l00991"></a><span class="lineno">  991</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: x86 64 bits with SSE4.2 instruction set build target&quot;)</span></div>
<div class="line"><a name="l00992"></a><span class="lineno">  992</span>&#160;<span class="preprocessor">#       elif (GLM_ARCH &amp; GLM_ARCH_SSE42_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_32)</span></div>
<div class="line"><a name="l00993"></a><span class="lineno">  993</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: x86 32 bits with SSE4.2 instruction set build target&quot;)</span></div>
<div class="line"><a name="l00994"></a><span class="lineno">  994</span>&#160;</div>
<div class="line"><a name="l00995"></a><span class="lineno">  995</span>&#160;<span class="preprocessor">#       elif (GLM_ARCH &amp; GLM_ARCH_SSE41_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_64)</span></div>
<div class="line"><a name="l00996"></a><span class="lineno">  996</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: x86 64 bits with SSE4.1 instruction set build target&quot;)</span></div>
<div class="line"><a name="l00997"></a><span class="lineno">  997</span>&#160;<span class="preprocessor">#       elif (GLM_ARCH &amp; GLM_ARCH_SSE41_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_32)</span></div>
<div class="line"><a name="l00998"></a><span class="lineno">  998</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: x86 32 bits with SSE4.1 instruction set build target&quot;)</span></div>
<div class="line"><a name="l00999"></a><span class="lineno">  999</span>&#160;</div>
<div class="line"><a name="l01000"></a><span class="lineno"> 1000</span>&#160;<span class="preprocessor">#       elif (GLM_ARCH &amp; GLM_ARCH_SSSE3_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_64)</span></div>
<div class="line"><a name="l01001"></a><span class="lineno"> 1001</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: x86 64 bits with SSSE3 instruction set build target&quot;)</span></div>
<div class="line"><a name="l01002"></a><span class="lineno"> 1002</span>&#160;<span class="preprocessor">#       elif (GLM_ARCH &amp; GLM_ARCH_SSSE3_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_32)</span></div>
<div class="line"><a name="l01003"></a><span class="lineno"> 1003</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: x86 32 bits with SSSE3 instruction set build target&quot;)</span></div>
<div class="line"><a name="l01004"></a><span class="lineno"> 1004</span>&#160;</div>
<div class="line"><a name="l01005"></a><span class="lineno"> 1005</span>&#160;<span class="preprocessor">#       elif (GLM_ARCH &amp; GLM_ARCH_SSE3_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_64)</span></div>
<div class="line"><a name="l01006"></a><span class="lineno"> 1006</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: x86 64 bits with SSE3 instruction set build target&quot;)</span></div>
<div class="line"><a name="l01007"></a><span class="lineno"> 1007</span>&#160;<span class="preprocessor">#       elif (GLM_ARCH &amp; GLM_ARCH_SSE3_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_32)</span></div>
<div class="line"><a name="l01008"></a><span class="lineno"> 1008</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: x86 32 bits with SSE3 instruction set build target&quot;)</span></div>
<div class="line"><a name="l01009"></a><span class="lineno"> 1009</span>&#160;</div>
<div class="line"><a name="l01010"></a><span class="lineno"> 1010</span>&#160;<span class="preprocessor">#       elif (GLM_ARCH &amp; GLM_ARCH_SSE2_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_64)</span></div>
<div class="line"><a name="l01011"></a><span class="lineno"> 1011</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: x86 64 bits with SSE2 instruction set build target&quot;)</span></div>
<div class="line"><a name="l01012"></a><span class="lineno"> 1012</span>&#160;<span class="preprocessor">#       elif (GLM_ARCH &amp; GLM_ARCH_SSE2_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_32)</span></div>
<div class="line"><a name="l01013"></a><span class="lineno"> 1013</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: x86 32 bits with SSE2 instruction set build target&quot;)</span></div>
<div class="line"><a name="l01014"></a><span class="lineno"> 1014</span>&#160;</div>
<div class="line"><a name="l01015"></a><span class="lineno"> 1015</span>&#160;<span class="preprocessor">#       elif (GLM_ARCH &amp; GLM_ARCH_X86_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_64)</span></div>
<div class="line"><a name="l01016"></a><span class="lineno"> 1016</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: x86 64 bits build target&quot;)</span></div>
<div class="line"><a name="l01017"></a><span class="lineno"> 1017</span>&#160;<span class="preprocessor">#       elif (GLM_ARCH &amp; GLM_ARCH_X86_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_32)</span></div>
<div class="line"><a name="l01018"></a><span class="lineno"> 1018</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: x86 32 bits build target&quot;)</span></div>
<div class="line"><a name="l01019"></a><span class="lineno"> 1019</span>&#160;</div>
<div class="line"><a name="l01020"></a><span class="lineno"> 1020</span>&#160;<span class="preprocessor">#       elif (GLM_ARCH &amp; GLM_ARCH_NEON_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_64)</span></div>
<div class="line"><a name="l01021"></a><span class="lineno"> 1021</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: ARM 64 bits with Neon instruction set build target&quot;)</span></div>
<div class="line"><a name="l01022"></a><span class="lineno"> 1022</span>&#160;<span class="preprocessor">#       elif (GLM_ARCH &amp; GLM_ARCH_NEON_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_32)</span></div>
<div class="line"><a name="l01023"></a><span class="lineno"> 1023</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: ARM 32 bits with Neon instruction set build target&quot;)</span></div>
<div class="line"><a name="l01024"></a><span class="lineno"> 1024</span>&#160;</div>
<div class="line"><a name="l01025"></a><span class="lineno"> 1025</span>&#160;<span class="preprocessor">#       elif (GLM_ARCH &amp; GLM_ARCH_ARM_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_64)</span></div>
<div class="line"><a name="l01026"></a><span class="lineno"> 1026</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: ARM 64 bits build target&quot;)</span></div>
<div class="line"><a name="l01027"></a><span class="lineno"> 1027</span>&#160;<span class="preprocessor">#       elif (GLM_ARCH &amp; GLM_ARCH_ARM_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_32)</span></div>
<div class="line"><a name="l01028"></a><span class="lineno"> 1028</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: ARM 32 bits build target&quot;)</span></div>
<div class="line"><a name="l01029"></a><span class="lineno"> 1029</span>&#160;</div>
<div class="line"><a name="l01030"></a><span class="lineno"> 1030</span>&#160;<span class="preprocessor">#       elif (GLM_ARCH &amp; GLM_ARCH_MIPS_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_64)</span></div>
<div class="line"><a name="l01031"></a><span class="lineno"> 1031</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: MIPS 64 bits build target&quot;)</span></div>
<div class="line"><a name="l01032"></a><span class="lineno"> 1032</span>&#160;<span class="preprocessor">#       elif (GLM_ARCH &amp; GLM_ARCH_MIPS_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_32)</span></div>
<div class="line"><a name="l01033"></a><span class="lineno"> 1033</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: MIPS 32 bits build target&quot;)</span></div>
<div class="line"><a name="l01034"></a><span class="lineno"> 1034</span>&#160;</div>
<div class="line"><a name="l01035"></a><span class="lineno"> 1035</span>&#160;<span class="preprocessor">#       elif (GLM_ARCH &amp; GLM_ARCH_PPC_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_64)</span></div>
<div class="line"><a name="l01036"></a><span class="lineno"> 1036</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: PowerPC 64 bits build target&quot;)</span></div>
<div class="line"><a name="l01037"></a><span class="lineno"> 1037</span>&#160;<span class="preprocessor">#       elif (GLM_ARCH &amp; GLM_ARCH_PPC_BIT) &amp;&amp; (GLM_MODEL == GLM_MODEL_32)</span></div>
<div class="line"><a name="l01038"></a><span class="lineno"> 1038</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: PowerPC 32 bits build target&quot;)</span></div>
<div class="line"><a name="l01039"></a><span class="lineno"> 1039</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l01040"></a><span class="lineno"> 1040</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: Unknown build target&quot;)</span></div>
<div class="line"><a name="l01041"></a><span class="lineno"> 1041</span>&#160;<span class="preprocessor">#       endif//GLM_ARCH</span></div>
<div class="line"><a name="l01042"></a><span class="lineno"> 1042</span>&#160;</div>
<div class="line"><a name="l01043"></a><span class="lineno"> 1043</span>&#160;        <span class="comment">// Report platform name</span></div>
<div class="line"><a name="l01044"></a><span class="lineno"> 1044</span>&#160;<span class="preprocessor">#       if(GLM_PLATFORM &amp; GLM_PLATFORM_QNXNTO)</span></div>
<div class="line"><a name="l01045"></a><span class="lineno"> 1045</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: QNX platform detected&quot;)</span></div>
<div class="line"><a name="l01046"></a><span class="lineno"> 1046</span>&#160;<span class="comment">//#     elif(GLM_PLATFORM &amp; GLM_PLATFORM_IOS)</span></div>
<div class="line"><a name="l01047"></a><span class="lineno"> 1047</span>&#160;<span class="comment">//#             pragma message(&quot;GLM: iOS platform detected&quot;)</span></div>
<div class="line"><a name="l01048"></a><span class="lineno"> 1048</span>&#160;<span class="preprocessor">#       elif(GLM_PLATFORM &amp; GLM_PLATFORM_APPLE)</span></div>
<div class="line"><a name="l01049"></a><span class="lineno"> 1049</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: Apple platform detected&quot;)</span></div>
<div class="line"><a name="l01050"></a><span class="lineno"> 1050</span>&#160;<span class="preprocessor">#       elif(GLM_PLATFORM &amp; GLM_PLATFORM_WINCE)</span></div>
<div class="line"><a name="l01051"></a><span class="lineno"> 1051</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: WinCE platform detected&quot;)</span></div>
<div class="line"><a name="l01052"></a><span class="lineno"> 1052</span>&#160;<span class="preprocessor">#       elif(GLM_PLATFORM &amp; GLM_PLATFORM_WINDOWS)</span></div>
<div class="line"><a name="l01053"></a><span class="lineno"> 1053</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: Windows platform detected&quot;)</span></div>
<div class="line"><a name="l01054"></a><span class="lineno"> 1054</span>&#160;<span class="preprocessor">#       elif(GLM_PLATFORM &amp; GLM_PLATFORM_CHROME_NACL)</span></div>
<div class="line"><a name="l01055"></a><span class="lineno"> 1055</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: Native Client detected&quot;)</span></div>
<div class="line"><a name="l01056"></a><span class="lineno"> 1056</span>&#160;<span class="preprocessor">#       elif(GLM_PLATFORM &amp; GLM_PLATFORM_ANDROID)</span></div>
<div class="line"><a name="l01057"></a><span class="lineno"> 1057</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: Android platform detected&quot;)</span></div>
<div class="line"><a name="l01058"></a><span class="lineno"> 1058</span>&#160;<span class="preprocessor">#       elif(GLM_PLATFORM &amp; GLM_PLATFORM_LINUX)</span></div>
<div class="line"><a name="l01059"></a><span class="lineno"> 1059</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: Linux platform detected&quot;)</span></div>
<div class="line"><a name="l01060"></a><span class="lineno"> 1060</span>&#160;<span class="preprocessor">#       elif(GLM_PLATFORM &amp; GLM_PLATFORM_UNIX)</span></div>
<div class="line"><a name="l01061"></a><span class="lineno"> 1061</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: UNIX platform detected&quot;)</span></div>
<div class="line"><a name="l01062"></a><span class="lineno"> 1062</span>&#160;<span class="preprocessor">#       elif(GLM_PLATFORM &amp; GLM_PLATFORM_UNKNOWN)</span></div>
<div class="line"><a name="l01063"></a><span class="lineno"> 1063</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: platform unknown&quot;)</span></div>
<div class="line"><a name="l01064"></a><span class="lineno"> 1064</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l01065"></a><span class="lineno"> 1065</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: platform not detected&quot;)</span></div>
<div class="line"><a name="l01066"></a><span class="lineno"> 1066</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l01067"></a><span class="lineno"> 1067</span>&#160;</div>
<div class="line"><a name="l01068"></a><span class="lineno"> 1068</span>&#160;        <span class="comment">// Report whether only xyzw component are used</span></div>
<div class="line"><a name="l01069"></a><span class="lineno"> 1069</span>&#160;<span class="preprocessor">#       if defined GLM_FORCE_XYZW_ONLY</span></div>
<div class="line"><a name="l01070"></a><span class="lineno"> 1070</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_FORCE_XYZW_ONLY is defined. Only x, y, z and w component are available in vector type. This define disables swizzle operators and SIMD instruction sets.&quot;)</span></div>
<div class="line"><a name="l01071"></a><span class="lineno"> 1071</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l01072"></a><span class="lineno"> 1072</span>&#160;</div>
<div class="line"><a name="l01073"></a><span class="lineno"> 1073</span>&#160;        <span class="comment">// Report swizzle operator support</span></div>
<div class="line"><a name="l01074"></a><span class="lineno"> 1074</span>&#160;<span class="preprocessor">#       if GLM_CONFIG_SWIZZLE == GLM_SWIZZLE_OPERATOR</span></div>
<div class="line"><a name="l01075"></a><span class="lineno"> 1075</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_FORCE_SWIZZLE is defined, swizzling operators enabled.&quot;)</span></div>
<div class="line"><a name="l01076"></a><span class="lineno"> 1076</span>&#160;<span class="preprocessor">#       elif GLM_CONFIG_SWIZZLE == GLM_SWIZZLE_FUNCTION</span></div>
<div class="line"><a name="l01077"></a><span class="lineno"> 1077</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_FORCE_SWIZZLE is defined, swizzling functions enabled. Enable compiler C++ language extensions to enable swizzle operators.&quot;)</span></div>
<div class="line"><a name="l01078"></a><span class="lineno"> 1078</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l01079"></a><span class="lineno"> 1079</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_FORCE_SWIZZLE is undefined. swizzling functions or operators are disabled.&quot;)</span></div>
<div class="line"><a name="l01080"></a><span class="lineno"> 1080</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l01081"></a><span class="lineno"> 1081</span>&#160;</div>
<div class="line"><a name="l01082"></a><span class="lineno"> 1082</span>&#160;        <span class="comment">// Report .length() type</span></div>
<div class="line"><a name="l01083"></a><span class="lineno"> 1083</span>&#160;<span class="preprocessor">#       if GLM_CONFIG_LENGTH_TYPE == GLM_LENGTH_SIZE_T</span></div>
<div class="line"><a name="l01084"></a><span class="lineno"> 1084</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_FORCE_SIZE_T_LENGTH is defined. .length() returns a glm::length_t, a typedef of std::size_t.&quot;)</span></div>
<div class="line"><a name="l01085"></a><span class="lineno"> 1085</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l01086"></a><span class="lineno"> 1086</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_FORCE_SIZE_T_LENGTH is undefined. .length() returns a glm::length_t, a typedef of int following GLSL.&quot;)</span></div>
<div class="line"><a name="l01087"></a><span class="lineno"> 1087</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l01088"></a><span class="lineno"> 1088</span>&#160;</div>
<div class="line"><a name="l01089"></a><span class="lineno"> 1089</span>&#160;<span class="preprocessor">#       if GLM_CONFIG_UNRESTRICTED_GENTYPE == GLM_ENABLE</span></div>
<div class="line"><a name="l01090"></a><span class="lineno"> 1090</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_FORCE_UNRESTRICTED_GENTYPE is defined. Removes GLSL restrictions on valid function genTypes.&quot;)</span></div>
<div class="line"><a name="l01091"></a><span class="lineno"> 1091</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l01092"></a><span class="lineno"> 1092</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_FORCE_UNRESTRICTED_GENTYPE is undefined. Follows strictly GLSL on valid function genTypes.&quot;)</span></div>
<div class="line"><a name="l01093"></a><span class="lineno"> 1093</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l01094"></a><span class="lineno"> 1094</span>&#160;</div>
<div class="line"><a name="l01095"></a><span class="lineno"> 1095</span>&#160;<span class="preprocessor">#       if GLM_SILENT_WARNINGS == GLM_ENABLE</span></div>
<div class="line"><a name="l01096"></a><span class="lineno"> 1096</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_FORCE_SILENT_WARNINGS is defined. Ignores C++ warnings from using C++ language extensions.&quot;)</span></div>
<div class="line"><a name="l01097"></a><span class="lineno"> 1097</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l01098"></a><span class="lineno"> 1098</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_FORCE_SILENT_WARNINGS is undefined. Shows C++ warnings from using C++ language extensions.&quot;)</span></div>
<div class="line"><a name="l01099"></a><span class="lineno"> 1099</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l01100"></a><span class="lineno"> 1100</span>&#160;</div>
<div class="line"><a name="l01101"></a><span class="lineno"> 1101</span>&#160;<span class="preprocessor">#       ifdef GLM_FORCE_SINGLE_ONLY</span></div>
<div class="line"><a name="l01102"></a><span class="lineno"> 1102</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_FORCE_SINGLE_ONLY is defined. Using only single precision floating-point types.&quot;)</span></div>
<div class="line"><a name="l01103"></a><span class="lineno"> 1103</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l01104"></a><span class="lineno"> 1104</span>&#160;</div>
<div class="line"><a name="l01105"></a><span class="lineno"> 1105</span>&#160;<span class="preprocessor">#       if defined(GLM_FORCE_ALIGNED_GENTYPES) &amp;&amp; (GLM_CONFIG_ALIGNED_GENTYPES == GLM_ENABLE)</span></div>
<div class="line"><a name="l01106"></a><span class="lineno"> 1106</span>&#160;<span class="preprocessor">#               undef GLM_FORCE_ALIGNED_GENTYPES</span></div>
<div class="line"><a name="l01107"></a><span class="lineno"> 1107</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_FORCE_ALIGNED_GENTYPES is defined, allowing aligned types. This prevents the use of C++ constexpr.&quot;)</span></div>
<div class="line"><a name="l01108"></a><span class="lineno"> 1108</span>&#160;<span class="preprocessor">#       elif defined(GLM_FORCE_ALIGNED_GENTYPES) &amp;&amp; (GLM_CONFIG_ALIGNED_GENTYPES == GLM_DISABLE)</span></div>
<div class="line"><a name="l01109"></a><span class="lineno"> 1109</span>&#160;<span class="preprocessor">#               undef GLM_FORCE_ALIGNED_GENTYPES</span></div>
<div class="line"><a name="l01110"></a><span class="lineno"> 1110</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_FORCE_ALIGNED_GENTYPES is defined but is disabled. It requires C++11 and language extensions.&quot;)</span></div>
<div class="line"><a name="l01111"></a><span class="lineno"> 1111</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l01112"></a><span class="lineno"> 1112</span>&#160;</div>
<div class="line"><a name="l01113"></a><span class="lineno"> 1113</span>&#160;<span class="preprocessor">#       if defined(GLM_FORCE_DEFAULT_ALIGNED_GENTYPES)</span></div>
<div class="line"><a name="l01114"></a><span class="lineno"> 1114</span>&#160;<span class="preprocessor">#               if GLM_CONFIG_ALIGNED_GENTYPES == GLM_DISABLE</span></div>
<div class="line"><a name="l01115"></a><span class="lineno"> 1115</span>&#160;<span class="preprocessor">#                       undef GLM_FORCE_DEFAULT_ALIGNED_GENTYPES</span></div>
<div class="line"><a name="l01116"></a><span class="lineno"> 1116</span>&#160;<span class="preprocessor">#                       pragma message(&quot;GLM: GLM_FORCE_DEFAULT_ALIGNED_GENTYPES is defined but is disabled. It requires C++11 and language extensions.&quot;)</span></div>
<div class="line"><a name="l01117"></a><span class="lineno"> 1117</span>&#160;<span class="preprocessor">#               elif GLM_CONFIG_ALIGNED_GENTYPES == GLM_ENABLE</span></div>
<div class="line"><a name="l01118"></a><span class="lineno"> 1118</span>&#160;<span class="preprocessor">#                       pragma message(&quot;GLM: GLM_FORCE_DEFAULT_ALIGNED_GENTYPES is defined. All gentypes (e.g. vec3) will be aligned and padded by default.&quot;)</span></div>
<div class="line"><a name="l01119"></a><span class="lineno"> 1119</span>&#160;<span class="preprocessor">#               endif</span></div>
<div class="line"><a name="l01120"></a><span class="lineno"> 1120</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l01121"></a><span class="lineno"> 1121</span>&#160;</div>
<div class="line"><a name="l01122"></a><span class="lineno"> 1122</span>&#160;<span class="preprocessor">#       if GLM_CONFIG_CLIP_CONTROL &amp; GLM_CLIP_CONTROL_ZO_BIT</span></div>
<div class="line"><a name="l01123"></a><span class="lineno"> 1123</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_FORCE_DEPTH_ZERO_TO_ONE is defined. Using zero to one depth clip space.&quot;)</span></div>
<div class="line"><a name="l01124"></a><span class="lineno"> 1124</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l01125"></a><span class="lineno"> 1125</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_FORCE_DEPTH_ZERO_TO_ONE is undefined. Using negative one to one depth clip space.&quot;)</span></div>
<div class="line"><a name="l01126"></a><span class="lineno"> 1126</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l01127"></a><span class="lineno"> 1127</span>&#160;</div>
<div class="line"><a name="l01128"></a><span class="lineno"> 1128</span>&#160;<span class="preprocessor">#       if GLM_CONFIG_CLIP_CONTROL &amp; GLM_CLIP_CONTROL_LH_BIT</span></div>
<div class="line"><a name="l01129"></a><span class="lineno"> 1129</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_FORCE_LEFT_HANDED is defined. Using left handed coordinate system.&quot;)</span></div>
<div class="line"><a name="l01130"></a><span class="lineno"> 1130</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l01131"></a><span class="lineno"> 1131</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_FORCE_LEFT_HANDED is undefined. Using right handed coordinate system.&quot;)</span></div>
<div class="line"><a name="l01132"></a><span class="lineno"> 1132</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l01133"></a><span class="lineno"> 1133</span>&#160;<span class="preprocessor">#endif//GLM_MESSAGES</span></div>
<div class="line"><a name="l01134"></a><span class="lineno"> 1134</span>&#160;</div>
<div class="line"><a name="l01135"></a><span class="lineno"> 1135</span>&#160;<span class="preprocessor">#endif//GLM_SETUP_INCLUDED</span></div>
<div class="ttc" id="a00304_html_ga322a7d7d2c2c68994dc872a33de63c61"><div class="ttname"><a href="a00304.html#ga322a7d7d2c2c68994dc872a33de63c61">glm::int64_t</a></div><div class="ttdeci">int64 int64_t</div><div class="ttdoc">64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00085">fwd.hpp:85</a></div></div>
<div class="ttc" id="a00304_html_ga4bf09d8838a86866b39ee6e109341645"><div class="ttname"><a href="a00304.html#ga4bf09d8838a86866b39ee6e109341645">glm::int8_t</a></div><div class="ttdeci">int8 int8_t</div><div class="ttdoc">8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00043">fwd.hpp:43</a></div></div>
<div class="ttc" id="a00304_html_ga2171d9dc1fefb1c82e2817f45b622eac"><div class="ttname"><a href="a00304.html#ga2171d9dc1fefb1c82e2817f45b622eac">glm::uint32_t</a></div><div class="ttdeci">uint32 uint32_t</div><div class="ttdoc">Default qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00129">fwd.hpp:129</a></div></div>
<div class="ttc" id="a00304_html_ga91f91f411080c37730856ff5887f5bcf"><div class="ttname"><a href="a00304.html#ga91f91f411080c37730856ff5887f5bcf">glm::uint16_t</a></div><div class="ttdeci">uint16 uint16_t</div><div class="ttdoc">Default qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00115">fwd.hpp:115</a></div></div>
<div class="ttc" id="a00304_html_ga28d97808322d3c92186e4a0c067d7e8e"><div class="ttname"><a href="a00304.html#ga28d97808322d3c92186e4a0c067d7e8e">glm::uint8_t</a></div><div class="ttdeci">uint8 uint8_t</div><div class="ttdoc">Default qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00101">fwd.hpp:101</a></div></div>
<div class="ttc" id="a00304_html_ga3999d3e7ff22025c16ddb601e14dfdee"><div class="ttname"><a href="a00304.html#ga3999d3e7ff22025c16ddb601e14dfdee">glm::uint64_t</a></div><div class="ttdeci">uint64 uint64_t</div><div class="ttdoc">Default qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00143">fwd.hpp:143</a></div></div>
<div class="ttc" id="a00304_html_gae8f5e3e964ca2ae240adc2c0d74adede"><div class="ttname"><a href="a00304.html#gae8f5e3e964ca2ae240adc2c0d74adede">glm::int16_t</a></div><div class="ttdeci">int16 int16_t</div><div class="ttdoc">16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00057">fwd.hpp:57</a></div></div>
<div class="ttc" id="a00240_html"><div class="ttname"><a href="a00240.html">std</a></div><div class="ttdef"><b>Definition:</b> <a href="a00040_source.html#l00049">hash.hpp:49</a></div></div>
<div class="ttc" id="a00304_html_ga042ef09ff2f0cb24a36f541bcb3a3710"><div class="ttname"><a href="a00304.html#ga042ef09ff2f0cb24a36f541bcb3a3710">glm::int32_t</a></div><div class="ttdeci">int32 int32_t</div><div class="ttdoc">32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00071">fwd.hpp:71</a></div></div>
<div class="ttc" id="a00263_html_gab630f76c26b50298187f7889104d4b9c"><div class="ttname"><a href="a00263.html#gab630f76c26b50298187f7889104d4b9c">glm::uint64</a></div><div class="ttdeci">detail::uint64 uint64</div><div class="ttdoc">64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00151_source.html#l00067">scalar_uint_sized.hpp:67</a></div></div>
<div class="ttc" id="a00260_html_gaff5189f97f9e842d9636a0f240001b2e"><div class="ttname"><a href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">glm::int64</a></div><div class="ttdeci">detail::int64 int64</div><div class="ttdoc">64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00146_source.html#l00067">scalar_int_sized.hpp:67</a></div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
