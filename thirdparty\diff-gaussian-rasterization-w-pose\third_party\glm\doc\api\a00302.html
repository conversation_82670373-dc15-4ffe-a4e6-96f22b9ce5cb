<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTC_round</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTC_round<div class="ingroups"><a class="el" href="a00286.html">Recommended extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00143.html" title="GLM_GTC_round ">glm/gtc/round.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga1d89ac88582aaf4d5dfa5feb4a376fd4"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga1d89ac88582aaf4d5dfa5feb4a376fd4"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00302.html#ga1d89ac88582aaf4d5dfa5feb4a376fd4">ceilMultiple</a> (genType v, genType Multiple)</td></tr>
<tr class="memdesc:ga1d89ac88582aaf4d5dfa5feb4a376fd4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Higher multiple number of Source.  <a href="a00302.html#ga1d89ac88582aaf4d5dfa5feb4a376fd4">More...</a><br /></td></tr>
<tr class="separator:ga1d89ac88582aaf4d5dfa5feb4a376fd4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab77fdcc13f8e92d2e0b1b7d7aeab8e9d"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gab77fdcc13f8e92d2e0b1b7d7aeab8e9d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00302.html#gab77fdcc13f8e92d2e0b1b7d7aeab8e9d">ceilMultiple</a> (vec&lt; L, T, Q &gt; const &amp;v, vec&lt; L, T, Q &gt; const &amp;Multiple)</td></tr>
<tr class="memdesc:gab77fdcc13f8e92d2e0b1b7d7aeab8e9d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Higher multiple number of Source.  <a href="a00302.html#gab77fdcc13f8e92d2e0b1b7d7aeab8e9d">More...</a><br /></td></tr>
<tr class="separator:gab77fdcc13f8e92d2e0b1b7d7aeab8e9d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5c3ef36ae32aa4271f1544f92bd578b6"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:ga5c3ef36ae32aa4271f1544f92bd578b6"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genIUType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00302.html#ga5c3ef36ae32aa4271f1544f92bd578b6">ceilPowerOfTwo</a> (genIUType v)</td></tr>
<tr class="memdesc:ga5c3ef36ae32aa4271f1544f92bd578b6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the power of two number which value is just higher the input value, round up to a power of two.  <a href="a00302.html#ga5c3ef36ae32aa4271f1544f92bd578b6">More...</a><br /></td></tr>
<tr class="separator:ga5c3ef36ae32aa4271f1544f92bd578b6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab53d4a97c0d3e297be5f693cdfdfe5d2"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gab53d4a97c0d3e297be5f693cdfdfe5d2"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00302.html#gab53d4a97c0d3e297be5f693cdfdfe5d2">ceilPowerOfTwo</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gab53d4a97c0d3e297be5f693cdfdfe5d2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the power of two number which value is just higher the input value, round up to a power of two.  <a href="a00302.html#gab53d4a97c0d3e297be5f693cdfdfe5d2">More...</a><br /></td></tr>
<tr class="separator:gab53d4a97c0d3e297be5f693cdfdfe5d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2ffa3cd5f2ea746ee1bf57c46da6315e"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga2ffa3cd5f2ea746ee1bf57c46da6315e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00302.html#ga2ffa3cd5f2ea746ee1bf57c46da6315e">floorMultiple</a> (genType v, genType Multiple)</td></tr>
<tr class="memdesc:ga2ffa3cd5f2ea746ee1bf57c46da6315e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Lower multiple number of Source.  <a href="a00302.html#ga2ffa3cd5f2ea746ee1bf57c46da6315e">More...</a><br /></td></tr>
<tr class="separator:ga2ffa3cd5f2ea746ee1bf57c46da6315e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacdd8901448f51f0b192380e422fae3e4"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gacdd8901448f51f0b192380e422fae3e4"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00302.html#gacdd8901448f51f0b192380e422fae3e4">floorMultiple</a> (vec&lt; L, T, Q &gt; const &amp;v, vec&lt; L, T, Q &gt; const &amp;Multiple)</td></tr>
<tr class="memdesc:gacdd8901448f51f0b192380e422fae3e4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Lower multiple number of Source.  <a href="a00302.html#gacdd8901448f51f0b192380e422fae3e4">More...</a><br /></td></tr>
<tr class="separator:gacdd8901448f51f0b192380e422fae3e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafe273a57935d04c9db677bf67f9a71f4"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:gafe273a57935d04c9db677bf67f9a71f4"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genIUType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00302.html#gafe273a57935d04c9db677bf67f9a71f4">floorPowerOfTwo</a> (genIUType v)</td></tr>
<tr class="memdesc:gafe273a57935d04c9db677bf67f9a71f4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the power of two number which value is just lower the input value, round down to a power of two.  <a href="a00302.html#gafe273a57935d04c9db677bf67f9a71f4">More...</a><br /></td></tr>
<tr class="separator:gafe273a57935d04c9db677bf67f9a71f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf0d591a8fca8ddb9289cdeb44b989c2d"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf0d591a8fca8ddb9289cdeb44b989c2d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00302.html#gaf0d591a8fca8ddb9289cdeb44b989c2d">floorPowerOfTwo</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gaf0d591a8fca8ddb9289cdeb44b989c2d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the power of two number which value is just lower the input value, round down to a power of two.  <a href="a00302.html#gaf0d591a8fca8ddb9289cdeb44b989c2d">More...</a><br /></td></tr>
<tr class="separator:gaf0d591a8fca8ddb9289cdeb44b989c2d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab892defcc9c0b0618df7251253dc0fbb"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gab892defcc9c0b0618df7251253dc0fbb"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00302.html#gab892defcc9c0b0618df7251253dc0fbb">roundMultiple</a> (genType v, genType Multiple)</td></tr>
<tr class="memdesc:gab892defcc9c0b0618df7251253dc0fbb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Lower multiple number of Source.  <a href="a00302.html#gab892defcc9c0b0618df7251253dc0fbb">More...</a><br /></td></tr>
<tr class="separator:gab892defcc9c0b0618df7251253dc0fbb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2f1a68332d761804c054460a612e3a4b"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga2f1a68332d761804c054460a612e3a4b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00302.html#ga2f1a68332d761804c054460a612e3a4b">roundMultiple</a> (vec&lt; L, T, Q &gt; const &amp;v, vec&lt; L, T, Q &gt; const &amp;Multiple)</td></tr>
<tr class="memdesc:ga2f1a68332d761804c054460a612e3a4b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Lower multiple number of Source.  <a href="a00302.html#ga2f1a68332d761804c054460a612e3a4b">More...</a><br /></td></tr>
<tr class="separator:ga2f1a68332d761804c054460a612e3a4b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae4e1bf5d1cd179f59261a7342bdcafca"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:gae4e1bf5d1cd179f59261a7342bdcafca"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genIUType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00302.html#gae4e1bf5d1cd179f59261a7342bdcafca">roundPowerOfTwo</a> (genIUType v)</td></tr>
<tr class="memdesc:gae4e1bf5d1cd179f59261a7342bdcafca"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the power of two number which value is the closet to the input value.  <a href="a00302.html#gae4e1bf5d1cd179f59261a7342bdcafca">More...</a><br /></td></tr>
<tr class="separator:gae4e1bf5d1cd179f59261a7342bdcafca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga258802a7d55c03c918f28cf4d241c4d0"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga258802a7d55c03c918f28cf4d241c4d0"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00302.html#ga258802a7d55c03c918f28cf4d241c4d0">roundPowerOfTwo</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga258802a7d55c03c918f28cf4d241c4d0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the power of two number which value is the closet to the input value.  <a href="a00302.html#ga258802a7d55c03c918f28cf4d241c4d0">More...</a><br /></td></tr>
<tr class="separator:ga258802a7d55c03c918f28cf4d241c4d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00143.html" title="GLM_GTC_round ">glm/gtc/round.hpp</a>&gt; to use the features of this extension. </p>
<p>Rounding value to specific boundings </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga1d89ac88582aaf4d5dfa5feb4a376fd4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::ceilMultiple </td>
          <td>(</td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>v</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>Multiple</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Higher multiple number of Source. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point or integer scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">v</td><td>Source value to which is applied the function </td></tr>
    <tr><td class="paramname">Multiple</td><td>Must be a null or positive value</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00302.html" title="Include <glm/gtc/round.hpp> to use the features of this extension. ">GLM_GTC_round</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gab77fdcc13f8e92d2e0b1b7d7aeab8e9d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::ceilMultiple </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Multiple</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Higher multiple number of Source. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point or integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">v</td><td>Source values to which is applied the function </td></tr>
    <tr><td class="paramname">Multiple</td><td>Must be a null or positive value</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00302.html" title="Include <glm/gtc/round.hpp> to use the features of this extension. ">GLM_GTC_round</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5c3ef36ae32aa4271f1544f92bd578b6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genIUType glm::ceilPowerOfTwo </td>
          <td>(</td>
          <td class="paramtype">genIUType&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the power of two number which value is just higher the input value, round up to a power of two. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00302.html" title="Include <glm/gtc/round.hpp> to use the features of this extension. ">GLM_GTC_round</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gab53d4a97c0d3e297be5f693cdfdfe5d2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::ceilPowerOfTwo </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the power of two number which value is just higher the input value, round up to a power of two. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point or integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00302.html" title="Include <glm/gtc/round.hpp> to use the features of this extension. ">GLM_GTC_round</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2ffa3cd5f2ea746ee1bf57c46da6315e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::floorMultiple </td>
          <td>(</td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>v</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>Multiple</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Lower multiple number of Source. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point or integer scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">v</td><td>Source value to which is applied the function </td></tr>
    <tr><td class="paramname">Multiple</td><td>Must be a null or positive value</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00302.html" title="Include <glm/gtc/round.hpp> to use the features of this extension. ">GLM_GTC_round</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gacdd8901448f51f0b192380e422fae3e4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::floorMultiple </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Multiple</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Lower multiple number of Source. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point or integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">v</td><td>Source values to which is applied the function </td></tr>
    <tr><td class="paramname">Multiple</td><td>Must be a null or positive value</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00302.html" title="Include <glm/gtc/round.hpp> to use the features of this extension. ">GLM_GTC_round</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gafe273a57935d04c9db677bf67f9a71f4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genIUType glm::floorPowerOfTwo </td>
          <td>(</td>
          <td class="paramtype">genIUType&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the power of two number which value is just lower the input value, round down to a power of two. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00302.html" title="Include <glm/gtc/round.hpp> to use the features of this extension. ">GLM_GTC_round</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaf0d591a8fca8ddb9289cdeb44b989c2d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::floorPowerOfTwo </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the power of two number which value is just lower the input value, round down to a power of two. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point or integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00302.html" title="Include <glm/gtc/round.hpp> to use the features of this extension. ">GLM_GTC_round</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gab892defcc9c0b0618df7251253dc0fbb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::roundMultiple </td>
          <td>(</td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>v</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>Multiple</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Lower multiple number of Source. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point or integer scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">v</td><td>Source value to which is applied the function </td></tr>
    <tr><td class="paramname">Multiple</td><td>Must be a null or positive value</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00302.html" title="Include <glm/gtc/round.hpp> to use the features of this extension. ">GLM_GTC_round</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2f1a68332d761804c054460a612e3a4b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::roundMultiple </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Multiple</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Lower multiple number of Source. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point or integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">v</td><td>Source values to which is applied the function </td></tr>
    <tr><td class="paramname">Multiple</td><td>Must be a null or positive value</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00302.html" title="Include <glm/gtc/round.hpp> to use the features of this extension. ">GLM_GTC_round</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae4e1bf5d1cd179f59261a7342bdcafca"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genIUType glm::roundPowerOfTwo </td>
          <td>(</td>
          <td class="paramtype">genIUType&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the power of two number which value is the closet to the input value. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00302.html" title="Include <glm/gtc/round.hpp> to use the features of this extension. ">GLM_GTC_round</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga258802a7d55c03c918f28cf4d241c4d0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::roundPowerOfTwo </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the power of two number which value is the closet to the input value. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point or integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00302.html" title="Include <glm/gtc/round.hpp> to use the features of this extension. ">GLM_GTC_round</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
