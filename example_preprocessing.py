#!/usr/bin/env python3
"""
Example script demonstrating how to use the GaVS preprocessing pipeline.

This script shows different ways to preprocess videos for GaVS training,
including basic usage, batch processing, and integration with mask generation.
"""

import os
import sys
from pathlib import Path
import logging

# Add the current directory to Python path to import our preprocessor
sys.path.append(str(Path(__file__).parent))

from preprocess_video_for_gavs import GaVSPreprocessor, create_gavs_config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def example_basic_preprocessing():
    """Example 1: Basic video preprocessing."""
    print("=" * 60)
    print("Example 1: Basic Video Preprocessing")
    print("=" * 60)
    
    # Input video path (replace with your video)
    input_video = "path/to/your/video.mp4"
    output_dir = "./dataset/example_video"
    
    # Check if input video exists
    if not Path(input_video).exists():
        print(f"⚠️  Input video not found: {input_video}")
        print("Please update the input_video path in this script")
        return False
    
    try:
        # Create preprocessor
        preprocessor = GaVSPreprocessor(
            input_video=input_video,
            output_dir=output_dir,
            fps=30.0
        )
        
        # Run preprocessing pipeline
        success = preprocessor.process()
        
        if success:
            print("✅ Basic preprocessing completed successfully!")
            
            # Create GaVS config file
            config_path = f"{output_dir}.yaml"
            create_gavs_config(output_dir, config_path)
            print(f"✅ Created config file: {config_path}")
            
            return True
        else:
            print("❌ Basic preprocessing failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error during preprocessing: {e}")
        return False


def example_batch_preprocessing():
    """Example 2: Batch processing multiple videos."""
    print("=" * 60)
    print("Example 2: Batch Video Processing")
    print("=" * 60)
    
    # List of input videos (replace with your videos)
    video_list = [
        "path/to/video1.mp4",
        "path/to/video2.mp4", 
        "path/to/video3.mp4"
    ]
    
    output_base_dir = "./dataset"
    
    successful_datasets = []
    failed_datasets = []
    
    for i, video_path in enumerate(video_list, 1):
        print(f"\n📹 Processing video {i}/{len(video_list)}: {video_path}")
        
        if not Path(video_path).exists():
            print(f"⚠️  Video not found: {video_path}")
            failed_datasets.append(video_path)
            continue
        
        # Generate output directory name from video filename
        video_name = Path(video_path).stem
        output_dir = Path(output_base_dir) / f"video_{i:02d}_{video_name}"
        
        try:
            preprocessor = GaVSPreprocessor(
                input_video=video_path,
                output_dir=str(output_dir),
                fps=30.0
            )
            
            success = preprocessor.process()
            
            if success:
                successful_datasets.append(str(output_dir))
                print(f"✅ Successfully processed: {video_path}")
                
                # Create config file
                config_path = f"{output_dir}.yaml"
                create_gavs_config(str(output_dir), config_path)
                
            else:
                failed_datasets.append(video_path)
                print(f"❌ Failed to process: {video_path}")
                
        except Exception as e:
            failed_datasets.append(video_path)
            print(f"❌ Error processing {video_path}: {e}")
    
    # Summary
    print("\n" + "=" * 60)
    print("Batch Processing Summary")
    print("=" * 60)
    print(f"✅ Successful: {len(successful_datasets)}")
    print(f"❌ Failed: {len(failed_datasets)}")
    
    if successful_datasets:
        print("\nSuccessful datasets:")
        for dataset in successful_datasets:
            print(f"  - {dataset}")
    
    if failed_datasets:
        print("\nFailed videos:")
        for video in failed_datasets:
            print(f"  - {video}")
    
    return len(successful_datasets) > 0


def example_custom_preprocessing():
    """Example 3: Custom preprocessing with specific settings."""
    print("=" * 60)
    print("Example 3: Custom Preprocessing Settings")
    print("=" * 60)
    
    input_video = "path/to/your/video.mp4"
    output_dir = "./dataset/custom_video"
    
    if not Path(input_video).exists():
        print(f"⚠️  Input video not found: {input_video}")
        print("Please update the input_video path in this script")
        return False
    
    try:
        # Create preprocessor with custom settings
        preprocessor = GaVSPreprocessor(
            input_video=input_video,
            output_dir=output_dir,
            fps=25.0  # Custom frame rate
        )
        
        # Run individual steps with custom logic
        print("🔧 Running custom preprocessing pipeline...")
        
        # Step 1: Check dependencies
        if not preprocessor.check_dependencies():
            print("❌ Dependencies check failed")
            return False
        
        # Step 2: Setup directories
        preprocessor.setup_directory_structure()
        print("✅ Directory structure created")
        
        # Step 3: Extract frames
        if not preprocessor.extract_frames():
            print("❌ Frame extraction failed")
            return False
        print("✅ Frames extracted")
        
        # Step 4: Get video info
        video_info = preprocessor.get_video_info()
        if video_info:
            print(f"📊 Video info: {video_info['width']}x{video_info['height']} @ {video_info['fps']} FPS")
        
        # Step 5: Run COLMAP
        if not preprocessor.run_colmap_pipeline():
            print("❌ COLMAP pipeline failed")
            return False
        print("✅ COLMAP reconstruction completed")
        
        # Step 6: Parse results and generate config
        cameras, images, points3d = preprocessor.parse_colmap_results()
        if not cameras or not images:
            print("❌ Failed to parse COLMAP results")
            return False
        
        if not preprocessor.generate_blender_json(cameras, images):
            print("❌ Failed to generate blender.json")
            return False
        print("✅ Configuration files generated")
        
        # Step 7: Generate masks
        if not preprocessor.generate_placeholder_masks():
            print("❌ Failed to generate masks")
            return False
        print("✅ Placeholder masks generated")
        
        # Step 8: Setup instructions
        preprocessor.setup_mask_generation_instructions()
        print("✅ Mask generation instructions created")
        
        # Step 9: Validate
        if not preprocessor.validate_dataset():
            print("❌ Dataset validation failed")
            return False
        print("✅ Dataset validation passed")
        
        print("🎉 Custom preprocessing completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during custom preprocessing: {e}")
        return False


def example_integration_with_mask_generation():
    """Example 4: Integration with proper mask generation."""
    print("=" * 60)
    print("Example 4: Integration with Mask Generation")
    print("=" * 60)
    
    print("This example shows how to integrate with proper mask generation tools.")
    print("Note: This requires additional setup of Grounded SAM 2 or similar tools.")
    
    # Placeholder for mask generation integration
    print("""
To integrate with Grounded SAM 2 for proper mask generation:

1. Install Grounded SAM 2:
   git clone https://github.com/IDEA-Research/Grounded-SAM-2.git
   cd Grounded-SAM-2
   pip install -e .

2. Modify the preprocessing script to use proper mask generation:
   
   def generate_proper_masks(self, text_prompts):
       from grounded_sam2 import GroundedSAM2
       
       model = GroundedSAM2()
       image_files = sorted(self.images_dir.glob('*.png'))
       
       for image_file in image_files:
           masks = model.generate_masks(str(image_file), text_prompts)
           # Process and save masks to fg_mask/ directory
           
3. Use text prompts specific to your video content:
   prompts = ["person", "car", "bicycle", "dog"]  # Adjust based on your video
   
4. Replace placeholder masks with proper segmentation results.
""")
    
    return True


def main():
    """Run all examples."""
    print("🚀 GaVS Preprocessing Examples")
    print("=" * 60)
    
    examples = [
        ("Basic Preprocessing", example_basic_preprocessing),
        ("Batch Processing", example_batch_preprocessing), 
        ("Custom Settings", example_custom_preprocessing),
        ("Mask Generation Integration", example_integration_with_mask_generation)
    ]
    
    print("Available examples:")
    for i, (name, _) in enumerate(examples, 1):
        print(f"  {i}. {name}")
    
    print("\nNote: Update video paths in this script before running examples.")
    print("The examples use placeholder paths that need to be replaced with actual video files.")
    
    # Uncomment the example you want to run:
    # example_basic_preprocessing()
    # example_batch_preprocessing()
    # example_custom_preprocessing()
    # example_integration_with_mask_generation()
    
    print("\n💡 To run an example, uncomment the corresponding function call in main().")


if __name__ == "__main__":
    main()
