<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: type_aligned.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_f35778ec600a1b9bbc4524e62e226aa2.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">gtx/type_aligned.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00364.html">GLM_GTX_type_aligned</a>  
<a href="#details">More...</a></p>

<p><a href="a00162_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gab5cd5c5fad228b25c782084f1cc30114"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gab5cd5c5fad228b25c782084f1cc30114">GLM_ALIGNED_TYPEDEF</a> (lowp_int8, aligned_lowp_int8, 1)</td></tr>
<tr class="memdesc:gab5cd5c5fad228b25c782084f1cc30114"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#gab5cd5c5fad228b25c782084f1cc30114">More...</a><br /></td></tr>
<tr class="separator:gab5cd5c5fad228b25c782084f1cc30114"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5bb5dd895ef625c1b113f2cf400186b0"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga5bb5dd895ef625c1b113f2cf400186b0">GLM_ALIGNED_TYPEDEF</a> (lowp_int16, aligned_lowp_int16, 2)</td></tr>
<tr class="memdesc:ga5bb5dd895ef625c1b113f2cf400186b0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#ga5bb5dd895ef625c1b113f2cf400186b0">More...</a><br /></td></tr>
<tr class="separator:ga5bb5dd895ef625c1b113f2cf400186b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac6efa54cf7c6c86f7158922abdb1a430"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gac6efa54cf7c6c86f7158922abdb1a430">GLM_ALIGNED_TYPEDEF</a> (lowp_int32, aligned_lowp_int32, 4)</td></tr>
<tr class="memdesc:gac6efa54cf7c6c86f7158922abdb1a430"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#gac6efa54cf7c6c86f7158922abdb1a430">More...</a><br /></td></tr>
<tr class="separator:gac6efa54cf7c6c86f7158922abdb1a430"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6612eb77c8607048e7552279a11eeb5f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga6612eb77c8607048e7552279a11eeb5f">GLM_ALIGNED_TYPEDEF</a> (lowp_int64, aligned_lowp_int64, 8)</td></tr>
<tr class="memdesc:ga6612eb77c8607048e7552279a11eeb5f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga6612eb77c8607048e7552279a11eeb5f">More...</a><br /></td></tr>
<tr class="separator:ga6612eb77c8607048e7552279a11eeb5f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7ddc1848ff2223026db8968ce0c97497"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga7ddc1848ff2223026db8968ce0c97497">GLM_ALIGNED_TYPEDEF</a> (lowp_int8_t, aligned_lowp_int8_t, 1)</td></tr>
<tr class="memdesc:ga7ddc1848ff2223026db8968ce0c97497"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#ga7ddc1848ff2223026db8968ce0c97497">More...</a><br /></td></tr>
<tr class="separator:ga7ddc1848ff2223026db8968ce0c97497"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga22240dd9458b0f8c11fbcc4f48714f68"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga22240dd9458b0f8c11fbcc4f48714f68">GLM_ALIGNED_TYPEDEF</a> (lowp_int16_t, aligned_lowp_int16_t, 2)</td></tr>
<tr class="memdesc:ga22240dd9458b0f8c11fbcc4f48714f68"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#ga22240dd9458b0f8c11fbcc4f48714f68">More...</a><br /></td></tr>
<tr class="separator:ga22240dd9458b0f8c11fbcc4f48714f68"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8130ea381d76a2cc34a93ccbb6cf487d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga8130ea381d76a2cc34a93ccbb6cf487d">GLM_ALIGNED_TYPEDEF</a> (lowp_int32_t, aligned_lowp_int32_t, 4)</td></tr>
<tr class="memdesc:ga8130ea381d76a2cc34a93ccbb6cf487d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#ga8130ea381d76a2cc34a93ccbb6cf487d">More...</a><br /></td></tr>
<tr class="separator:ga8130ea381d76a2cc34a93ccbb6cf487d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7ccb60f3215d293fd62b33b31ed0e7be"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga7ccb60f3215d293fd62b33b31ed0e7be">GLM_ALIGNED_TYPEDEF</a> (lowp_int64_t, aligned_lowp_int64_t, 8)</td></tr>
<tr class="memdesc:ga7ccb60f3215d293fd62b33b31ed0e7be"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga7ccb60f3215d293fd62b33b31ed0e7be">More...</a><br /></td></tr>
<tr class="separator:ga7ccb60f3215d293fd62b33b31ed0e7be"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac20d508d2ef5cc95ad3daf083c57ec2a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gac20d508d2ef5cc95ad3daf083c57ec2a">GLM_ALIGNED_TYPEDEF</a> (lowp_i8, aligned_lowp_i8, 1)</td></tr>
<tr class="memdesc:gac20d508d2ef5cc95ad3daf083c57ec2a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#gac20d508d2ef5cc95ad3daf083c57ec2a">More...</a><br /></td></tr>
<tr class="separator:gac20d508d2ef5cc95ad3daf083c57ec2a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga50257b48069a31d0c8d9c1f644d267de"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga50257b48069a31d0c8d9c1f644d267de">GLM_ALIGNED_TYPEDEF</a> (lowp_i16, aligned_lowp_i16, 2)</td></tr>
<tr class="memdesc:ga50257b48069a31d0c8d9c1f644d267de"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#ga50257b48069a31d0c8d9c1f644d267de">More...</a><br /></td></tr>
<tr class="separator:ga50257b48069a31d0c8d9c1f644d267de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa07e98e67b7a3435c0746018c7a2a839"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaa07e98e67b7a3435c0746018c7a2a839">GLM_ALIGNED_TYPEDEF</a> (lowp_i32, aligned_lowp_i32, 4)</td></tr>
<tr class="memdesc:gaa07e98e67b7a3435c0746018c7a2a839"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#gaa07e98e67b7a3435c0746018c7a2a839">More...</a><br /></td></tr>
<tr class="separator:gaa07e98e67b7a3435c0746018c7a2a839"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga62601fc6f8ca298b77285bedf03faffd"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga62601fc6f8ca298b77285bedf03faffd">GLM_ALIGNED_TYPEDEF</a> (lowp_i64, aligned_lowp_i64, 8)</td></tr>
<tr class="memdesc:ga62601fc6f8ca298b77285bedf03faffd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga62601fc6f8ca298b77285bedf03faffd">More...</a><br /></td></tr>
<tr class="separator:ga62601fc6f8ca298b77285bedf03faffd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac8cff825951aeb54dd846037113c72db"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gac8cff825951aeb54dd846037113c72db">GLM_ALIGNED_TYPEDEF</a> (mediump_int8, aligned_mediump_int8, 1)</td></tr>
<tr class="memdesc:gac8cff825951aeb54dd846037113c72db"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#gac8cff825951aeb54dd846037113c72db">More...</a><br /></td></tr>
<tr class="separator:gac8cff825951aeb54dd846037113c72db"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga78f443d88f438575a62b5df497cdf66b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga78f443d88f438575a62b5df497cdf66b">GLM_ALIGNED_TYPEDEF</a> (mediump_int16, aligned_mediump_int16, 2)</td></tr>
<tr class="memdesc:ga78f443d88f438575a62b5df497cdf66b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#ga78f443d88f438575a62b5df497cdf66b">More...</a><br /></td></tr>
<tr class="separator:ga78f443d88f438575a62b5df497cdf66b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0680cd3b5d4e8006985fb41a4f9b57af"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga0680cd3b5d4e8006985fb41a4f9b57af">GLM_ALIGNED_TYPEDEF</a> (mediump_int32, aligned_mediump_int32, 4)</td></tr>
<tr class="memdesc:ga0680cd3b5d4e8006985fb41a4f9b57af"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#ga0680cd3b5d4e8006985fb41a4f9b57af">More...</a><br /></td></tr>
<tr class="separator:ga0680cd3b5d4e8006985fb41a4f9b57af"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad9e5babb1dd3e3531b42c37bf25dd951"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gad9e5babb1dd3e3531b42c37bf25dd951">GLM_ALIGNED_TYPEDEF</a> (mediump_int64, aligned_mediump_int64, 8)</td></tr>
<tr class="memdesc:gad9e5babb1dd3e3531b42c37bf25dd951"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#gad9e5babb1dd3e3531b42c37bf25dd951">More...</a><br /></td></tr>
<tr class="separator:gad9e5babb1dd3e3531b42c37bf25dd951"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga353fd9fa8a9ad952fcabd0d53ad9a6dd"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga353fd9fa8a9ad952fcabd0d53ad9a6dd">GLM_ALIGNED_TYPEDEF</a> (mediump_int8_t, aligned_mediump_int8_t, 1)</td></tr>
<tr class="memdesc:ga353fd9fa8a9ad952fcabd0d53ad9a6dd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#ga353fd9fa8a9ad952fcabd0d53ad9a6dd">More...</a><br /></td></tr>
<tr class="separator:ga353fd9fa8a9ad952fcabd0d53ad9a6dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2196442c0e5c5e8c77842de388c42521"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga2196442c0e5c5e8c77842de388c42521">GLM_ALIGNED_TYPEDEF</a> (mediump_int16_t, aligned_mediump_int16_t, 2)</td></tr>
<tr class="memdesc:ga2196442c0e5c5e8c77842de388c42521"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#ga2196442c0e5c5e8c77842de388c42521">More...</a><br /></td></tr>
<tr class="separator:ga2196442c0e5c5e8c77842de388c42521"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1284488189daf897cf095c5eefad9744"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga1284488189daf897cf095c5eefad9744">GLM_ALIGNED_TYPEDEF</a> (mediump_int32_t, aligned_mediump_int32_t, 4)</td></tr>
<tr class="memdesc:ga1284488189daf897cf095c5eefad9744"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#ga1284488189daf897cf095c5eefad9744">More...</a><br /></td></tr>
<tr class="separator:ga1284488189daf897cf095c5eefad9744"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga73fdc86a539808af58808b7c60a1c4d8"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga73fdc86a539808af58808b7c60a1c4d8">GLM_ALIGNED_TYPEDEF</a> (mediump_int64_t, aligned_mediump_int64_t, 8)</td></tr>
<tr class="memdesc:ga73fdc86a539808af58808b7c60a1c4d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga73fdc86a539808af58808b7c60a1c4d8">More...</a><br /></td></tr>
<tr class="separator:ga73fdc86a539808af58808b7c60a1c4d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafafeea923e1983262c972e2b83922d3b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gafafeea923e1983262c972e2b83922d3b">GLM_ALIGNED_TYPEDEF</a> (mediump_i8, aligned_mediump_i8, 1)</td></tr>
<tr class="memdesc:gafafeea923e1983262c972e2b83922d3b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#gafafeea923e1983262c972e2b83922d3b">More...</a><br /></td></tr>
<tr class="separator:gafafeea923e1983262c972e2b83922d3b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4b35ca5fe8f55c9d2fe54fdb8d8896f4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga4b35ca5fe8f55c9d2fe54fdb8d8896f4">GLM_ALIGNED_TYPEDEF</a> (mediump_i16, aligned_mediump_i16, 2)</td></tr>
<tr class="memdesc:ga4b35ca5fe8f55c9d2fe54fdb8d8896f4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#ga4b35ca5fe8f55c9d2fe54fdb8d8896f4">More...</a><br /></td></tr>
<tr class="separator:ga4b35ca5fe8f55c9d2fe54fdb8d8896f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga63b882e29170d428463d99c3d630acc6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga63b882e29170d428463d99c3d630acc6">GLM_ALIGNED_TYPEDEF</a> (mediump_i32, aligned_mediump_i32, 4)</td></tr>
<tr class="memdesc:ga63b882e29170d428463d99c3d630acc6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#ga63b882e29170d428463d99c3d630acc6">More...</a><br /></td></tr>
<tr class="separator:ga63b882e29170d428463d99c3d630acc6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8b20507bb048c1edea2d441cc953e6f0"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga8b20507bb048c1edea2d441cc953e6f0">GLM_ALIGNED_TYPEDEF</a> (mediump_i64, aligned_mediump_i64, 8)</td></tr>
<tr class="memdesc:ga8b20507bb048c1edea2d441cc953e6f0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga8b20507bb048c1edea2d441cc953e6f0">More...</a><br /></td></tr>
<tr class="separator:ga8b20507bb048c1edea2d441cc953e6f0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga56c5ca60813027b603c7b61425a0479d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga56c5ca60813027b603c7b61425a0479d">GLM_ALIGNED_TYPEDEF</a> (highp_int8, aligned_highp_int8, 1)</td></tr>
<tr class="memdesc:ga56c5ca60813027b603c7b61425a0479d"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#ga56c5ca60813027b603c7b61425a0479d">More...</a><br /></td></tr>
<tr class="separator:ga56c5ca60813027b603c7b61425a0479d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7a751b3aff24c0259f4a7357c2969089"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga7a751b3aff24c0259f4a7357c2969089">GLM_ALIGNED_TYPEDEF</a> (highp_int16, aligned_highp_int16, 2)</td></tr>
<tr class="memdesc:ga7a751b3aff24c0259f4a7357c2969089"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#ga7a751b3aff24c0259f4a7357c2969089">More...</a><br /></td></tr>
<tr class="separator:ga7a751b3aff24c0259f4a7357c2969089"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga70cd2144351c556469ee6119e59971fc"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga70cd2144351c556469ee6119e59971fc">GLM_ALIGNED_TYPEDEF</a> (highp_int32, aligned_highp_int32, 4)</td></tr>
<tr class="memdesc:ga70cd2144351c556469ee6119e59971fc"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#ga70cd2144351c556469ee6119e59971fc">More...</a><br /></td></tr>
<tr class="separator:ga70cd2144351c556469ee6119e59971fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga46bbf08dc004d8c433041e0b5018a5d3"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga46bbf08dc004d8c433041e0b5018a5d3">GLM_ALIGNED_TYPEDEF</a> (highp_int64, aligned_highp_int64, 8)</td></tr>
<tr class="memdesc:ga46bbf08dc004d8c433041e0b5018a5d3"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga46bbf08dc004d8c433041e0b5018a5d3">More...</a><br /></td></tr>
<tr class="separator:ga46bbf08dc004d8c433041e0b5018a5d3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab3e10c77a20d1abad2de1c561c7a5c18"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gab3e10c77a20d1abad2de1c561c7a5c18">GLM_ALIGNED_TYPEDEF</a> (highp_int8_t, aligned_highp_int8_t, 1)</td></tr>
<tr class="memdesc:gab3e10c77a20d1abad2de1c561c7a5c18"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#gab3e10c77a20d1abad2de1c561c7a5c18">More...</a><br /></td></tr>
<tr class="separator:gab3e10c77a20d1abad2de1c561c7a5c18"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga968f30319ebeaca9ebcd3a25a8e139fb"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga968f30319ebeaca9ebcd3a25a8e139fb">GLM_ALIGNED_TYPEDEF</a> (highp_int16_t, aligned_highp_int16_t, 2)</td></tr>
<tr class="memdesc:ga968f30319ebeaca9ebcd3a25a8e139fb"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#ga968f30319ebeaca9ebcd3a25a8e139fb">More...</a><br /></td></tr>
<tr class="separator:ga968f30319ebeaca9ebcd3a25a8e139fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaae773c28e6390c6aa76f5b678b7098a3"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaae773c28e6390c6aa76f5b678b7098a3">GLM_ALIGNED_TYPEDEF</a> (highp_int32_t, aligned_highp_int32_t, 4)</td></tr>
<tr class="memdesc:gaae773c28e6390c6aa76f5b678b7098a3"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#gaae773c28e6390c6aa76f5b678b7098a3">More...</a><br /></td></tr>
<tr class="separator:gaae773c28e6390c6aa76f5b678b7098a3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga790cfff1ca39d0ed696ffed980809311"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga790cfff1ca39d0ed696ffed980809311">GLM_ALIGNED_TYPEDEF</a> (highp_int64_t, aligned_highp_int64_t, 8)</td></tr>
<tr class="memdesc:ga790cfff1ca39d0ed696ffed980809311"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga790cfff1ca39d0ed696ffed980809311">More...</a><br /></td></tr>
<tr class="separator:ga790cfff1ca39d0ed696ffed980809311"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8265b91eb23c120a9b0c3e381bc37b96"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga8265b91eb23c120a9b0c3e381bc37b96">GLM_ALIGNED_TYPEDEF</a> (highp_i8, aligned_highp_i8, 1)</td></tr>
<tr class="memdesc:ga8265b91eb23c120a9b0c3e381bc37b96"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#ga8265b91eb23c120a9b0c3e381bc37b96">More...</a><br /></td></tr>
<tr class="separator:ga8265b91eb23c120a9b0c3e381bc37b96"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae6d384de17588d8edb894fbe06e0d410"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gae6d384de17588d8edb894fbe06e0d410">GLM_ALIGNED_TYPEDEF</a> (highp_i16, aligned_highp_i16, 2)</td></tr>
<tr class="memdesc:gae6d384de17588d8edb894fbe06e0d410"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#gae6d384de17588d8edb894fbe06e0d410">More...</a><br /></td></tr>
<tr class="separator:gae6d384de17588d8edb894fbe06e0d410"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9c8172b745ee03fc5b2b91c350c2922f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga9c8172b745ee03fc5b2b91c350c2922f">GLM_ALIGNED_TYPEDEF</a> (highp_i32, aligned_highp_i32, 4)</td></tr>
<tr class="memdesc:ga9c8172b745ee03fc5b2b91c350c2922f"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#ga9c8172b745ee03fc5b2b91c350c2922f">More...</a><br /></td></tr>
<tr class="separator:ga9c8172b745ee03fc5b2b91c350c2922f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga77e0dff12aa4020ddc3f8cabbea7b2e6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga77e0dff12aa4020ddc3f8cabbea7b2e6">GLM_ALIGNED_TYPEDEF</a> (highp_i64, aligned_highp_i64, 8)</td></tr>
<tr class="memdesc:ga77e0dff12aa4020ddc3f8cabbea7b2e6"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga77e0dff12aa4020ddc3f8cabbea7b2e6">More...</a><br /></td></tr>
<tr class="separator:ga77e0dff12aa4020ddc3f8cabbea7b2e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabd82b9faa9d4d618dbbe0fc8a1efee63"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gabd82b9faa9d4d618dbbe0fc8a1efee63">GLM_ALIGNED_TYPEDEF</a> (int8, aligned_int8, 1)</td></tr>
<tr class="memdesc:gabd82b9faa9d4d618dbbe0fc8a1efee63"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#gabd82b9faa9d4d618dbbe0fc8a1efee63">More...</a><br /></td></tr>
<tr class="separator:gabd82b9faa9d4d618dbbe0fc8a1efee63"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga285649744560be21000cfd81bbb5d507"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga285649744560be21000cfd81bbb5d507">GLM_ALIGNED_TYPEDEF</a> (int16, aligned_int16, 2)</td></tr>
<tr class="memdesc:ga285649744560be21000cfd81bbb5d507"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#ga285649744560be21000cfd81bbb5d507">More...</a><br /></td></tr>
<tr class="separator:ga285649744560be21000cfd81bbb5d507"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga07732da630b2deda428ce95c0ecaf3ff"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga07732da630b2deda428ce95c0ecaf3ff">GLM_ALIGNED_TYPEDEF</a> (int32, aligned_int32, 4)</td></tr>
<tr class="memdesc:ga07732da630b2deda428ce95c0ecaf3ff"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#ga07732da630b2deda428ce95c0ecaf3ff">More...</a><br /></td></tr>
<tr class="separator:ga07732da630b2deda428ce95c0ecaf3ff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1a8da2a8c51f69c07a2e7f473aa420f4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga1a8da2a8c51f69c07a2e7f473aa420f4">GLM_ALIGNED_TYPEDEF</a> (int64, aligned_int64, 8)</td></tr>
<tr class="memdesc:ga1a8da2a8c51f69c07a2e7f473aa420f4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga1a8da2a8c51f69c07a2e7f473aa420f4">More...</a><br /></td></tr>
<tr class="separator:ga1a8da2a8c51f69c07a2e7f473aa420f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga848aedf13e2d9738acf0bb482c590174"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga848aedf13e2d9738acf0bb482c590174">GLM_ALIGNED_TYPEDEF</a> (int8_t, aligned_int8_t, 1)</td></tr>
<tr class="memdesc:ga848aedf13e2d9738acf0bb482c590174"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#ga848aedf13e2d9738acf0bb482c590174">More...</a><br /></td></tr>
<tr class="separator:ga848aedf13e2d9738acf0bb482c590174"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafd2803d39049dd45a37a63931e25d943"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gafd2803d39049dd45a37a63931e25d943">GLM_ALIGNED_TYPEDEF</a> (int16_t, aligned_int16_t, 2)</td></tr>
<tr class="memdesc:gafd2803d39049dd45a37a63931e25d943"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#gafd2803d39049dd45a37a63931e25d943">More...</a><br /></td></tr>
<tr class="separator:gafd2803d39049dd45a37a63931e25d943"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae553b33349d6da832cf0724f1e024094"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gae553b33349d6da832cf0724f1e024094">GLM_ALIGNED_TYPEDEF</a> (int32_t, aligned_int32_t, 4)</td></tr>
<tr class="memdesc:gae553b33349d6da832cf0724f1e024094"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#gae553b33349d6da832cf0724f1e024094">More...</a><br /></td></tr>
<tr class="separator:gae553b33349d6da832cf0724f1e024094"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga16d223a2b3409e812e1d3bd87f0e9e5c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga16d223a2b3409e812e1d3bd87f0e9e5c">GLM_ALIGNED_TYPEDEF</a> (int64_t, aligned_int64_t, 8)</td></tr>
<tr class="memdesc:ga16d223a2b3409e812e1d3bd87f0e9e5c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga16d223a2b3409e812e1d3bd87f0e9e5c">More...</a><br /></td></tr>
<tr class="separator:ga16d223a2b3409e812e1d3bd87f0e9e5c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2de065d2ddfdb366bcd0febca79ae2ad"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga2de065d2ddfdb366bcd0febca79ae2ad">GLM_ALIGNED_TYPEDEF</a> (i8, aligned_i8, 1)</td></tr>
<tr class="memdesc:ga2de065d2ddfdb366bcd0febca79ae2ad"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#ga2de065d2ddfdb366bcd0febca79ae2ad">More...</a><br /></td></tr>
<tr class="separator:ga2de065d2ddfdb366bcd0febca79ae2ad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabd786bdc20a11c8cb05c92c8212e28d3"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gabd786bdc20a11c8cb05c92c8212e28d3">GLM_ALIGNED_TYPEDEF</a> (i16, aligned_i16, 2)</td></tr>
<tr class="memdesc:gabd786bdc20a11c8cb05c92c8212e28d3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#gabd786bdc20a11c8cb05c92c8212e28d3">More...</a><br /></td></tr>
<tr class="separator:gabd786bdc20a11c8cb05c92c8212e28d3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad4aefe56691cdb640c72f0d46d3fb532"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gad4aefe56691cdb640c72f0d46d3fb532">GLM_ALIGNED_TYPEDEF</a> (i32, aligned_i32, 4)</td></tr>
<tr class="memdesc:gad4aefe56691cdb640c72f0d46d3fb532"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#gad4aefe56691cdb640c72f0d46d3fb532">More...</a><br /></td></tr>
<tr class="separator:gad4aefe56691cdb640c72f0d46d3fb532"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8fe9745f7de24a8394518152ff9fccdc"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga8fe9745f7de24a8394518152ff9fccdc">GLM_ALIGNED_TYPEDEF</a> (i64, aligned_i64, 8)</td></tr>
<tr class="memdesc:ga8fe9745f7de24a8394518152ff9fccdc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga8fe9745f7de24a8394518152ff9fccdc">More...</a><br /></td></tr>
<tr class="separator:ga8fe9745f7de24a8394518152ff9fccdc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaad735483450099f7f882d4e3a3569bd"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaaad735483450099f7f882d4e3a3569bd">GLM_ALIGNED_TYPEDEF</a> (ivec1, aligned_ivec1, 4)</td></tr>
<tr class="memdesc:gaaad735483450099f7f882d4e3a3569bd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#gaaad735483450099f7f882d4e3a3569bd">More...</a><br /></td></tr>
<tr class="separator:gaaad735483450099f7f882d4e3a3569bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac7b6f823802edbd6edbaf70ea25bf068"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gac7b6f823802edbd6edbaf70ea25bf068">GLM_ALIGNED_TYPEDEF</a> (ivec2, aligned_ivec2, 8)</td></tr>
<tr class="memdesc:gac7b6f823802edbd6edbaf70ea25bf068"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit signed integer aligned vector of 2 components type.  <a href="a00364.html#gac7b6f823802edbd6edbaf70ea25bf068">More...</a><br /></td></tr>
<tr class="separator:gac7b6f823802edbd6edbaf70ea25bf068"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3e235bcd2b8029613f25b8d40a2d3ef7"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga3e235bcd2b8029613f25b8d40a2d3ef7">GLM_ALIGNED_TYPEDEF</a> (ivec3, aligned_ivec3, 16)</td></tr>
<tr class="memdesc:ga3e235bcd2b8029613f25b8d40a2d3ef7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit signed integer aligned vector of 3 components type.  <a href="a00364.html#ga3e235bcd2b8029613f25b8d40a2d3ef7">More...</a><br /></td></tr>
<tr class="separator:ga3e235bcd2b8029613f25b8d40a2d3ef7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga50d8a9523968c77f8325b4c9bfbff41e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga50d8a9523968c77f8325b4c9bfbff41e">GLM_ALIGNED_TYPEDEF</a> (ivec4, aligned_ivec4, 16)</td></tr>
<tr class="memdesc:ga50d8a9523968c77f8325b4c9bfbff41e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit signed integer aligned vector of 4 components type.  <a href="a00364.html#ga50d8a9523968c77f8325b4c9bfbff41e">More...</a><br /></td></tr>
<tr class="separator:ga50d8a9523968c77f8325b4c9bfbff41e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9ec20fdfb729c702032da9378c79679f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga9ec20fdfb729c702032da9378c79679f">GLM_ALIGNED_TYPEDEF</a> (i8vec1, aligned_i8vec1, 1)</td></tr>
<tr class="memdesc:ga9ec20fdfb729c702032da9378c79679f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#ga9ec20fdfb729c702032da9378c79679f">More...</a><br /></td></tr>
<tr class="separator:ga9ec20fdfb729c702032da9378c79679f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga25b3fe1d9e8d0a5e86c1949c1acd8131"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga25b3fe1d9e8d0a5e86c1949c1acd8131">GLM_ALIGNED_TYPEDEF</a> (i8vec2, aligned_i8vec2, 2)</td></tr>
<tr class="memdesc:ga25b3fe1d9e8d0a5e86c1949c1acd8131"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit signed integer aligned vector of 2 components type.  <a href="a00364.html#ga25b3fe1d9e8d0a5e86c1949c1acd8131">More...</a><br /></td></tr>
<tr class="separator:ga25b3fe1d9e8d0a5e86c1949c1acd8131"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2958f907719d94d8109b562540c910e2"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga2958f907719d94d8109b562540c910e2">GLM_ALIGNED_TYPEDEF</a> (i8vec3, aligned_i8vec3, 4)</td></tr>
<tr class="memdesc:ga2958f907719d94d8109b562540c910e2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit signed integer aligned vector of 3 components type.  <a href="a00364.html#ga2958f907719d94d8109b562540c910e2">More...</a><br /></td></tr>
<tr class="separator:ga2958f907719d94d8109b562540c910e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1fe6fc032a978f1c845fac9aa0668714"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga1fe6fc032a978f1c845fac9aa0668714">GLM_ALIGNED_TYPEDEF</a> (i8vec4, aligned_i8vec4, 4)</td></tr>
<tr class="memdesc:ga1fe6fc032a978f1c845fac9aa0668714"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit signed integer aligned vector of 4 components type.  <a href="a00364.html#ga1fe6fc032a978f1c845fac9aa0668714">More...</a><br /></td></tr>
<tr class="separator:ga1fe6fc032a978f1c845fac9aa0668714"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa4161e7a496dc96972254143fe873e55"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaa4161e7a496dc96972254143fe873e55">GLM_ALIGNED_TYPEDEF</a> (i16vec1, aligned_i16vec1, 2)</td></tr>
<tr class="memdesc:gaa4161e7a496dc96972254143fe873e55"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#gaa4161e7a496dc96972254143fe873e55">More...</a><br /></td></tr>
<tr class="separator:gaa4161e7a496dc96972254143fe873e55"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9d7cb211ccda69b1c22ddeeb0f3e7aba"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga9d7cb211ccda69b1c22ddeeb0f3e7aba">GLM_ALIGNED_TYPEDEF</a> (i16vec2, aligned_i16vec2, 4)</td></tr>
<tr class="memdesc:ga9d7cb211ccda69b1c22ddeeb0f3e7aba"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit signed integer aligned vector of 2 components type.  <a href="a00364.html#ga9d7cb211ccda69b1c22ddeeb0f3e7aba">More...</a><br /></td></tr>
<tr class="separator:ga9d7cb211ccda69b1c22ddeeb0f3e7aba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaee91dd2ab34423bcc11072ef6bd0f02"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaaee91dd2ab34423bcc11072ef6bd0f02">GLM_ALIGNED_TYPEDEF</a> (i16vec3, aligned_i16vec3, 8)</td></tr>
<tr class="memdesc:gaaee91dd2ab34423bcc11072ef6bd0f02"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit signed integer aligned vector of 3 components type.  <a href="a00364.html#gaaee91dd2ab34423bcc11072ef6bd0f02">More...</a><br /></td></tr>
<tr class="separator:gaaee91dd2ab34423bcc11072ef6bd0f02"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga49f047ccaa8b31fad9f26c67bf9b3510"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga49f047ccaa8b31fad9f26c67bf9b3510">GLM_ALIGNED_TYPEDEF</a> (i16vec4, aligned_i16vec4, 8)</td></tr>
<tr class="memdesc:ga49f047ccaa8b31fad9f26c67bf9b3510"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit signed integer aligned vector of 4 components type.  <a href="a00364.html#ga49f047ccaa8b31fad9f26c67bf9b3510">More...</a><br /></td></tr>
<tr class="separator:ga49f047ccaa8b31fad9f26c67bf9b3510"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga904e9c2436bb099397c0823506a0771f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga904e9c2436bb099397c0823506a0771f">GLM_ALIGNED_TYPEDEF</a> (i32vec1, aligned_i32vec1, 4)</td></tr>
<tr class="memdesc:ga904e9c2436bb099397c0823506a0771f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#ga904e9c2436bb099397c0823506a0771f">More...</a><br /></td></tr>
<tr class="separator:ga904e9c2436bb099397c0823506a0771f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf90651cf2f5e7ee2b11cfdc5a6749534"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaf90651cf2f5e7ee2b11cfdc5a6749534">GLM_ALIGNED_TYPEDEF</a> (i32vec2, aligned_i32vec2, 8)</td></tr>
<tr class="memdesc:gaf90651cf2f5e7ee2b11cfdc5a6749534"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit signed integer aligned vector of 2 components type.  <a href="a00364.html#gaf90651cf2f5e7ee2b11cfdc5a6749534">More...</a><br /></td></tr>
<tr class="separator:gaf90651cf2f5e7ee2b11cfdc5a6749534"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7354a4ead8cb17868aec36b9c30d6010"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga7354a4ead8cb17868aec36b9c30d6010">GLM_ALIGNED_TYPEDEF</a> (i32vec3, aligned_i32vec3, 16)</td></tr>
<tr class="memdesc:ga7354a4ead8cb17868aec36b9c30d6010"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit signed integer aligned vector of 3 components type.  <a href="a00364.html#ga7354a4ead8cb17868aec36b9c30d6010">More...</a><br /></td></tr>
<tr class="separator:ga7354a4ead8cb17868aec36b9c30d6010"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad2ecbdea18732163e2636e27b37981ee"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gad2ecbdea18732163e2636e27b37981ee">GLM_ALIGNED_TYPEDEF</a> (i32vec4, aligned_i32vec4, 16)</td></tr>
<tr class="memdesc:gad2ecbdea18732163e2636e27b37981ee"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit signed integer aligned vector of 4 components type.  <a href="a00364.html#gad2ecbdea18732163e2636e27b37981ee">More...</a><br /></td></tr>
<tr class="separator:gad2ecbdea18732163e2636e27b37981ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga965b1c9aa1800e93d4abc2eb2b5afcbf"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga965b1c9aa1800e93d4abc2eb2b5afcbf">GLM_ALIGNED_TYPEDEF</a> (i64vec1, aligned_i64vec1, 8)</td></tr>
<tr class="memdesc:ga965b1c9aa1800e93d4abc2eb2b5afcbf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga965b1c9aa1800e93d4abc2eb2b5afcbf">More...</a><br /></td></tr>
<tr class="separator:ga965b1c9aa1800e93d4abc2eb2b5afcbf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1f9e9c2ea2768675dff9bae5cde2d829"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga1f9e9c2ea2768675dff9bae5cde2d829">GLM_ALIGNED_TYPEDEF</a> (i64vec2, aligned_i64vec2, 16)</td></tr>
<tr class="memdesc:ga1f9e9c2ea2768675dff9bae5cde2d829"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit signed integer aligned vector of 2 components type.  <a href="a00364.html#ga1f9e9c2ea2768675dff9bae5cde2d829">More...</a><br /></td></tr>
<tr class="separator:ga1f9e9c2ea2768675dff9bae5cde2d829"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad77c317b7d942322cd5be4c8127b3187"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gad77c317b7d942322cd5be4c8127b3187">GLM_ALIGNED_TYPEDEF</a> (i64vec3, aligned_i64vec3, 32)</td></tr>
<tr class="memdesc:gad77c317b7d942322cd5be4c8127b3187"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit signed integer aligned vector of 3 components type.  <a href="a00364.html#gad77c317b7d942322cd5be4c8127b3187">More...</a><br /></td></tr>
<tr class="separator:gad77c317b7d942322cd5be4c8127b3187"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga716f8ea809bdb11b5b542d8b71aeb04f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga716f8ea809bdb11b5b542d8b71aeb04f">GLM_ALIGNED_TYPEDEF</a> (i64vec4, aligned_i64vec4, 32)</td></tr>
<tr class="memdesc:ga716f8ea809bdb11b5b542d8b71aeb04f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit signed integer aligned vector of 4 components type.  <a href="a00364.html#ga716f8ea809bdb11b5b542d8b71aeb04f">More...</a><br /></td></tr>
<tr class="separator:ga716f8ea809bdb11b5b542d8b71aeb04f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad46f8e9082d5878b1bc04f9c1471cdaa"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gad46f8e9082d5878b1bc04f9c1471cdaa">GLM_ALIGNED_TYPEDEF</a> (lowp_uint8, aligned_lowp_uint8, 1)</td></tr>
<tr class="memdesc:gad46f8e9082d5878b1bc04f9c1471cdaa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#gad46f8e9082d5878b1bc04f9c1471cdaa">More...</a><br /></td></tr>
<tr class="separator:gad46f8e9082d5878b1bc04f9c1471cdaa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1246094581af624aca6c7499aaabf801"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga1246094581af624aca6c7499aaabf801">GLM_ALIGNED_TYPEDEF</a> (lowp_uint16, aligned_lowp_uint16, 2)</td></tr>
<tr class="memdesc:ga1246094581af624aca6c7499aaabf801"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga1246094581af624aca6c7499aaabf801">More...</a><br /></td></tr>
<tr class="separator:ga1246094581af624aca6c7499aaabf801"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7a5009a1d0196bbf21dd7518f61f0249"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga7a5009a1d0196bbf21dd7518f61f0249">GLM_ALIGNED_TYPEDEF</a> (lowp_uint32, aligned_lowp_uint32, 4)</td></tr>
<tr class="memdesc:ga7a5009a1d0196bbf21dd7518f61f0249"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga7a5009a1d0196bbf21dd7518f61f0249">More...</a><br /></td></tr>
<tr class="separator:ga7a5009a1d0196bbf21dd7518f61f0249"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga45213fd18b3bb1df391671afefe4d1e7"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga45213fd18b3bb1df391671afefe4d1e7">GLM_ALIGNED_TYPEDEF</a> (lowp_uint64, aligned_lowp_uint64, 8)</td></tr>
<tr class="memdesc:ga45213fd18b3bb1df391671afefe4d1e7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga45213fd18b3bb1df391671afefe4d1e7">More...</a><br /></td></tr>
<tr class="separator:ga45213fd18b3bb1df391671afefe4d1e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0ba26b4e3fd9ecbc25358efd68d8a4ca"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga0ba26b4e3fd9ecbc25358efd68d8a4ca">GLM_ALIGNED_TYPEDEF</a> (lowp_uint8_t, aligned_lowp_uint8_t, 1)</td></tr>
<tr class="memdesc:ga0ba26b4e3fd9ecbc25358efd68d8a4ca"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga0ba26b4e3fd9ecbc25358efd68d8a4ca">More...</a><br /></td></tr>
<tr class="separator:ga0ba26b4e3fd9ecbc25358efd68d8a4ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf2b58f5fb6d4ec8ce7b76221d3af43e1"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaf2b58f5fb6d4ec8ce7b76221d3af43e1">GLM_ALIGNED_TYPEDEF</a> (lowp_uint16_t, aligned_lowp_uint16_t, 2)</td></tr>
<tr class="memdesc:gaf2b58f5fb6d4ec8ce7b76221d3af43e1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#gaf2b58f5fb6d4ec8ce7b76221d3af43e1">More...</a><br /></td></tr>
<tr class="separator:gaf2b58f5fb6d4ec8ce7b76221d3af43e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadc246401847dcba155f0699425e49dcd"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gadc246401847dcba155f0699425e49dcd">GLM_ALIGNED_TYPEDEF</a> (lowp_uint32_t, aligned_lowp_uint32_t, 4)</td></tr>
<tr class="memdesc:gadc246401847dcba155f0699425e49dcd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#gadc246401847dcba155f0699425e49dcd">More...</a><br /></td></tr>
<tr class="separator:gadc246401847dcba155f0699425e49dcd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaace64bddf51a9def01498da9a94fb01c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaace64bddf51a9def01498da9a94fb01c">GLM_ALIGNED_TYPEDEF</a> (lowp_uint64_t, aligned_lowp_uint64_t, 8)</td></tr>
<tr class="memdesc:gaace64bddf51a9def01498da9a94fb01c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#gaace64bddf51a9def01498da9a94fb01c">More...</a><br /></td></tr>
<tr class="separator:gaace64bddf51a9def01498da9a94fb01c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad7bb97c29d664bd86ffb1bed4abc5534"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gad7bb97c29d664bd86ffb1bed4abc5534">GLM_ALIGNED_TYPEDEF</a> (lowp_u8, aligned_lowp_u8, 1)</td></tr>
<tr class="memdesc:gad7bb97c29d664bd86ffb1bed4abc5534"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#gad7bb97c29d664bd86ffb1bed4abc5534">More...</a><br /></td></tr>
<tr class="separator:gad7bb97c29d664bd86ffb1bed4abc5534"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga404bba7785130e0b1384d695a9450b28"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga404bba7785130e0b1384d695a9450b28">GLM_ALIGNED_TYPEDEF</a> (lowp_u16, aligned_lowp_u16, 2)</td></tr>
<tr class="memdesc:ga404bba7785130e0b1384d695a9450b28"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga404bba7785130e0b1384d695a9450b28">More...</a><br /></td></tr>
<tr class="separator:ga404bba7785130e0b1384d695a9450b28"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga31ba41fd896257536958ec6080203d2a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga31ba41fd896257536958ec6080203d2a">GLM_ALIGNED_TYPEDEF</a> (lowp_u32, aligned_lowp_u32, 4)</td></tr>
<tr class="memdesc:ga31ba41fd896257536958ec6080203d2a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga31ba41fd896257536958ec6080203d2a">More...</a><br /></td></tr>
<tr class="separator:ga31ba41fd896257536958ec6080203d2a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacca5f13627f57b3505676e40a6e43e5e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gacca5f13627f57b3505676e40a6e43e5e">GLM_ALIGNED_TYPEDEF</a> (lowp_u64, aligned_lowp_u64, 8)</td></tr>
<tr class="memdesc:gacca5f13627f57b3505676e40a6e43e5e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#gacca5f13627f57b3505676e40a6e43e5e">More...</a><br /></td></tr>
<tr class="separator:gacca5f13627f57b3505676e40a6e43e5e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5faf1d3e70bf33174dd7f3d01d5b883b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga5faf1d3e70bf33174dd7f3d01d5b883b">GLM_ALIGNED_TYPEDEF</a> (mediump_uint8, aligned_mediump_uint8, 1)</td></tr>
<tr class="memdesc:ga5faf1d3e70bf33174dd7f3d01d5b883b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga5faf1d3e70bf33174dd7f3d01d5b883b">More...</a><br /></td></tr>
<tr class="separator:ga5faf1d3e70bf33174dd7f3d01d5b883b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga727e2bf2c433bb3b0182605860a48363"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga727e2bf2c433bb3b0182605860a48363">GLM_ALIGNED_TYPEDEF</a> (mediump_uint16, aligned_mediump_uint16, 2)</td></tr>
<tr class="memdesc:ga727e2bf2c433bb3b0182605860a48363"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga727e2bf2c433bb3b0182605860a48363">More...</a><br /></td></tr>
<tr class="separator:ga727e2bf2c433bb3b0182605860a48363"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga12566ca66d5962dadb4a5eb4c74e891e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga12566ca66d5962dadb4a5eb4c74e891e">GLM_ALIGNED_TYPEDEF</a> (mediump_uint32, aligned_mediump_uint32, 4)</td></tr>
<tr class="memdesc:ga12566ca66d5962dadb4a5eb4c74e891e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga12566ca66d5962dadb4a5eb4c74e891e">More...</a><br /></td></tr>
<tr class="separator:ga12566ca66d5962dadb4a5eb4c74e891e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7b66a97a8acaa35c5a377b947318c6bc"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga7b66a97a8acaa35c5a377b947318c6bc">GLM_ALIGNED_TYPEDEF</a> (mediump_uint64, aligned_mediump_uint64, 8)</td></tr>
<tr class="memdesc:ga7b66a97a8acaa35c5a377b947318c6bc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga7b66a97a8acaa35c5a377b947318c6bc">More...</a><br /></td></tr>
<tr class="separator:ga7b66a97a8acaa35c5a377b947318c6bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa9cde002439b74fa66120a16a9f55fcc"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaa9cde002439b74fa66120a16a9f55fcc">GLM_ALIGNED_TYPEDEF</a> (mediump_uint8_t, aligned_mediump_uint8_t, 1)</td></tr>
<tr class="memdesc:gaa9cde002439b74fa66120a16a9f55fcc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#gaa9cde002439b74fa66120a16a9f55fcc">More...</a><br /></td></tr>
<tr class="separator:gaa9cde002439b74fa66120a16a9f55fcc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1ca98c67f7d1e975f7c5202f1da1df1f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga1ca98c67f7d1e975f7c5202f1da1df1f">GLM_ALIGNED_TYPEDEF</a> (mediump_uint16_t, aligned_mediump_uint16_t, 2)</td></tr>
<tr class="memdesc:ga1ca98c67f7d1e975f7c5202f1da1df1f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga1ca98c67f7d1e975f7c5202f1da1df1f">More...</a><br /></td></tr>
<tr class="separator:ga1ca98c67f7d1e975f7c5202f1da1df1f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1dc8bc6199d785f235576948d80a597c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga1dc8bc6199d785f235576948d80a597c">GLM_ALIGNED_TYPEDEF</a> (mediump_uint32_t, aligned_mediump_uint32_t, 4)</td></tr>
<tr class="memdesc:ga1dc8bc6199d785f235576948d80a597c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga1dc8bc6199d785f235576948d80a597c">More...</a><br /></td></tr>
<tr class="separator:ga1dc8bc6199d785f235576948d80a597c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad14a0f2ec93519682b73d70b8e401d81"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gad14a0f2ec93519682b73d70b8e401d81">GLM_ALIGNED_TYPEDEF</a> (mediump_uint64_t, aligned_mediump_uint64_t, 8)</td></tr>
<tr class="memdesc:gad14a0f2ec93519682b73d70b8e401d81"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#gad14a0f2ec93519682b73d70b8e401d81">More...</a><br /></td></tr>
<tr class="separator:gad14a0f2ec93519682b73d70b8e401d81"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gada8b996eb6526dc1ead813bd49539d1b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gada8b996eb6526dc1ead813bd49539d1b">GLM_ALIGNED_TYPEDEF</a> (mediump_u8, aligned_mediump_u8, 1)</td></tr>
<tr class="memdesc:gada8b996eb6526dc1ead813bd49539d1b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#gada8b996eb6526dc1ead813bd49539d1b">More...</a><br /></td></tr>
<tr class="separator:gada8b996eb6526dc1ead813bd49539d1b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga28948f6bfb52b42deb9d73ae1ea8d8b0"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga28948f6bfb52b42deb9d73ae1ea8d8b0">GLM_ALIGNED_TYPEDEF</a> (mediump_u16, aligned_mediump_u16, 2)</td></tr>
<tr class="memdesc:ga28948f6bfb52b42deb9d73ae1ea8d8b0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga28948f6bfb52b42deb9d73ae1ea8d8b0">More...</a><br /></td></tr>
<tr class="separator:ga28948f6bfb52b42deb9d73ae1ea8d8b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad6a7c0b5630f89d3f1c5b4ef2919bb4c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gad6a7c0b5630f89d3f1c5b4ef2919bb4c">GLM_ALIGNED_TYPEDEF</a> (mediump_u32, aligned_mediump_u32, 4)</td></tr>
<tr class="memdesc:gad6a7c0b5630f89d3f1c5b4ef2919bb4c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#gad6a7c0b5630f89d3f1c5b4ef2919bb4c">More...</a><br /></td></tr>
<tr class="separator:gad6a7c0b5630f89d3f1c5b4ef2919bb4c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa0fc531cbaa972ac3a0b86d21ef4a7fa"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaa0fc531cbaa972ac3a0b86d21ef4a7fa">GLM_ALIGNED_TYPEDEF</a> (mediump_u64, aligned_mediump_u64, 8)</td></tr>
<tr class="memdesc:gaa0fc531cbaa972ac3a0b86d21ef4a7fa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#gaa0fc531cbaa972ac3a0b86d21ef4a7fa">More...</a><br /></td></tr>
<tr class="separator:gaa0fc531cbaa972ac3a0b86d21ef4a7fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0ee829f7b754b262bbfe6317c0d678ac"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga0ee829f7b754b262bbfe6317c0d678ac">GLM_ALIGNED_TYPEDEF</a> (highp_uint8, aligned_highp_uint8, 1)</td></tr>
<tr class="memdesc:ga0ee829f7b754b262bbfe6317c0d678ac"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga0ee829f7b754b262bbfe6317c0d678ac">More...</a><br /></td></tr>
<tr class="separator:ga0ee829f7b754b262bbfe6317c0d678ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga447848a817a626cae08cedc9778b331c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga447848a817a626cae08cedc9778b331c">GLM_ALIGNED_TYPEDEF</a> (highp_uint16, aligned_highp_uint16, 2)</td></tr>
<tr class="memdesc:ga447848a817a626cae08cedc9778b331c"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga447848a817a626cae08cedc9778b331c">More...</a><br /></td></tr>
<tr class="separator:ga447848a817a626cae08cedc9778b331c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6027ae13b2734f542a6e7beee11b8820"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga6027ae13b2734f542a6e7beee11b8820">GLM_ALIGNED_TYPEDEF</a> (highp_uint32, aligned_highp_uint32, 4)</td></tr>
<tr class="memdesc:ga6027ae13b2734f542a6e7beee11b8820"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga6027ae13b2734f542a6e7beee11b8820">More...</a><br /></td></tr>
<tr class="separator:ga6027ae13b2734f542a6e7beee11b8820"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2aca46c8608c95ef991ee4c332acde5f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga2aca46c8608c95ef991ee4c332acde5f">GLM_ALIGNED_TYPEDEF</a> (highp_uint64, aligned_highp_uint64, 8)</td></tr>
<tr class="memdesc:ga2aca46c8608c95ef991ee4c332acde5f"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga2aca46c8608c95ef991ee4c332acde5f">More...</a><br /></td></tr>
<tr class="separator:ga2aca46c8608c95ef991ee4c332acde5f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaff50b10dd1c48be324fdaffd18e2c7ea"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaff50b10dd1c48be324fdaffd18e2c7ea">GLM_ALIGNED_TYPEDEF</a> (highp_uint8_t, aligned_highp_uint8_t, 1)</td></tr>
<tr class="memdesc:gaff50b10dd1c48be324fdaffd18e2c7ea"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#gaff50b10dd1c48be324fdaffd18e2c7ea">More...</a><br /></td></tr>
<tr class="separator:gaff50b10dd1c48be324fdaffd18e2c7ea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9fc4421dbb833d5461e6d4e59dcfde55"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga9fc4421dbb833d5461e6d4e59dcfde55">GLM_ALIGNED_TYPEDEF</a> (highp_uint16_t, aligned_highp_uint16_t, 2)</td></tr>
<tr class="memdesc:ga9fc4421dbb833d5461e6d4e59dcfde55"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga9fc4421dbb833d5461e6d4e59dcfde55">More...</a><br /></td></tr>
<tr class="separator:ga9fc4421dbb833d5461e6d4e59dcfde55"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga329f1e2b94b33ba5e3918197030bcf03"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga329f1e2b94b33ba5e3918197030bcf03">GLM_ALIGNED_TYPEDEF</a> (highp_uint32_t, aligned_highp_uint32_t, 4)</td></tr>
<tr class="memdesc:ga329f1e2b94b33ba5e3918197030bcf03"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga329f1e2b94b33ba5e3918197030bcf03">More...</a><br /></td></tr>
<tr class="separator:ga329f1e2b94b33ba5e3918197030bcf03"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga71e646f7e301aa422328194162c9c998"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga71e646f7e301aa422328194162c9c998">GLM_ALIGNED_TYPEDEF</a> (highp_uint64_t, aligned_highp_uint64_t, 8)</td></tr>
<tr class="memdesc:ga71e646f7e301aa422328194162c9c998"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga71e646f7e301aa422328194162c9c998">More...</a><br /></td></tr>
<tr class="separator:ga71e646f7e301aa422328194162c9c998"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8942e09f479489441a7a5004c6d8cb66"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga8942e09f479489441a7a5004c6d8cb66">GLM_ALIGNED_TYPEDEF</a> (highp_u8, aligned_highp_u8, 1)</td></tr>
<tr class="memdesc:ga8942e09f479489441a7a5004c6d8cb66"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga8942e09f479489441a7a5004c6d8cb66">More...</a><br /></td></tr>
<tr class="separator:ga8942e09f479489441a7a5004c6d8cb66"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaab32497d6e4db16ee439dbedd64c5865"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaab32497d6e4db16ee439dbedd64c5865">GLM_ALIGNED_TYPEDEF</a> (highp_u16, aligned_highp_u16, 2)</td></tr>
<tr class="memdesc:gaab32497d6e4db16ee439dbedd64c5865"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#gaab32497d6e4db16ee439dbedd64c5865">More...</a><br /></td></tr>
<tr class="separator:gaab32497d6e4db16ee439dbedd64c5865"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaadbb34952eca8e3d7fe122c3e167742"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaaadbb34952eca8e3d7fe122c3e167742">GLM_ALIGNED_TYPEDEF</a> (highp_u32, aligned_highp_u32, 4)</td></tr>
<tr class="memdesc:gaaadbb34952eca8e3d7fe122c3e167742"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#gaaadbb34952eca8e3d7fe122c3e167742">More...</a><br /></td></tr>
<tr class="separator:gaaadbb34952eca8e3d7fe122c3e167742"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga92024d27c74a3650afb55ec8e024ed25"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga92024d27c74a3650afb55ec8e024ed25">GLM_ALIGNED_TYPEDEF</a> (highp_u64, aligned_highp_u64, 8)</td></tr>
<tr class="memdesc:ga92024d27c74a3650afb55ec8e024ed25"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga92024d27c74a3650afb55ec8e024ed25">More...</a><br /></td></tr>
<tr class="separator:ga92024d27c74a3650afb55ec8e024ed25"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabde1d0b4072df35453db76075ab896a6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gabde1d0b4072df35453db76075ab896a6">GLM_ALIGNED_TYPEDEF</a> (uint8, aligned_uint8, 1)</td></tr>
<tr class="memdesc:gabde1d0b4072df35453db76075ab896a6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#gabde1d0b4072df35453db76075ab896a6">More...</a><br /></td></tr>
<tr class="separator:gabde1d0b4072df35453db76075ab896a6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga06c296c9e398b294c8c9dd2a7693dcbb"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga06c296c9e398b294c8c9dd2a7693dcbb">GLM_ALIGNED_TYPEDEF</a> (uint16, aligned_uint16, 2)</td></tr>
<tr class="memdesc:ga06c296c9e398b294c8c9dd2a7693dcbb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga06c296c9e398b294c8c9dd2a7693dcbb">More...</a><br /></td></tr>
<tr class="separator:ga06c296c9e398b294c8c9dd2a7693dcbb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacf1744488c96ebd33c9f36ad33b2010a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gacf1744488c96ebd33c9f36ad33b2010a">GLM_ALIGNED_TYPEDEF</a> (uint32, aligned_uint32, 4)</td></tr>
<tr class="memdesc:gacf1744488c96ebd33c9f36ad33b2010a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#gacf1744488c96ebd33c9f36ad33b2010a">More...</a><br /></td></tr>
<tr class="separator:gacf1744488c96ebd33c9f36ad33b2010a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3328061a64c20ba59d5f9da24c2cd059"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga3328061a64c20ba59d5f9da24c2cd059">GLM_ALIGNED_TYPEDEF</a> (uint64, aligned_uint64, 8)</td></tr>
<tr class="memdesc:ga3328061a64c20ba59d5f9da24c2cd059"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga3328061a64c20ba59d5f9da24c2cd059">More...</a><br /></td></tr>
<tr class="separator:ga3328061a64c20ba59d5f9da24c2cd059"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf6ced36f13bae57f377bafa6f5fcc299"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaf6ced36f13bae57f377bafa6f5fcc299">GLM_ALIGNED_TYPEDEF</a> (uint8_t, aligned_uint8_t, 1)</td></tr>
<tr class="memdesc:gaf6ced36f13bae57f377bafa6f5fcc299"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#gaf6ced36f13bae57f377bafa6f5fcc299">More...</a><br /></td></tr>
<tr class="separator:gaf6ced36f13bae57f377bafa6f5fcc299"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafbc7fb7847bfc78a339d1d371c915c73"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gafbc7fb7847bfc78a339d1d371c915c73">GLM_ALIGNED_TYPEDEF</a> (uint16_t, aligned_uint16_t, 2)</td></tr>
<tr class="memdesc:gafbc7fb7847bfc78a339d1d371c915c73"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#gafbc7fb7847bfc78a339d1d371c915c73">More...</a><br /></td></tr>
<tr class="separator:gafbc7fb7847bfc78a339d1d371c915c73"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa86bc56a73fd8120b1121b5f5e6245ae"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaa86bc56a73fd8120b1121b5f5e6245ae">GLM_ALIGNED_TYPEDEF</a> (uint32_t, aligned_uint32_t, 4)</td></tr>
<tr class="memdesc:gaa86bc56a73fd8120b1121b5f5e6245ae"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#gaa86bc56a73fd8120b1121b5f5e6245ae">More...</a><br /></td></tr>
<tr class="separator:gaa86bc56a73fd8120b1121b5f5e6245ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga68c0b9e669060d0eb5ab8c3ddeb483d8"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga68c0b9e669060d0eb5ab8c3ddeb483d8">GLM_ALIGNED_TYPEDEF</a> (uint64_t, aligned_uint64_t, 8)</td></tr>
<tr class="memdesc:ga68c0b9e669060d0eb5ab8c3ddeb483d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga68c0b9e669060d0eb5ab8c3ddeb483d8">More...</a><br /></td></tr>
<tr class="separator:ga68c0b9e669060d0eb5ab8c3ddeb483d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4f3bab577daf3343e99cc005134bce86"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga4f3bab577daf3343e99cc005134bce86">GLM_ALIGNED_TYPEDEF</a> (u8, aligned_u8, 1)</td></tr>
<tr class="memdesc:ga4f3bab577daf3343e99cc005134bce86"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga4f3bab577daf3343e99cc005134bce86">More...</a><br /></td></tr>
<tr class="separator:ga4f3bab577daf3343e99cc005134bce86"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga13a2391339d0790d43b76d00a7611c4f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga13a2391339d0790d43b76d00a7611c4f">GLM_ALIGNED_TYPEDEF</a> (u16, aligned_u16, 2)</td></tr>
<tr class="memdesc:ga13a2391339d0790d43b76d00a7611c4f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga13a2391339d0790d43b76d00a7611c4f">More...</a><br /></td></tr>
<tr class="separator:ga13a2391339d0790d43b76d00a7611c4f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga197570e03acbc3d18ab698e342971e8f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga197570e03acbc3d18ab698e342971e8f">GLM_ALIGNED_TYPEDEF</a> (u32, aligned_u32, 4)</td></tr>
<tr class="memdesc:ga197570e03acbc3d18ab698e342971e8f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga197570e03acbc3d18ab698e342971e8f">More...</a><br /></td></tr>
<tr class="separator:ga197570e03acbc3d18ab698e342971e8f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0f033b21e145a1faa32c62ede5878993"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga0f033b21e145a1faa32c62ede5878993">GLM_ALIGNED_TYPEDEF</a> (u64, aligned_u64, 8)</td></tr>
<tr class="memdesc:ga0f033b21e145a1faa32c62ede5878993"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga0f033b21e145a1faa32c62ede5878993">More...</a><br /></td></tr>
<tr class="separator:ga0f033b21e145a1faa32c62ede5878993"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga509af83527f5cd512e9a7873590663aa"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga509af83527f5cd512e9a7873590663aa">GLM_ALIGNED_TYPEDEF</a> (uvec1, aligned_uvec1, 4)</td></tr>
<tr class="memdesc:ga509af83527f5cd512e9a7873590663aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga509af83527f5cd512e9a7873590663aa">More...</a><br /></td></tr>
<tr class="separator:ga509af83527f5cd512e9a7873590663aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga94e86186978c502c6dc0c0d9c4a30679"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga94e86186978c502c6dc0c0d9c4a30679">GLM_ALIGNED_TYPEDEF</a> (uvec2, aligned_uvec2, 8)</td></tr>
<tr class="memdesc:ga94e86186978c502c6dc0c0d9c4a30679"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit unsigned integer aligned vector of 2 components type.  <a href="a00364.html#ga94e86186978c502c6dc0c0d9c4a30679">More...</a><br /></td></tr>
<tr class="separator:ga94e86186978c502c6dc0c0d9c4a30679"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5cec574686a7f3c8ed24bb195c5e2d0a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga5cec574686a7f3c8ed24bb195c5e2d0a">GLM_ALIGNED_TYPEDEF</a> (uvec3, aligned_uvec3, 16)</td></tr>
<tr class="memdesc:ga5cec574686a7f3c8ed24bb195c5e2d0a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit unsigned integer aligned vector of 3 components type.  <a href="a00364.html#ga5cec574686a7f3c8ed24bb195c5e2d0a">More...</a><br /></td></tr>
<tr class="separator:ga5cec574686a7f3c8ed24bb195c5e2d0a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga47edfdcee9c89b1ebdaf20450323b1d4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga47edfdcee9c89b1ebdaf20450323b1d4">GLM_ALIGNED_TYPEDEF</a> (uvec4, aligned_uvec4, 16)</td></tr>
<tr class="memdesc:ga47edfdcee9c89b1ebdaf20450323b1d4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit unsigned integer aligned vector of 4 components type.  <a href="a00364.html#ga47edfdcee9c89b1ebdaf20450323b1d4">More...</a><br /></td></tr>
<tr class="separator:ga47edfdcee9c89b1ebdaf20450323b1d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5611d6718e3a00096918a64192e73a45"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga5611d6718e3a00096918a64192e73a45">GLM_ALIGNED_TYPEDEF</a> (u8vec1, aligned_u8vec1, 1)</td></tr>
<tr class="memdesc:ga5611d6718e3a00096918a64192e73a45"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga5611d6718e3a00096918a64192e73a45">More...</a><br /></td></tr>
<tr class="separator:ga5611d6718e3a00096918a64192e73a45"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga19837e6f72b60d994a805ef564c6c326"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga19837e6f72b60d994a805ef564c6c326">GLM_ALIGNED_TYPEDEF</a> (u8vec2, aligned_u8vec2, 2)</td></tr>
<tr class="memdesc:ga19837e6f72b60d994a805ef564c6c326"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit unsigned integer aligned vector of 2 components type.  <a href="a00364.html#ga19837e6f72b60d994a805ef564c6c326">More...</a><br /></td></tr>
<tr class="separator:ga19837e6f72b60d994a805ef564c6c326"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9740cf8e34f068049b42a2753f9601c2"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga9740cf8e34f068049b42a2753f9601c2">GLM_ALIGNED_TYPEDEF</a> (u8vec3, aligned_u8vec3, 4)</td></tr>
<tr class="memdesc:ga9740cf8e34f068049b42a2753f9601c2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit unsigned integer aligned vector of 3 components type.  <a href="a00364.html#ga9740cf8e34f068049b42a2753f9601c2">More...</a><br /></td></tr>
<tr class="separator:ga9740cf8e34f068049b42a2753f9601c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8b8588bb221448f5541a858903822a57"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga8b8588bb221448f5541a858903822a57">GLM_ALIGNED_TYPEDEF</a> (u8vec4, aligned_u8vec4, 4)</td></tr>
<tr class="memdesc:ga8b8588bb221448f5541a858903822a57"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit unsigned integer aligned vector of 4 components type.  <a href="a00364.html#ga8b8588bb221448f5541a858903822a57">More...</a><br /></td></tr>
<tr class="separator:ga8b8588bb221448f5541a858903822a57"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga991abe990c16de26b2129d6bc2f4c051"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga991abe990c16de26b2129d6bc2f4c051">GLM_ALIGNED_TYPEDEF</a> (u16vec1, aligned_u16vec1, 2)</td></tr>
<tr class="memdesc:ga991abe990c16de26b2129d6bc2f4c051"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga991abe990c16de26b2129d6bc2f4c051">More...</a><br /></td></tr>
<tr class="separator:ga991abe990c16de26b2129d6bc2f4c051"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac01bb9fc32a1cd76c2b80d030f71df4c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gac01bb9fc32a1cd76c2b80d030f71df4c">GLM_ALIGNED_TYPEDEF</a> (u16vec2, aligned_u16vec2, 4)</td></tr>
<tr class="memdesc:gac01bb9fc32a1cd76c2b80d030f71df4c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit unsigned integer aligned vector of 2 components type.  <a href="a00364.html#gac01bb9fc32a1cd76c2b80d030f71df4c">More...</a><br /></td></tr>
<tr class="separator:gac01bb9fc32a1cd76c2b80d030f71df4c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga09540dbca093793a36a8997e0d4bee77"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga09540dbca093793a36a8997e0d4bee77">GLM_ALIGNED_TYPEDEF</a> (u16vec3, aligned_u16vec3, 8)</td></tr>
<tr class="memdesc:ga09540dbca093793a36a8997e0d4bee77"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit unsigned integer aligned vector of 3 components type.  <a href="a00364.html#ga09540dbca093793a36a8997e0d4bee77">More...</a><br /></td></tr>
<tr class="separator:ga09540dbca093793a36a8997e0d4bee77"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaecafb5996f5a44f57e34d29c8670741e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaecafb5996f5a44f57e34d29c8670741e">GLM_ALIGNED_TYPEDEF</a> (u16vec4, aligned_u16vec4, 8)</td></tr>
<tr class="memdesc:gaecafb5996f5a44f57e34d29c8670741e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit unsigned integer aligned vector of 4 components type.  <a href="a00364.html#gaecafb5996f5a44f57e34d29c8670741e">More...</a><br /></td></tr>
<tr class="separator:gaecafb5996f5a44f57e34d29c8670741e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac6b161a04d2f8408fe1c9d857e8daac0"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gac6b161a04d2f8408fe1c9d857e8daac0">GLM_ALIGNED_TYPEDEF</a> (u32vec1, aligned_u32vec1, 4)</td></tr>
<tr class="memdesc:gac6b161a04d2f8408fe1c9d857e8daac0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#gac6b161a04d2f8408fe1c9d857e8daac0">More...</a><br /></td></tr>
<tr class="separator:gac6b161a04d2f8408fe1c9d857e8daac0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1fa0dfc8feb0fa17dab2acd43e05342b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga1fa0dfc8feb0fa17dab2acd43e05342b">GLM_ALIGNED_TYPEDEF</a> (u32vec2, aligned_u32vec2, 8)</td></tr>
<tr class="memdesc:ga1fa0dfc8feb0fa17dab2acd43e05342b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit unsigned integer aligned vector of 2 components type.  <a href="a00364.html#ga1fa0dfc8feb0fa17dab2acd43e05342b">More...</a><br /></td></tr>
<tr class="separator:ga1fa0dfc8feb0fa17dab2acd43e05342b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0019500abbfa9c66eff61ca75eaaed94"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga0019500abbfa9c66eff61ca75eaaed94">GLM_ALIGNED_TYPEDEF</a> (u32vec3, aligned_u32vec3, 16)</td></tr>
<tr class="memdesc:ga0019500abbfa9c66eff61ca75eaaed94"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit unsigned integer aligned vector of 3 components type.  <a href="a00364.html#ga0019500abbfa9c66eff61ca75eaaed94">More...</a><br /></td></tr>
<tr class="separator:ga0019500abbfa9c66eff61ca75eaaed94"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga14fd29d01dae7b08a04e9facbcc18824"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga14fd29d01dae7b08a04e9facbcc18824">GLM_ALIGNED_TYPEDEF</a> (u32vec4, aligned_u32vec4, 16)</td></tr>
<tr class="memdesc:ga14fd29d01dae7b08a04e9facbcc18824"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit unsigned integer aligned vector of 4 components type.  <a href="a00364.html#ga14fd29d01dae7b08a04e9facbcc18824">More...</a><br /></td></tr>
<tr class="separator:ga14fd29d01dae7b08a04e9facbcc18824"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab253845f534a67136f9619843cade903"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gab253845f534a67136f9619843cade903">GLM_ALIGNED_TYPEDEF</a> (u64vec1, aligned_u64vec1, 8)</td></tr>
<tr class="memdesc:gab253845f534a67136f9619843cade903"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#gab253845f534a67136f9619843cade903">More...</a><br /></td></tr>
<tr class="separator:gab253845f534a67136f9619843cade903"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga929427a7627940cdf3304f9c050b677d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga929427a7627940cdf3304f9c050b677d">GLM_ALIGNED_TYPEDEF</a> (u64vec2, aligned_u64vec2, 16)</td></tr>
<tr class="memdesc:ga929427a7627940cdf3304f9c050b677d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit unsigned integer aligned vector of 2 components type.  <a href="a00364.html#ga929427a7627940cdf3304f9c050b677d">More...</a><br /></td></tr>
<tr class="separator:ga929427a7627940cdf3304f9c050b677d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae373b6c04fdf9879f33d63e6949c037e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gae373b6c04fdf9879f33d63e6949c037e">GLM_ALIGNED_TYPEDEF</a> (u64vec3, aligned_u64vec3, 32)</td></tr>
<tr class="memdesc:gae373b6c04fdf9879f33d63e6949c037e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit unsigned integer aligned vector of 3 components type.  <a href="a00364.html#gae373b6c04fdf9879f33d63e6949c037e">More...</a><br /></td></tr>
<tr class="separator:gae373b6c04fdf9879f33d63e6949c037e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga53a8a03dca2015baec4584f45b8e9cdc"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga53a8a03dca2015baec4584f45b8e9cdc">GLM_ALIGNED_TYPEDEF</a> (u64vec4, aligned_u64vec4, 32)</td></tr>
<tr class="memdesc:ga53a8a03dca2015baec4584f45b8e9cdc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit unsigned integer aligned vector of 4 components type.  <a href="a00364.html#ga53a8a03dca2015baec4584f45b8e9cdc">More...</a><br /></td></tr>
<tr class="separator:ga53a8a03dca2015baec4584f45b8e9cdc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab3301bae94ef5bf59fbdd9a24e7d2a01"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gab3301bae94ef5bf59fbdd9a24e7d2a01">GLM_ALIGNED_TYPEDEF</a> (float32, aligned_float32, 4)</td></tr>
<tr class="memdesc:gab3301bae94ef5bf59fbdd9a24e7d2a01"><td class="mdescLeft">&#160;</td><td class="mdescRight">32 bit single-qualifier floating-point aligned scalar.  <a href="a00364.html#gab3301bae94ef5bf59fbdd9a24e7d2a01">More...</a><br /></td></tr>
<tr class="separator:gab3301bae94ef5bf59fbdd9a24e7d2a01"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gada9b0bea273d3ae0286f891533b9568f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gada9b0bea273d3ae0286f891533b9568f">GLM_ALIGNED_TYPEDEF</a> (float32_t, aligned_float32_t, 4)</td></tr>
<tr class="memdesc:gada9b0bea273d3ae0286f891533b9568f"><td class="mdescLeft">&#160;</td><td class="mdescRight">32 bit single-qualifier floating-point aligned scalar.  <a href="a00364.html#gada9b0bea273d3ae0286f891533b9568f">More...</a><br /></td></tr>
<tr class="separator:gada9b0bea273d3ae0286f891533b9568f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadbce23b9f23d77bb3884e289a574ebd5"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gadbce23b9f23d77bb3884e289a574ebd5">GLM_ALIGNED_TYPEDEF</a> (float32, aligned_f32, 4)</td></tr>
<tr class="memdesc:gadbce23b9f23d77bb3884e289a574ebd5"><td class="mdescLeft">&#160;</td><td class="mdescRight">32 bit single-qualifier floating-point aligned scalar.  <a href="a00364.html#gadbce23b9f23d77bb3884e289a574ebd5">More...</a><br /></td></tr>
<tr class="separator:gadbce23b9f23d77bb3884e289a574ebd5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga75930684ff2233171c573e603f216162"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga75930684ff2233171c573e603f216162">GLM_ALIGNED_TYPEDEF</a> (float64, aligned_float64, 8)</td></tr>
<tr class="memdesc:ga75930684ff2233171c573e603f216162"><td class="mdescLeft">&#160;</td><td class="mdescRight">64 bit double-qualifier floating-point aligned scalar.  <a href="a00364.html#ga75930684ff2233171c573e603f216162">More...</a><br /></td></tr>
<tr class="separator:ga75930684ff2233171c573e603f216162"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6e3a2d83b131336219a0f4c7cbba2a48"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga6e3a2d83b131336219a0f4c7cbba2a48">GLM_ALIGNED_TYPEDEF</a> (float64_t, aligned_float64_t, 8)</td></tr>
<tr class="memdesc:ga6e3a2d83b131336219a0f4c7cbba2a48"><td class="mdescLeft">&#160;</td><td class="mdescRight">64 bit double-qualifier floating-point aligned scalar.  <a href="a00364.html#ga6e3a2d83b131336219a0f4c7cbba2a48">More...</a><br /></td></tr>
<tr class="separator:ga6e3a2d83b131336219a0f4c7cbba2a48"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa4deaa0dea930c393d55e7a4352b0a20"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaa4deaa0dea930c393d55e7a4352b0a20">GLM_ALIGNED_TYPEDEF</a> (float64, aligned_f64, 8)</td></tr>
<tr class="memdesc:gaa4deaa0dea930c393d55e7a4352b0a20"><td class="mdescLeft">&#160;</td><td class="mdescRight">64 bit double-qualifier floating-point aligned scalar.  <a href="a00364.html#gaa4deaa0dea930c393d55e7a4352b0a20">More...</a><br /></td></tr>
<tr class="separator:gaa4deaa0dea930c393d55e7a4352b0a20"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga81bc497b2bfc6f80bab690c6ee28f0f9"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga81bc497b2bfc6f80bab690c6ee28f0f9">GLM_ALIGNED_TYPEDEF</a> (vec1, aligned_vec1, 4)</td></tr>
<tr class="memdesc:ga81bc497b2bfc6f80bab690c6ee28f0f9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 1 component.  <a href="a00364.html#ga81bc497b2bfc6f80bab690c6ee28f0f9">More...</a><br /></td></tr>
<tr class="separator:ga81bc497b2bfc6f80bab690c6ee28f0f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gada3e8f783e9d4b90006695a16c39d4d4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gada3e8f783e9d4b90006695a16c39d4d4">GLM_ALIGNED_TYPEDEF</a> (vec2, aligned_vec2, 8)</td></tr>
<tr class="memdesc:gada3e8f783e9d4b90006695a16c39d4d4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 2 components.  <a href="a00364.html#gada3e8f783e9d4b90006695a16c39d4d4">More...</a><br /></td></tr>
<tr class="separator:gada3e8f783e9d4b90006695a16c39d4d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab8d081fac3a38d6f55fa552f32168d32"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gab8d081fac3a38d6f55fa552f32168d32">GLM_ALIGNED_TYPEDEF</a> (vec3, aligned_vec3, 16)</td></tr>
<tr class="memdesc:gab8d081fac3a38d6f55fa552f32168d32"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 3 components.  <a href="a00364.html#gab8d081fac3a38d6f55fa552f32168d32">More...</a><br /></td></tr>
<tr class="separator:gab8d081fac3a38d6f55fa552f32168d32"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga12fe7b9769c964c5b48dcfd8b7f40198"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga12fe7b9769c964c5b48dcfd8b7f40198">GLM_ALIGNED_TYPEDEF</a> (vec4, aligned_vec4, 16)</td></tr>
<tr class="memdesc:ga12fe7b9769c964c5b48dcfd8b7f40198"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 4 components.  <a href="a00364.html#ga12fe7b9769c964c5b48dcfd8b7f40198">More...</a><br /></td></tr>
<tr class="separator:ga12fe7b9769c964c5b48dcfd8b7f40198"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaefab04611c7f8fe1fd9be3071efea6cc"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaefab04611c7f8fe1fd9be3071efea6cc">GLM_ALIGNED_TYPEDEF</a> (fvec1, aligned_fvec1, 4)</td></tr>
<tr class="memdesc:gaefab04611c7f8fe1fd9be3071efea6cc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 1 component.  <a href="a00364.html#gaefab04611c7f8fe1fd9be3071efea6cc">More...</a><br /></td></tr>
<tr class="separator:gaefab04611c7f8fe1fd9be3071efea6cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2543c05ba19b3bd19d45b1227390c5b4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga2543c05ba19b3bd19d45b1227390c5b4">GLM_ALIGNED_TYPEDEF</a> (fvec2, aligned_fvec2, 8)</td></tr>
<tr class="memdesc:ga2543c05ba19b3bd19d45b1227390c5b4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 2 components.  <a href="a00364.html#ga2543c05ba19b3bd19d45b1227390c5b4">More...</a><br /></td></tr>
<tr class="separator:ga2543c05ba19b3bd19d45b1227390c5b4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga009afd727fd657ef33a18754d6d28f60"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga009afd727fd657ef33a18754d6d28f60">GLM_ALIGNED_TYPEDEF</a> (fvec3, aligned_fvec3, 16)</td></tr>
<tr class="memdesc:ga009afd727fd657ef33a18754d6d28f60"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 3 components.  <a href="a00364.html#ga009afd727fd657ef33a18754d6d28f60">More...</a><br /></td></tr>
<tr class="separator:ga009afd727fd657ef33a18754d6d28f60"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2f26177e74bfb301a3d0e02ec3c3ef53"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga2f26177e74bfb301a3d0e02ec3c3ef53">GLM_ALIGNED_TYPEDEF</a> (fvec4, aligned_fvec4, 16)</td></tr>
<tr class="memdesc:ga2f26177e74bfb301a3d0e02ec3c3ef53"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 4 components.  <a href="a00364.html#ga2f26177e74bfb301a3d0e02ec3c3ef53">More...</a><br /></td></tr>
<tr class="separator:ga2f26177e74bfb301a3d0e02ec3c3ef53"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga309f495a1d6b75ddf195b674b65cb1e4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga309f495a1d6b75ddf195b674b65cb1e4">GLM_ALIGNED_TYPEDEF</a> (f32vec1, aligned_f32vec1, 4)</td></tr>
<tr class="memdesc:ga309f495a1d6b75ddf195b674b65cb1e4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 1 component.  <a href="a00364.html#ga309f495a1d6b75ddf195b674b65cb1e4">More...</a><br /></td></tr>
<tr class="separator:ga309f495a1d6b75ddf195b674b65cb1e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5e185865a2217d0cd47187644683a8c3"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga5e185865a2217d0cd47187644683a8c3">GLM_ALIGNED_TYPEDEF</a> (f32vec2, aligned_f32vec2, 8)</td></tr>
<tr class="memdesc:ga5e185865a2217d0cd47187644683a8c3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 2 components.  <a href="a00364.html#ga5e185865a2217d0cd47187644683a8c3">More...</a><br /></td></tr>
<tr class="separator:ga5e185865a2217d0cd47187644683a8c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gade4458b27b039b9ca34f8ec049f3115a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gade4458b27b039b9ca34f8ec049f3115a">GLM_ALIGNED_TYPEDEF</a> (f32vec3, aligned_f32vec3, 16)</td></tr>
<tr class="memdesc:gade4458b27b039b9ca34f8ec049f3115a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 3 components.  <a href="a00364.html#gade4458b27b039b9ca34f8ec049f3115a">More...</a><br /></td></tr>
<tr class="separator:gade4458b27b039b9ca34f8ec049f3115a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2e8a12c5e6a9c4ae4ddaeda1d1cffe3b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga2e8a12c5e6a9c4ae4ddaeda1d1cffe3b">GLM_ALIGNED_TYPEDEF</a> (f32vec4, aligned_f32vec4, 16)</td></tr>
<tr class="memdesc:ga2e8a12c5e6a9c4ae4ddaeda1d1cffe3b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 4 components.  <a href="a00364.html#ga2e8a12c5e6a9c4ae4ddaeda1d1cffe3b">More...</a><br /></td></tr>
<tr class="separator:ga2e8a12c5e6a9c4ae4ddaeda1d1cffe3b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3e0f35fa0c626285a8bad41707e7316c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga3e0f35fa0c626285a8bad41707e7316c">GLM_ALIGNED_TYPEDEF</a> (dvec1, aligned_dvec1, 8)</td></tr>
<tr class="memdesc:ga3e0f35fa0c626285a8bad41707e7316c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned vector of 1 component.  <a href="a00364.html#ga3e0f35fa0c626285a8bad41707e7316c">More...</a><br /></td></tr>
<tr class="separator:ga3e0f35fa0c626285a8bad41707e7316c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga78bfec2f185d1d365ea0a9ef1e3d45b8"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga78bfec2f185d1d365ea0a9ef1e3d45b8">GLM_ALIGNED_TYPEDEF</a> (dvec2, aligned_dvec2, 16)</td></tr>
<tr class="memdesc:ga78bfec2f185d1d365ea0a9ef1e3d45b8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned vector of 2 components.  <a href="a00364.html#ga78bfec2f185d1d365ea0a9ef1e3d45b8">More...</a><br /></td></tr>
<tr class="separator:ga78bfec2f185d1d365ea0a9ef1e3d45b8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga01fe6fee6db5df580b6724a7e681f069"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga01fe6fee6db5df580b6724a7e681f069">GLM_ALIGNED_TYPEDEF</a> (dvec3, aligned_dvec3, 32)</td></tr>
<tr class="memdesc:ga01fe6fee6db5df580b6724a7e681f069"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned vector of 3 components.  <a href="a00364.html#ga01fe6fee6db5df580b6724a7e681f069">More...</a><br /></td></tr>
<tr class="separator:ga01fe6fee6db5df580b6724a7e681f069"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga687d5b8f551d5af32425c0b2fba15e99"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga687d5b8f551d5af32425c0b2fba15e99">GLM_ALIGNED_TYPEDEF</a> (dvec4, aligned_dvec4, 32)</td></tr>
<tr class="memdesc:ga687d5b8f551d5af32425c0b2fba15e99"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned vector of 4 components.  <a href="a00364.html#ga687d5b8f551d5af32425c0b2fba15e99">More...</a><br /></td></tr>
<tr class="separator:ga687d5b8f551d5af32425c0b2fba15e99"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8e842371d46842ff8f1813419ba49d0f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga8e842371d46842ff8f1813419ba49d0f">GLM_ALIGNED_TYPEDEF</a> (f64vec1, aligned_f64vec1, 8)</td></tr>
<tr class="memdesc:ga8e842371d46842ff8f1813419ba49d0f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned vector of 1 component.  <a href="a00364.html#ga8e842371d46842ff8f1813419ba49d0f">More...</a><br /></td></tr>
<tr class="separator:ga8e842371d46842ff8f1813419ba49d0f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga32814aa0f19316b43134fc25f2aad2b9"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga32814aa0f19316b43134fc25f2aad2b9">GLM_ALIGNED_TYPEDEF</a> (f64vec2, aligned_f64vec2, 16)</td></tr>
<tr class="memdesc:ga32814aa0f19316b43134fc25f2aad2b9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned vector of 2 components.  <a href="a00364.html#ga32814aa0f19316b43134fc25f2aad2b9">More...</a><br /></td></tr>
<tr class="separator:ga32814aa0f19316b43134fc25f2aad2b9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf3d3bbc1e93909b689123b085e177a14"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaf3d3bbc1e93909b689123b085e177a14">GLM_ALIGNED_TYPEDEF</a> (f64vec3, aligned_f64vec3, 32)</td></tr>
<tr class="memdesc:gaf3d3bbc1e93909b689123b085e177a14"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned vector of 3 components.  <a href="a00364.html#gaf3d3bbc1e93909b689123b085e177a14">More...</a><br /></td></tr>
<tr class="separator:gaf3d3bbc1e93909b689123b085e177a14"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga804c654cead1139bd250f90f9bb01fad"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga804c654cead1139bd250f90f9bb01fad">GLM_ALIGNED_TYPEDEF</a> (f64vec4, aligned_f64vec4, 32)</td></tr>
<tr class="memdesc:ga804c654cead1139bd250f90f9bb01fad"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned vector of 4 components.  <a href="a00364.html#ga804c654cead1139bd250f90f9bb01fad">More...</a><br /></td></tr>
<tr class="separator:ga804c654cead1139bd250f90f9bb01fad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacce4ac532880b8c7469d3c31974420a1"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gacce4ac532880b8c7469d3c31974420a1">GLM_ALIGNED_TYPEDEF</a> (mat2, aligned_mat2, 16)</td></tr>
<tr class="memdesc:gacce4ac532880b8c7469d3c31974420a1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 1x1 matrix.  <a href="a00364.html#gacce4ac532880b8c7469d3c31974420a1">More...</a><br /></td></tr>
<tr class="separator:gacce4ac532880b8c7469d3c31974420a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0498e0e249a6faddaf96aa55d7f81c3b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga0498e0e249a6faddaf96aa55d7f81c3b">GLM_ALIGNED_TYPEDEF</a> (mat3, aligned_mat3, 16)</td></tr>
<tr class="memdesc:ga0498e0e249a6faddaf96aa55d7f81c3b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 3x3 matrix.  <a href="a00364.html#ga0498e0e249a6faddaf96aa55d7f81c3b">More...</a><br /></td></tr>
<tr class="separator:ga0498e0e249a6faddaf96aa55d7f81c3b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7435d87de82a0d652b35dc5b9cc718d5"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga7435d87de82a0d652b35dc5b9cc718d5">GLM_ALIGNED_TYPEDEF</a> (mat4, aligned_mat4, 16)</td></tr>
<tr class="memdesc:ga7435d87de82a0d652b35dc5b9cc718d5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 4x4 matrix.  <a href="a00364.html#ga7435d87de82a0d652b35dc5b9cc718d5">More...</a><br /></td></tr>
<tr class="separator:ga7435d87de82a0d652b35dc5b9cc718d5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga719da577361541a4c43a2dd1d0e361e1"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga719da577361541a4c43a2dd1d0e361e1">GLM_ALIGNED_TYPEDEF</a> (fmat2x2, aligned_fmat2, 16)</td></tr>
<tr class="memdesc:ga719da577361541a4c43a2dd1d0e361e1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 1x1 matrix.  <a href="a00364.html#ga719da577361541a4c43a2dd1d0e361e1">More...</a><br /></td></tr>
<tr class="separator:ga719da577361541a4c43a2dd1d0e361e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6e7ee4f541e1d7db66cd1a224caacafb"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga6e7ee4f541e1d7db66cd1a224caacafb">GLM_ALIGNED_TYPEDEF</a> (fmat3x3, aligned_fmat3, 16)</td></tr>
<tr class="memdesc:ga6e7ee4f541e1d7db66cd1a224caacafb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 3x3 matrix.  <a href="a00364.html#ga6e7ee4f541e1d7db66cd1a224caacafb">More...</a><br /></td></tr>
<tr class="separator:ga6e7ee4f541e1d7db66cd1a224caacafb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae5d672d359f2a39f63f98c7975057486"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gae5d672d359f2a39f63f98c7975057486">GLM_ALIGNED_TYPEDEF</a> (fmat4x4, aligned_fmat4, 16)</td></tr>
<tr class="memdesc:gae5d672d359f2a39f63f98c7975057486"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 4x4 matrix.  <a href="a00364.html#gae5d672d359f2a39f63f98c7975057486">More...</a><br /></td></tr>
<tr class="separator:gae5d672d359f2a39f63f98c7975057486"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6fa2df037dbfc5fe8c8e0b4db8a34953"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga6fa2df037dbfc5fe8c8e0b4db8a34953">GLM_ALIGNED_TYPEDEF</a> (fmat2x2, aligned_fmat2x2, 16)</td></tr>
<tr class="memdesc:ga6fa2df037dbfc5fe8c8e0b4db8a34953"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 1x1 matrix.  <a href="a00364.html#ga6fa2df037dbfc5fe8c8e0b4db8a34953">More...</a><br /></td></tr>
<tr class="separator:ga6fa2df037dbfc5fe8c8e0b4db8a34953"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0743b4f4f69a3227b82ff58f6abbad62"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga0743b4f4f69a3227b82ff58f6abbad62">GLM_ALIGNED_TYPEDEF</a> (fmat2x3, aligned_fmat2x3, 16)</td></tr>
<tr class="memdesc:ga0743b4f4f69a3227b82ff58f6abbad62"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 2x3 matrix.  <a href="a00364.html#ga0743b4f4f69a3227b82ff58f6abbad62">More...</a><br /></td></tr>
<tr class="separator:ga0743b4f4f69a3227b82ff58f6abbad62"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1a76b325fdf70f961d835edd182c63dd"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga1a76b325fdf70f961d835edd182c63dd">GLM_ALIGNED_TYPEDEF</a> (fmat2x4, aligned_fmat2x4, 16)</td></tr>
<tr class="memdesc:ga1a76b325fdf70f961d835edd182c63dd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 2x4 matrix.  <a href="a00364.html#ga1a76b325fdf70f961d835edd182c63dd">More...</a><br /></td></tr>
<tr class="separator:ga1a76b325fdf70f961d835edd182c63dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4b4e181cd041ba28c3163e7b8074aef0"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga4b4e181cd041ba28c3163e7b8074aef0">GLM_ALIGNED_TYPEDEF</a> (fmat3x2, aligned_fmat3x2, 16)</td></tr>
<tr class="memdesc:ga4b4e181cd041ba28c3163e7b8074aef0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 3x2 matrix.  <a href="a00364.html#ga4b4e181cd041ba28c3163e7b8074aef0">More...</a><br /></td></tr>
<tr class="separator:ga4b4e181cd041ba28c3163e7b8074aef0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga27b13f465abc8a40705698145e222c3f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga27b13f465abc8a40705698145e222c3f">GLM_ALIGNED_TYPEDEF</a> (fmat3x3, aligned_fmat3x3, 16)</td></tr>
<tr class="memdesc:ga27b13f465abc8a40705698145e222c3f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 3x3 matrix.  <a href="a00364.html#ga27b13f465abc8a40705698145e222c3f">More...</a><br /></td></tr>
<tr class="separator:ga27b13f465abc8a40705698145e222c3f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2608d19cc275830a6f8c0b6405625a4f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga2608d19cc275830a6f8c0b6405625a4f">GLM_ALIGNED_TYPEDEF</a> (fmat3x4, aligned_fmat3x4, 16)</td></tr>
<tr class="memdesc:ga2608d19cc275830a6f8c0b6405625a4f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 3x4 matrix.  <a href="a00364.html#ga2608d19cc275830a6f8c0b6405625a4f">More...</a><br /></td></tr>
<tr class="separator:ga2608d19cc275830a6f8c0b6405625a4f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga93f09768241358a287c4cca538f1f7e7"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga93f09768241358a287c4cca538f1f7e7">GLM_ALIGNED_TYPEDEF</a> (fmat4x2, aligned_fmat4x2, 16)</td></tr>
<tr class="memdesc:ga93f09768241358a287c4cca538f1f7e7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 4x2 matrix.  <a href="a00364.html#ga93f09768241358a287c4cca538f1f7e7">More...</a><br /></td></tr>
<tr class="separator:ga93f09768241358a287c4cca538f1f7e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7c117e3ecca089e10247b1d41d88aff9"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga7c117e3ecca089e10247b1d41d88aff9">GLM_ALIGNED_TYPEDEF</a> (fmat4x3, aligned_fmat4x3, 16)</td></tr>
<tr class="memdesc:ga7c117e3ecca089e10247b1d41d88aff9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 4x3 matrix.  <a href="a00364.html#ga7c117e3ecca089e10247b1d41d88aff9">More...</a><br /></td></tr>
<tr class="separator:ga7c117e3ecca089e10247b1d41d88aff9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga07c75cd04ba42dc37fa3e105f89455c5"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga07c75cd04ba42dc37fa3e105f89455c5">GLM_ALIGNED_TYPEDEF</a> (fmat4x4, aligned_fmat4x4, 16)</td></tr>
<tr class="memdesc:ga07c75cd04ba42dc37fa3e105f89455c5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 4x4 matrix.  <a href="a00364.html#ga07c75cd04ba42dc37fa3e105f89455c5">More...</a><br /></td></tr>
<tr class="separator:ga07c75cd04ba42dc37fa3e105f89455c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga65ff0d690a34a4d7f46f9b2eb51525ee"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga65ff0d690a34a4d7f46f9b2eb51525ee">GLM_ALIGNED_TYPEDEF</a> (f32mat2x2, aligned_f32mat2, 16)</td></tr>
<tr class="memdesc:ga65ff0d690a34a4d7f46f9b2eb51525ee"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 1x1 matrix.  <a href="a00364.html#ga65ff0d690a34a4d7f46f9b2eb51525ee">More...</a><br /></td></tr>
<tr class="separator:ga65ff0d690a34a4d7f46f9b2eb51525ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadd8ddbe2bf65ccede865ba2f510176dc"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gadd8ddbe2bf65ccede865ba2f510176dc">GLM_ALIGNED_TYPEDEF</a> (f32mat3x3, aligned_f32mat3, 16)</td></tr>
<tr class="memdesc:gadd8ddbe2bf65ccede865ba2f510176dc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 3x3 matrix.  <a href="a00364.html#gadd8ddbe2bf65ccede865ba2f510176dc">More...</a><br /></td></tr>
<tr class="separator:gadd8ddbe2bf65ccede865ba2f510176dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf18dbff14bf13d3ff540c517659ec045"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaf18dbff14bf13d3ff540c517659ec045">GLM_ALIGNED_TYPEDEF</a> (f32mat4x4, aligned_f32mat4, 16)</td></tr>
<tr class="memdesc:gaf18dbff14bf13d3ff540c517659ec045"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 4x4 matrix.  <a href="a00364.html#gaf18dbff14bf13d3ff540c517659ec045">More...</a><br /></td></tr>
<tr class="separator:gaf18dbff14bf13d3ff540c517659ec045"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga66339f6139bf7ff19e245beb33f61cc8"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga66339f6139bf7ff19e245beb33f61cc8">GLM_ALIGNED_TYPEDEF</a> (f32mat2x2, aligned_f32mat2x2, 16)</td></tr>
<tr class="memdesc:ga66339f6139bf7ff19e245beb33f61cc8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 1x1 matrix.  <a href="a00364.html#ga66339f6139bf7ff19e245beb33f61cc8">More...</a><br /></td></tr>
<tr class="separator:ga66339f6139bf7ff19e245beb33f61cc8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1558a48b3934011b52612809f443e46d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga1558a48b3934011b52612809f443e46d">GLM_ALIGNED_TYPEDEF</a> (f32mat2x3, aligned_f32mat2x3, 16)</td></tr>
<tr class="memdesc:ga1558a48b3934011b52612809f443e46d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 2x3 matrix.  <a href="a00364.html#ga1558a48b3934011b52612809f443e46d">More...</a><br /></td></tr>
<tr class="separator:ga1558a48b3934011b52612809f443e46d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa52e5732daa62851627021ad551c7680"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaa52e5732daa62851627021ad551c7680">GLM_ALIGNED_TYPEDEF</a> (f32mat2x4, aligned_f32mat2x4, 16)</td></tr>
<tr class="memdesc:gaa52e5732daa62851627021ad551c7680"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 2x4 matrix.  <a href="a00364.html#gaa52e5732daa62851627021ad551c7680">More...</a><br /></td></tr>
<tr class="separator:gaa52e5732daa62851627021ad551c7680"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac09663c42566bcb58d23c6781ac4e85a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gac09663c42566bcb58d23c6781ac4e85a">GLM_ALIGNED_TYPEDEF</a> (f32mat3x2, aligned_f32mat3x2, 16)</td></tr>
<tr class="memdesc:gac09663c42566bcb58d23c6781ac4e85a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 3x2 matrix.  <a href="a00364.html#gac09663c42566bcb58d23c6781ac4e85a">More...</a><br /></td></tr>
<tr class="separator:gac09663c42566bcb58d23c6781ac4e85a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3f510999e59e1b309113e1d561162b29"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga3f510999e59e1b309113e1d561162b29">GLM_ALIGNED_TYPEDEF</a> (f32mat3x3, aligned_f32mat3x3, 16)</td></tr>
<tr class="memdesc:ga3f510999e59e1b309113e1d561162b29"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 3x3 matrix.  <a href="a00364.html#ga3f510999e59e1b309113e1d561162b29">More...</a><br /></td></tr>
<tr class="separator:ga3f510999e59e1b309113e1d561162b29"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2c9c94f0c89cd71ce56551db6cf4aaec"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga2c9c94f0c89cd71ce56551db6cf4aaec">GLM_ALIGNED_TYPEDEF</a> (f32mat3x4, aligned_f32mat3x4, 16)</td></tr>
<tr class="memdesc:ga2c9c94f0c89cd71ce56551db6cf4aaec"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 3x4 matrix.  <a href="a00364.html#ga2c9c94f0c89cd71ce56551db6cf4aaec">More...</a><br /></td></tr>
<tr class="separator:ga2c9c94f0c89cd71ce56551db6cf4aaec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga99ce8274c750fbfdf0e70c95946a2875"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga99ce8274c750fbfdf0e70c95946a2875">GLM_ALIGNED_TYPEDEF</a> (f32mat4x2, aligned_f32mat4x2, 16)</td></tr>
<tr class="memdesc:ga99ce8274c750fbfdf0e70c95946a2875"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 4x2 matrix.  <a href="a00364.html#ga99ce8274c750fbfdf0e70c95946a2875">More...</a><br /></td></tr>
<tr class="separator:ga99ce8274c750fbfdf0e70c95946a2875"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9476ef66790239df53dbe66f3989c3b5"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga9476ef66790239df53dbe66f3989c3b5">GLM_ALIGNED_TYPEDEF</a> (f32mat4x3, aligned_f32mat4x3, 16)</td></tr>
<tr class="memdesc:ga9476ef66790239df53dbe66f3989c3b5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 4x3 matrix.  <a href="a00364.html#ga9476ef66790239df53dbe66f3989c3b5">More...</a><br /></td></tr>
<tr class="separator:ga9476ef66790239df53dbe66f3989c3b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacc429b3b0b49921e12713b6d31e14e1d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gacc429b3b0b49921e12713b6d31e14e1d">GLM_ALIGNED_TYPEDEF</a> (f32mat4x4, aligned_f32mat4x4, 16)</td></tr>
<tr class="memdesc:gacc429b3b0b49921e12713b6d31e14e1d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 4x4 matrix.  <a href="a00364.html#gacc429b3b0b49921e12713b6d31e14e1d">More...</a><br /></td></tr>
<tr class="separator:gacc429b3b0b49921e12713b6d31e14e1d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga88f6c6fa06e6e64479763e69444669cf"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga88f6c6fa06e6e64479763e69444669cf">GLM_ALIGNED_TYPEDEF</a> (f64mat2x2, aligned_f64mat2, 32)</td></tr>
<tr class="memdesc:ga88f6c6fa06e6e64479763e69444669cf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 1x1 matrix.  <a href="a00364.html#ga88f6c6fa06e6e64479763e69444669cf">More...</a><br /></td></tr>
<tr class="separator:ga88f6c6fa06e6e64479763e69444669cf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaae8e4639c991e64754145ab8e4c32083"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaae8e4639c991e64754145ab8e4c32083">GLM_ALIGNED_TYPEDEF</a> (f64mat3x3, aligned_f64mat3, 32)</td></tr>
<tr class="memdesc:gaae8e4639c991e64754145ab8e4c32083"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 3x3 matrix.  <a href="a00364.html#gaae8e4639c991e64754145ab8e4c32083">More...</a><br /></td></tr>
<tr class="separator:gaae8e4639c991e64754145ab8e4c32083"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6e9094f3feb3b5b49d0f83683a101fde"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga6e9094f3feb3b5b49d0f83683a101fde">GLM_ALIGNED_TYPEDEF</a> (f64mat4x4, aligned_f64mat4, 32)</td></tr>
<tr class="memdesc:ga6e9094f3feb3b5b49d0f83683a101fde"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 4x4 matrix.  <a href="a00364.html#ga6e9094f3feb3b5b49d0f83683a101fde">More...</a><br /></td></tr>
<tr class="separator:ga6e9094f3feb3b5b49d0f83683a101fde"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadbd2c639c03de1c3e9591b5a39f65559"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gadbd2c639c03de1c3e9591b5a39f65559">GLM_ALIGNED_TYPEDEF</a> (f64mat2x2, aligned_f64mat2x2, 32)</td></tr>
<tr class="memdesc:gadbd2c639c03de1c3e9591b5a39f65559"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 1x1 matrix.  <a href="a00364.html#gadbd2c639c03de1c3e9591b5a39f65559">More...</a><br /></td></tr>
<tr class="separator:gadbd2c639c03de1c3e9591b5a39f65559"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab059d7b9fe2094acc563b7223987499f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gab059d7b9fe2094acc563b7223987499f">GLM_ALIGNED_TYPEDEF</a> (f64mat2x3, aligned_f64mat2x3, 32)</td></tr>
<tr class="memdesc:gab059d7b9fe2094acc563b7223987499f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 2x3 matrix.  <a href="a00364.html#gab059d7b9fe2094acc563b7223987499f">More...</a><br /></td></tr>
<tr class="separator:gab059d7b9fe2094acc563b7223987499f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabbc811d1c52ed2b8cfcaff1378f75c69"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gabbc811d1c52ed2b8cfcaff1378f75c69">GLM_ALIGNED_TYPEDEF</a> (f64mat2x4, aligned_f64mat2x4, 32)</td></tr>
<tr class="memdesc:gabbc811d1c52ed2b8cfcaff1378f75c69"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 2x4 matrix.  <a href="a00364.html#gabbc811d1c52ed2b8cfcaff1378f75c69">More...</a><br /></td></tr>
<tr class="separator:gabbc811d1c52ed2b8cfcaff1378f75c69"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9ddf5212777734d2fd841a84439f3bdf"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga9ddf5212777734d2fd841a84439f3bdf">GLM_ALIGNED_TYPEDEF</a> (f64mat3x2, aligned_f64mat3x2, 32)</td></tr>
<tr class="memdesc:ga9ddf5212777734d2fd841a84439f3bdf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 3x2 matrix.  <a href="a00364.html#ga9ddf5212777734d2fd841a84439f3bdf">More...</a><br /></td></tr>
<tr class="separator:ga9ddf5212777734d2fd841a84439f3bdf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad1dda32ed09f94bfcf0a7d8edfb6cf13"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gad1dda32ed09f94bfcf0a7d8edfb6cf13">GLM_ALIGNED_TYPEDEF</a> (f64mat3x3, aligned_f64mat3x3, 32)</td></tr>
<tr class="memdesc:gad1dda32ed09f94bfcf0a7d8edfb6cf13"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 3x3 matrix.  <a href="a00364.html#gad1dda32ed09f94bfcf0a7d8edfb6cf13">More...</a><br /></td></tr>
<tr class="separator:gad1dda32ed09f94bfcf0a7d8edfb6cf13"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5875e0fa72f07e271e7931811cbbf31a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga5875e0fa72f07e271e7931811cbbf31a">GLM_ALIGNED_TYPEDEF</a> (f64mat3x4, aligned_f64mat3x4, 32)</td></tr>
<tr class="memdesc:ga5875e0fa72f07e271e7931811cbbf31a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 3x4 matrix.  <a href="a00364.html#ga5875e0fa72f07e271e7931811cbbf31a">More...</a><br /></td></tr>
<tr class="separator:ga5875e0fa72f07e271e7931811cbbf31a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga41e82cd6ac07f912ba2a2d45799dcf0d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga41e82cd6ac07f912ba2a2d45799dcf0d">GLM_ALIGNED_TYPEDEF</a> (f64mat4x2, aligned_f64mat4x2, 32)</td></tr>
<tr class="memdesc:ga41e82cd6ac07f912ba2a2d45799dcf0d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 4x2 matrix.  <a href="a00364.html#ga41e82cd6ac07f912ba2a2d45799dcf0d">More...</a><br /></td></tr>
<tr class="separator:ga41e82cd6ac07f912ba2a2d45799dcf0d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0892638d6ba773043b3d63d1d092622e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga0892638d6ba773043b3d63d1d092622e">GLM_ALIGNED_TYPEDEF</a> (f64mat4x3, aligned_f64mat4x3, 32)</td></tr>
<tr class="memdesc:ga0892638d6ba773043b3d63d1d092622e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 4x3 matrix.  <a href="a00364.html#ga0892638d6ba773043b3d63d1d092622e">More...</a><br /></td></tr>
<tr class="separator:ga0892638d6ba773043b3d63d1d092622e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga912a16432608b822f1e13607529934c1"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga912a16432608b822f1e13607529934c1">GLM_ALIGNED_TYPEDEF</a> (f64mat4x4, aligned_f64mat4x4, 32)</td></tr>
<tr class="memdesc:ga912a16432608b822f1e13607529934c1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 4x4 matrix.  <a href="a00364.html#ga912a16432608b822f1e13607529934c1">More...</a><br /></td></tr>
<tr class="separator:ga912a16432608b822f1e13607529934c1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafd945a8ea86b042aba410e0560df9a3d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gafd945a8ea86b042aba410e0560df9a3d">GLM_ALIGNED_TYPEDEF</a> (quat, aligned_quat, 16)</td></tr>
<tr class="memdesc:gafd945a8ea86b042aba410e0560df9a3d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned quaternion.  <a href="a00364.html#gafd945a8ea86b042aba410e0560df9a3d">More...</a><br /></td></tr>
<tr class="separator:gafd945a8ea86b042aba410e0560df9a3d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga19c2ba545d1f2f36bcb7b60c9a228622"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga19c2ba545d1f2f36bcb7b60c9a228622">GLM_ALIGNED_TYPEDEF</a> (quat, aligned_fquat, 16)</td></tr>
<tr class="memdesc:ga19c2ba545d1f2f36bcb7b60c9a228622"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned quaternion.  <a href="a00364.html#ga19c2ba545d1f2f36bcb7b60c9a228622">More...</a><br /></td></tr>
<tr class="separator:ga19c2ba545d1f2f36bcb7b60c9a228622"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaabc28c84a3288b697605d4688686f9a9"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaabc28c84a3288b697605d4688686f9a9">GLM_ALIGNED_TYPEDEF</a> (dquat, aligned_dquat, 32)</td></tr>
<tr class="memdesc:gaabc28c84a3288b697605d4688686f9a9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned quaternion.  <a href="a00364.html#gaabc28c84a3288b697605d4688686f9a9">More...</a><br /></td></tr>
<tr class="separator:gaabc28c84a3288b697605d4688686f9a9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1ed8aeb5ca67fade269a46105f1bf273"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga1ed8aeb5ca67fade269a46105f1bf273">GLM_ALIGNED_TYPEDEF</a> (f32quat, aligned_f32quat, 16)</td></tr>
<tr class="memdesc:ga1ed8aeb5ca67fade269a46105f1bf273"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned quaternion.  <a href="a00364.html#ga1ed8aeb5ca67fade269a46105f1bf273">More...</a><br /></td></tr>
<tr class="separator:ga1ed8aeb5ca67fade269a46105f1bf273"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga95cc03b8b475993fa50e05e38e203303"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a> (f64quat, aligned_f64quat, 32)</td></tr>
<tr class="memdesc:ga95cc03b8b475993fa50e05e38e203303"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned quaternion.  <a href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">More...</a><br /></td></tr>
<tr class="separator:ga95cc03b8b475993fa50e05e38e203303"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00364.html">GLM_GTX_type_aligned</a> </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00280.html" title="Features that implement in C++ the GLSL specification as closely as possible. ">Core features</a> (dependence) </dd>
<dd>
<a class="el" href="a00299.html" title="Include <glm/gtc/quaternion.hpp> to use the features of this extension. ">GLM_GTC_quaternion</a> (dependence) </dd></dl>

<p>Definition in file <a class="el" href="a00162_source.html">gtx/type_aligned.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
