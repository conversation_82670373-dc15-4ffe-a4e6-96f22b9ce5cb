<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_EXT_vector_float1_precision</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a>  </div>
  <div class="headertitle">
<div class="title">GLM_EXT_vector_float1_precision<div class="ingroups"><a class="el" href="a00285.html">Stable extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Exposes highp_vec1, mediump_vec1 and lowp_vec1 types.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga9e8ed21862a897c156c0b2abca70b1e9"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga9e8ed21862a897c156c0b2abca70b1e9"></a>
typedef vec&lt; 1, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00271.html#ga9e8ed21862a897c156c0b2abca70b1e9">highp_vec1</a></td></tr>
<tr class="memdesc:ga9e8ed21862a897c156c0b2abca70b1e9"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga9e8ed21862a897c156c0b2abca70b1e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0a57630f03031706b1d26a7d70d9184c"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga0a57630f03031706b1d26a7d70d9184c"></a>
typedef vec&lt; 1, float, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00271.html#ga0a57630f03031706b1d26a7d70d9184c">lowp_vec1</a></td></tr>
<tr class="memdesc:ga0a57630f03031706b1d26a7d70d9184c"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga0a57630f03031706b1d26a7d70d9184c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga645f53e6b8056609023a894b4e2beef4"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga645f53e6b8056609023a894b4e2beef4"></a>
typedef vec&lt; 1, float, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00271.html#ga645f53e6b8056609023a894b4e2beef4">mediump_vec1</a></td></tr>
<tr class="memdesc:ga645f53e6b8056609023a894b4e2beef4"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga645f53e6b8056609023a894b4e2beef4"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Exposes highp_vec1, mediump_vec1 and lowp_vec1 types. </p>
<p>Include &lt;<a class="el" href="a00207.html" title="GLM_EXT_vector_float1_precision ">glm/ext/vector_float1_precision.hpp</a>&gt; to use the features of this extension.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00270.html" title="Exposes single-precision floating point vector type with one component. ">GLM_EXT_vector_float1</a> extension. </dd></dl>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
