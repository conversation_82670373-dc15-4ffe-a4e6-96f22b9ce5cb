<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_associated_min_max</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_associated_min_max<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00007.html" title="GLM_GTX_associated_min_max ">glm/gtx/associated_min_max.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga7d9c8785230c8db60f72ec8975f1ba45"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U &gt; </td></tr>
<tr class="memitem:ga7d9c8785230c8db60f72ec8975f1ba45"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL U&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00308.html#ga7d9c8785230c8db60f72ec8975f1ba45">associatedMax</a> (T x, U a, T y, U b)</td></tr>
<tr class="memdesc:ga7d9c8785230c8db60f72ec8975f1ba45"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximum comparison between 2 variables and returns 2 associated variable values.  <a href="a00308.html#ga7d9c8785230c8db60f72ec8975f1ba45">More...</a><br /></td></tr>
<tr class="separator:ga7d9c8785230c8db60f72ec8975f1ba45"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5c6758bc50aa7fbe700f87123a045aad"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:ga5c6758bc50aa7fbe700f87123a045aad"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 2, U, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00308.html#ga5c6758bc50aa7fbe700f87123a045aad">associatedMax</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, U, Q &gt; const &amp;a, vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, U, Q &gt; const &amp;b)</td></tr>
<tr class="memdesc:ga5c6758bc50aa7fbe700f87123a045aad"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximum comparison between 2 variables and returns 2 associated variable values.  <a href="a00308.html#ga5c6758bc50aa7fbe700f87123a045aad">More...</a><br /></td></tr>
<tr class="separator:ga5c6758bc50aa7fbe700f87123a045aad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0d169d6ce26b03248df175f39005d77f"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:ga0d169d6ce26b03248df175f39005d77f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00308.html#ga0d169d6ce26b03248df175f39005d77f">associatedMax</a> (T x, vec&lt; L, U, Q &gt; const &amp;a, T y, vec&lt; L, U, Q &gt; const &amp;b)</td></tr>
<tr class="memdesc:ga0d169d6ce26b03248df175f39005d77f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximum comparison between 2 variables and returns 2 associated variable values.  <a href="a00308.html#ga0d169d6ce26b03248df175f39005d77f">More...</a><br /></td></tr>
<tr class="separator:ga0d169d6ce26b03248df175f39005d77f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4086269afabcb81dd7ded33cb3448653"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:ga4086269afabcb81dd7ded33cb3448653"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, U, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00308.html#ga4086269afabcb81dd7ded33cb3448653">associatedMax</a> (vec&lt; L, T, Q &gt; const &amp;x, U a, vec&lt; L, T, Q &gt; const &amp;y, U b)</td></tr>
<tr class="memdesc:ga4086269afabcb81dd7ded33cb3448653"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximum comparison between 2 variables and returns 2 associated variable values.  <a href="a00308.html#ga4086269afabcb81dd7ded33cb3448653">More...</a><br /></td></tr>
<tr class="separator:ga4086269afabcb81dd7ded33cb3448653"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaec891e363d91abbf3a4443cf2f652209"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U &gt; </td></tr>
<tr class="memitem:gaec891e363d91abbf3a4443cf2f652209"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL U&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00308.html#gaec891e363d91abbf3a4443cf2f652209">associatedMax</a> (T x, U a, T y, U b, T z, U c)</td></tr>
<tr class="memdesc:gaec891e363d91abbf3a4443cf2f652209"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximum comparison between 3 variables and returns 3 associated variable values.  <a href="a00308.html#gaec891e363d91abbf3a4443cf2f652209">More...</a><br /></td></tr>
<tr class="separator:gaec891e363d91abbf3a4443cf2f652209"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab84fdc35016a31e8cd0cbb8296bddf7c"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:gab84fdc35016a31e8cd0cbb8296bddf7c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, U, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00308.html#gab84fdc35016a31e8cd0cbb8296bddf7c">associatedMax</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, U, Q &gt; const &amp;a, vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, U, Q &gt; const &amp;b, vec&lt; L, T, Q &gt; const &amp;z, vec&lt; L, U, Q &gt; const &amp;c)</td></tr>
<tr class="memdesc:gab84fdc35016a31e8cd0cbb8296bddf7c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximum comparison between 3 variables and returns 3 associated variable values.  <a href="a00308.html#gab84fdc35016a31e8cd0cbb8296bddf7c">More...</a><br /></td></tr>
<tr class="separator:gab84fdc35016a31e8cd0cbb8296bddf7c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadd2a2002f4f2144bbc39eb2336dd2fba"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:gadd2a2002f4f2144bbc39eb2336dd2fba"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00308.html#gadd2a2002f4f2144bbc39eb2336dd2fba">associatedMax</a> (T x, vec&lt; L, U, Q &gt; const &amp;a, T y, vec&lt; L, U, Q &gt; const &amp;b, T z, vec&lt; L, U, Q &gt; const &amp;c)</td></tr>
<tr class="memdesc:gadd2a2002f4f2144bbc39eb2336dd2fba"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximum comparison between 3 variables and returns 3 associated variable values.  <a href="a00308.html#gadd2a2002f4f2144bbc39eb2336dd2fba">More...</a><br /></td></tr>
<tr class="separator:gadd2a2002f4f2144bbc39eb2336dd2fba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga19f59d1141a51a3b2108a9807af78f7f"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:ga19f59d1141a51a3b2108a9807af78f7f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, U, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00308.html#ga19f59d1141a51a3b2108a9807af78f7f">associatedMax</a> (vec&lt; L, T, Q &gt; const &amp;x, U a, vec&lt; L, T, Q &gt; const &amp;y, U b, vec&lt; L, T, Q &gt; const &amp;z, U c)</td></tr>
<tr class="memdesc:ga19f59d1141a51a3b2108a9807af78f7f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximum comparison between 3 variables and returns 3 associated variable values.  <a href="a00308.html#ga19f59d1141a51a3b2108a9807af78f7f">More...</a><br /></td></tr>
<tr class="separator:ga19f59d1141a51a3b2108a9807af78f7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3038ffcb43eaa6af75897a99a5047ccc"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U &gt; </td></tr>
<tr class="memitem:ga3038ffcb43eaa6af75897a99a5047ccc"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL U&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00308.html#ga3038ffcb43eaa6af75897a99a5047ccc">associatedMax</a> (T x, U a, T y, U b, T z, U c, T w, U d)</td></tr>
<tr class="memdesc:ga3038ffcb43eaa6af75897a99a5047ccc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximum comparison between 4 variables and returns 4 associated variable values.  <a href="a00308.html#ga3038ffcb43eaa6af75897a99a5047ccc">More...</a><br /></td></tr>
<tr class="separator:ga3038ffcb43eaa6af75897a99a5047ccc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf5ab0c428f8d1cd9e3b45fcfbf6423a6"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf5ab0c428f8d1cd9e3b45fcfbf6423a6"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, U, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00308.html#gaf5ab0c428f8d1cd9e3b45fcfbf6423a6">associatedMax</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, U, Q &gt; const &amp;a, vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, U, Q &gt; const &amp;b, vec&lt; L, T, Q &gt; const &amp;z, vec&lt; L, U, Q &gt; const &amp;c, vec&lt; L, T, Q &gt; const &amp;w, vec&lt; L, U, Q &gt; const &amp;d)</td></tr>
<tr class="memdesc:gaf5ab0c428f8d1cd9e3b45fcfbf6423a6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximum comparison between 4 variables and returns 4 associated variable values.  <a href="a00308.html#gaf5ab0c428f8d1cd9e3b45fcfbf6423a6">More...</a><br /></td></tr>
<tr class="separator:gaf5ab0c428f8d1cd9e3b45fcfbf6423a6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga11477c2c4b5b0bfd1b72b29df3725a9d"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:ga11477c2c4b5b0bfd1b72b29df3725a9d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, U, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00308.html#ga11477c2c4b5b0bfd1b72b29df3725a9d">associatedMax</a> (T x, vec&lt; L, U, Q &gt; const &amp;a, T y, vec&lt; L, U, Q &gt; const &amp;b, T z, vec&lt; L, U, Q &gt; const &amp;c, T w, vec&lt; L, U, Q &gt; const &amp;d)</td></tr>
<tr class="memdesc:ga11477c2c4b5b0bfd1b72b29df3725a9d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximum comparison between 4 variables and returns 4 associated variable values.  <a href="a00308.html#ga11477c2c4b5b0bfd1b72b29df3725a9d">More...</a><br /></td></tr>
<tr class="separator:ga11477c2c4b5b0bfd1b72b29df3725a9d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab9c3dd74cac899d2c625b5767ea3b3fb"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:gab9c3dd74cac899d2c625b5767ea3b3fb"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, U, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00308.html#gab9c3dd74cac899d2c625b5767ea3b3fb">associatedMax</a> (vec&lt; L, T, Q &gt; const &amp;x, U a, vec&lt; L, T, Q &gt; const &amp;y, U b, vec&lt; L, T, Q &gt; const &amp;z, U c, vec&lt; L, T, Q &gt; const &amp;w, U d)</td></tr>
<tr class="memdesc:gab9c3dd74cac899d2c625b5767ea3b3fb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximum comparison between 4 variables and returns 4 associated variable values.  <a href="a00308.html#gab9c3dd74cac899d2c625b5767ea3b3fb">More...</a><br /></td></tr>
<tr class="separator:gab9c3dd74cac899d2c625b5767ea3b3fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacc01bd272359572fc28437ae214a02df"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:gacc01bd272359572fc28437ae214a02df"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL U&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00308.html#gacc01bd272359572fc28437ae214a02df">associatedMin</a> (T x, U a, T y, U b)</td></tr>
<tr class="memdesc:gacc01bd272359572fc28437ae214a02df"><td class="mdescLeft">&#160;</td><td class="mdescRight">Minimum comparison between 2 variables and returns 2 associated variable values.  <a href="a00308.html#gacc01bd272359572fc28437ae214a02df">More...</a><br /></td></tr>
<tr class="separator:gacc01bd272359572fc28437ae214a02df"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac2f0dff90948f2e44386a5eafd941d1c"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:gac2f0dff90948f2e44386a5eafd941d1c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 2, U, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00308.html#gac2f0dff90948f2e44386a5eafd941d1c">associatedMin</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, U, Q &gt; const &amp;a, vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, U, Q &gt; const &amp;b)</td></tr>
<tr class="memdesc:gac2f0dff90948f2e44386a5eafd941d1c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Minimum comparison between 2 variables and returns 2 associated variable values.  <a href="a00308.html#gac2f0dff90948f2e44386a5eafd941d1c">More...</a><br /></td></tr>
<tr class="separator:gac2f0dff90948f2e44386a5eafd941d1c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacfec519c820331d023ef53a511749319"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:gacfec519c820331d023ef53a511749319"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, U, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00308.html#gacfec519c820331d023ef53a511749319">associatedMin</a> (T x, const vec&lt; L, U, Q &gt; &amp;a, T y, const vec&lt; L, U, Q &gt; &amp;b)</td></tr>
<tr class="memdesc:gacfec519c820331d023ef53a511749319"><td class="mdescLeft">&#160;</td><td class="mdescRight">Minimum comparison between 2 variables and returns 2 associated variable values.  <a href="a00308.html#gacfec519c820331d023ef53a511749319">More...</a><br /></td></tr>
<tr class="separator:gacfec519c820331d023ef53a511749319"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4757c7cab2d809124a8525d0a9deeb37"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:ga4757c7cab2d809124a8525d0a9deeb37"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, U, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00308.html#ga4757c7cab2d809124a8525d0a9deeb37">associatedMin</a> (vec&lt; L, T, Q &gt; const &amp;x, U a, vec&lt; L, T, Q &gt; const &amp;y, U b)</td></tr>
<tr class="memdesc:ga4757c7cab2d809124a8525d0a9deeb37"><td class="mdescLeft">&#160;</td><td class="mdescRight">Minimum comparison between 2 variables and returns 2 associated variable values.  <a href="a00308.html#ga4757c7cab2d809124a8525d0a9deeb37">More...</a><br /></td></tr>
<tr class="separator:ga4757c7cab2d809124a8525d0a9deeb37"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad0aa8f86259a26d839d34a3577a923fc"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U &gt; </td></tr>
<tr class="memitem:gad0aa8f86259a26d839d34a3577a923fc"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL U&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00308.html#gad0aa8f86259a26d839d34a3577a923fc">associatedMin</a> (T x, U a, T y, U b, T z, U c)</td></tr>
<tr class="memdesc:gad0aa8f86259a26d839d34a3577a923fc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Minimum comparison between 3 variables and returns 3 associated variable values.  <a href="a00308.html#gad0aa8f86259a26d839d34a3577a923fc">More...</a><br /></td></tr>
<tr class="separator:gad0aa8f86259a26d839d34a3577a923fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga723e5411cebc7ffbd5c81ffeec61127d"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:ga723e5411cebc7ffbd5c81ffeec61127d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, U, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00308.html#ga723e5411cebc7ffbd5c81ffeec61127d">associatedMin</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, U, Q &gt; const &amp;a, vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, U, Q &gt; const &amp;b, vec&lt; L, T, Q &gt; const &amp;z, vec&lt; L, U, Q &gt; const &amp;c)</td></tr>
<tr class="memdesc:ga723e5411cebc7ffbd5c81ffeec61127d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Minimum comparison between 3 variables and returns 3 associated variable values.  <a href="a00308.html#ga723e5411cebc7ffbd5c81ffeec61127d">More...</a><br /></td></tr>
<tr class="separator:ga723e5411cebc7ffbd5c81ffeec61127d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga432224ebe2085eaa2b63a077ecbbbff6"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U &gt; </td></tr>
<tr class="memitem:ga432224ebe2085eaa2b63a077ecbbbff6"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL U&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00308.html#ga432224ebe2085eaa2b63a077ecbbbff6">associatedMin</a> (T x, U a, T y, U b, T z, U c, T w, U d)</td></tr>
<tr class="memdesc:ga432224ebe2085eaa2b63a077ecbbbff6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Minimum comparison between 4 variables and returns 4 associated variable values.  <a href="a00308.html#ga432224ebe2085eaa2b63a077ecbbbff6">More...</a><br /></td></tr>
<tr class="separator:ga432224ebe2085eaa2b63a077ecbbbff6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga66b08118bc88f0494bcacb7cdb940556"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:ga66b08118bc88f0494bcacb7cdb940556"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, U, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00308.html#ga66b08118bc88f0494bcacb7cdb940556">associatedMin</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, U, Q &gt; const &amp;a, vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, U, Q &gt; const &amp;b, vec&lt; L, T, Q &gt; const &amp;z, vec&lt; L, U, Q &gt; const &amp;c, vec&lt; L, T, Q &gt; const &amp;w, vec&lt; L, U, Q &gt; const &amp;d)</td></tr>
<tr class="memdesc:ga66b08118bc88f0494bcacb7cdb940556"><td class="mdescLeft">&#160;</td><td class="mdescRight">Minimum comparison between 4 variables and returns 4 associated variable values.  <a href="a00308.html#ga66b08118bc88f0494bcacb7cdb940556">More...</a><br /></td></tr>
<tr class="separator:ga66b08118bc88f0494bcacb7cdb940556"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga78c28fde1a7080fb7420bd88e68c6c68"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:ga78c28fde1a7080fb7420bd88e68c6c68"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, U, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00308.html#ga78c28fde1a7080fb7420bd88e68c6c68">associatedMin</a> (T x, vec&lt; L, U, Q &gt; const &amp;a, T y, vec&lt; L, U, Q &gt; const &amp;b, T z, vec&lt; L, U, Q &gt; const &amp;c, T w, vec&lt; L, U, Q &gt; const &amp;d)</td></tr>
<tr class="memdesc:ga78c28fde1a7080fb7420bd88e68c6c68"><td class="mdescLeft">&#160;</td><td class="mdescRight">Minimum comparison between 4 variables and returns 4 associated variable values.  <a href="a00308.html#ga78c28fde1a7080fb7420bd88e68c6c68">More...</a><br /></td></tr>
<tr class="separator:ga78c28fde1a7080fb7420bd88e68c6c68"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2db7e351994baee78540a562d4bb6d3b"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:ga2db7e351994baee78540a562d4bb6d3b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, U, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00308.html#ga2db7e351994baee78540a562d4bb6d3b">associatedMin</a> (vec&lt; L, T, Q &gt; const &amp;x, U a, vec&lt; L, T, Q &gt; const &amp;y, U b, vec&lt; L, T, Q &gt; const &amp;z, U c, vec&lt; L, T, Q &gt; const &amp;w, U d)</td></tr>
<tr class="memdesc:ga2db7e351994baee78540a562d4bb6d3b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Minimum comparison between 4 variables and returns 4 associated variable values.  <a href="a00308.html#ga2db7e351994baee78540a562d4bb6d3b">More...</a><br /></td></tr>
<tr class="separator:ga2db7e351994baee78540a562d4bb6d3b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00007.html" title="GLM_GTX_associated_min_max ">glm/gtx/associated_min_max.hpp</a>&gt; to use the features of this extension. </p>
<p>Min and max functions that return associated values not the compared onces. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga7d9c8785230c8db60f72ec8975f1ba45"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL U glm::associatedMax </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>b</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Maximum comparison between 2 variables and returns 2 associated variable values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00308.html" title="Include <glm/gtx/associated_min_max.hpp> to use the features of this extension. ">GLM_GTX_associated_min_max</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5c6758bc50aa7fbe700f87123a045aad"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;2, U, Q&gt; glm::associatedMax </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Maximum comparison between 2 variables and returns 2 associated variable values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00308.html" title="Include <glm/gtx/associated_min_max.hpp> to use the features of this extension. ">GLM_GTX_associated_min_max</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga0d169d6ce26b03248df175f39005d77f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::associatedMax </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Maximum comparison between 2 variables and returns 2 associated variable values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00308.html" title="Include <glm/gtx/associated_min_max.hpp> to use the features of this extension. ">GLM_GTX_associated_min_max</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga4086269afabcb81dd7ded33cb3448653"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, U, Q&gt; glm::associatedMax </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>b</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Maximum comparison between 2 variables and returns 2 associated variable values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00308.html" title="Include <glm/gtx/associated_min_max.hpp> to use the features of this extension. ">GLM_GTX_associated_min_max</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaec891e363d91abbf3a4443cf2f652209"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL U glm::associatedMax </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>c</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Maximum comparison between 3 variables and returns 3 associated variable values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00308.html" title="Include <glm/gtx/associated_min_max.hpp> to use the features of this extension. ">GLM_GTX_associated_min_max</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gab84fdc35016a31e8cd0cbb8296bddf7c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, U, Q&gt; glm::associatedMax </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>c</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Maximum comparison between 3 variables and returns 3 associated variable values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00308.html" title="Include <glm/gtx/associated_min_max.hpp> to use the features of this extension. ">GLM_GTX_associated_min_max</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gadd2a2002f4f2144bbc39eb2336dd2fba"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::associatedMax </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>c</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Maximum comparison between 3 variables and returns 3 associated variable values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00308.html" title="Include <glm/gtx/associated_min_max.hpp> to use the features of this extension. ">GLM_GTX_associated_min_max</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga19f59d1141a51a3b2108a9807af78f7f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, U, Q&gt; glm::associatedMax </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>c</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Maximum comparison between 3 variables and returns 3 associated variable values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00308.html" title="Include <glm/gtx/associated_min_max.hpp> to use the features of this extension. ">GLM_GTX_associated_min_max</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga3038ffcb43eaa6af75897a99a5047ccc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL U glm::associatedMax </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>c</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>w</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>d</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Maximum comparison between 4 variables and returns 4 associated variable values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00308.html" title="Include <glm/gtx/associated_min_max.hpp> to use the features of this extension. ">GLM_GTX_associated_min_max</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaf5ab0c428f8d1cd9e3b45fcfbf6423a6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, U, Q&gt; glm::associatedMax </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>c</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>w</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>d</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Maximum comparison between 4 variables and returns 4 associated variable values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00308.html" title="Include <glm/gtx/associated_min_max.hpp> to use the features of this extension. ">GLM_GTX_associated_min_max</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga11477c2c4b5b0bfd1b72b29df3725a9d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, U, Q&gt; glm::associatedMax </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>c</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>w</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>d</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Maximum comparison between 4 variables and returns 4 associated variable values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00308.html" title="Include <glm/gtx/associated_min_max.hpp> to use the features of this extension. ">GLM_GTX_associated_min_max</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gab9c3dd74cac899d2c625b5767ea3b3fb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, U, Q&gt; glm::associatedMax </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>c</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>w</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>d</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Maximum comparison between 4 variables and returns 4 associated variable values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00308.html" title="Include <glm/gtx/associated_min_max.hpp> to use the features of this extension. ">GLM_GTX_associated_min_max</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gacc01bd272359572fc28437ae214a02df"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL U glm::associatedMin </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>b</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Minimum comparison between 2 variables and returns 2 associated variable values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00308.html" title="Include <glm/gtx/associated_min_max.hpp> to use the features of this extension. ">GLM_GTX_associated_min_max</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gac2f0dff90948f2e44386a5eafd941d1c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;2, U, Q&gt; glm::associatedMin </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Minimum comparison between 2 variables and returns 2 associated variable values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00308.html" title="Include <glm/gtx/associated_min_max.hpp> to use the features of this extension. ">GLM_GTX_associated_min_max</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gacfec519c820331d023ef53a511749319"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, U, Q&gt; glm::associatedMin </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const vec&lt; L, U, Q &gt; &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const vec&lt; L, U, Q &gt; &amp;&#160;</td>
          <td class="paramname"><em>b</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Minimum comparison between 2 variables and returns 2 associated variable values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00308.html" title="Include <glm/gtx/associated_min_max.hpp> to use the features of this extension. ">GLM_GTX_associated_min_max</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga4757c7cab2d809124a8525d0a9deeb37"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, U, Q&gt; glm::associatedMin </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>b</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Minimum comparison between 2 variables and returns 2 associated variable values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00308.html" title="Include <glm/gtx/associated_min_max.hpp> to use the features of this extension. ">GLM_GTX_associated_min_max</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gad0aa8f86259a26d839d34a3577a923fc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL U glm::associatedMin </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>c</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Minimum comparison between 3 variables and returns 3 associated variable values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00308.html" title="Include <glm/gtx/associated_min_max.hpp> to use the features of this extension. ">GLM_GTX_associated_min_max</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga723e5411cebc7ffbd5c81ffeec61127d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, U, Q&gt; glm::associatedMin </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>c</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Minimum comparison between 3 variables and returns 3 associated variable values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00308.html" title="Include <glm/gtx/associated_min_max.hpp> to use the features of this extension. ">GLM_GTX_associated_min_max</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga432224ebe2085eaa2b63a077ecbbbff6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL U glm::associatedMin </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>c</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>w</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>d</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Minimum comparison between 4 variables and returns 4 associated variable values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00308.html" title="Include <glm/gtx/associated_min_max.hpp> to use the features of this extension. ">GLM_GTX_associated_min_max</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga66b08118bc88f0494bcacb7cdb940556"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, U, Q&gt; glm::associatedMin </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>c</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>w</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>d</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Minimum comparison between 4 variables and returns 4 associated variable values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00308.html" title="Include <glm/gtx/associated_min_max.hpp> to use the features of this extension. ">GLM_GTX_associated_min_max</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga78c28fde1a7080fb7420bd88e68c6c68"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, U, Q&gt; glm::associatedMin </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>c</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>w</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>d</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Minimum comparison between 4 variables and returns 4 associated variable values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00308.html" title="Include <glm/gtx/associated_min_max.hpp> to use the features of this extension. ">GLM_GTX_associated_min_max</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2db7e351994baee78540a562d4bb6d3b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, U, Q&gt; glm::associatedMin </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>c</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>w</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">U&#160;</td>
          <td class="paramname"><em>d</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Minimum comparison between 4 variables and returns 4 associated variable values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00308.html" title="Include <glm/gtx/associated_min_max.hpp> to use the features of this extension. ">GLM_GTX_associated_min_max</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
