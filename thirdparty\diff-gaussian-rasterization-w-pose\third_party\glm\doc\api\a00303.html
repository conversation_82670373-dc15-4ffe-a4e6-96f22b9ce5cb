<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTC_type_aligned</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTC_type_aligned<div class="ingroups"><a class="el" href="a00286.html">Recommended extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00161.html" title="GLM_GTC_type_aligned ">glm/gtc/type_aligned.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga780a35f764020f553a9601a3fcdcd059"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga780a35f764020f553a9601a3fcdcd059"></a>
typedef aligned_highp_bvec1&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga780a35f764020f553a9601a3fcdcd059">aligned_bvec1</a></td></tr>
<tr class="memdesc:ga780a35f764020f553a9601a3fcdcd059"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector aligned in memory of bool values. <br /></td></tr>
<tr class="separator:ga780a35f764020f553a9601a3fcdcd059"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae766b317c5afec852bfb3d74a3c54bc8"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gae766b317c5afec852bfb3d74a3c54bc8"></a>
typedef aligned_highp_bvec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gae766b317c5afec852bfb3d74a3c54bc8">aligned_bvec2</a></td></tr>
<tr class="memdesc:gae766b317c5afec852bfb3d74a3c54bc8"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector aligned in memory of bool values. <br /></td></tr>
<tr class="separator:gae766b317c5afec852bfb3d74a3c54bc8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae1964ba70d15915e5b710926decbb3cb"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gae1964ba70d15915e5b710926decbb3cb"></a>
typedef aligned_highp_bvec3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gae1964ba70d15915e5b710926decbb3cb">aligned_bvec3</a></td></tr>
<tr class="memdesc:gae1964ba70d15915e5b710926decbb3cb"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector aligned in memory of bool values. <br /></td></tr>
<tr class="separator:gae1964ba70d15915e5b710926decbb3cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae164a1f7879f828bc35e50b79d786b05"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gae164a1f7879f828bc35e50b79d786b05"></a>
typedef aligned_highp_bvec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gae164a1f7879f828bc35e50b79d786b05">aligned_bvec4</a></td></tr>
<tr class="memdesc:gae164a1f7879f828bc35e50b79d786b05"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector aligned in memory of bool values. <br /></td></tr>
<tr class="separator:gae164a1f7879f828bc35e50b79d786b05"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6783859382677d35fcd5dac7dcbefdbd"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga6783859382677d35fcd5dac7dcbefdbd"></a>
typedef aligned_highp_dmat2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga6783859382677d35fcd5dac7dcbefdbd">aligned_dmat2</a></td></tr>
<tr class="memdesc:ga6783859382677d35fcd5dac7dcbefdbd"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix tightly aligned in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga6783859382677d35fcd5dac7dcbefdbd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga449a3ec2dde6b6bb4bb94c49a6aad388"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga449a3ec2dde6b6bb4bb94c49a6aad388"></a>
typedef aligned_highp_dmat2x2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga449a3ec2dde6b6bb4bb94c49a6aad388">aligned_dmat2x2</a></td></tr>
<tr class="memdesc:ga449a3ec2dde6b6bb4bb94c49a6aad388"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix tightly aligned in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga449a3ec2dde6b6bb4bb94c49a6aad388"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga53d519a7b1bfb69076b3ec206a6b3bd1"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga53d519a7b1bfb69076b3ec206a6b3bd1"></a>
typedef aligned_highp_dmat2x3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga53d519a7b1bfb69076b3ec206a6b3bd1">aligned_dmat2x3</a></td></tr>
<tr class="memdesc:ga53d519a7b1bfb69076b3ec206a6b3bd1"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 3 matrix tightly aligned in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga53d519a7b1bfb69076b3ec206a6b3bd1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5ccb2baeb0ab57b818c24e0d486c59d0"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga5ccb2baeb0ab57b818c24e0d486c59d0"></a>
typedef aligned_highp_dmat2x4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga5ccb2baeb0ab57b818c24e0d486c59d0">aligned_dmat2x4</a></td></tr>
<tr class="memdesc:ga5ccb2baeb0ab57b818c24e0d486c59d0"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 4 matrix tightly aligned in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga5ccb2baeb0ab57b818c24e0d486c59d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga19aa695ffdb45ce29f7ea0b5029627de"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga19aa695ffdb45ce29f7ea0b5029627de"></a>
typedef aligned_highp_dmat3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga19aa695ffdb45ce29f7ea0b5029627de">aligned_dmat3</a></td></tr>
<tr class="memdesc:ga19aa695ffdb45ce29f7ea0b5029627de"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix tightly aligned in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga19aa695ffdb45ce29f7ea0b5029627de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5f5123d834bd1170edf8c386834e112c"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga5f5123d834bd1170edf8c386834e112c"></a>
typedef aligned_highp_dmat3x2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga5f5123d834bd1170edf8c386834e112c">aligned_dmat3x2</a></td></tr>
<tr class="memdesc:ga5f5123d834bd1170edf8c386834e112c"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 2 matrix tightly aligned in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga5f5123d834bd1170edf8c386834e112c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga635bf3732281a2c2ca54d8f9d33d178f"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga635bf3732281a2c2ca54d8f9d33d178f"></a>
typedef aligned_highp_dmat3x3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga635bf3732281a2c2ca54d8f9d33d178f">aligned_dmat3x3</a></td></tr>
<tr class="memdesc:ga635bf3732281a2c2ca54d8f9d33d178f"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix tightly aligned in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga635bf3732281a2c2ca54d8f9d33d178f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf488c6ad88c185054595d4d5c7ba5b9d"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaf488c6ad88c185054595d4d5c7ba5b9d"></a>
typedef aligned_highp_dmat3x4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaf488c6ad88c185054595d4d5c7ba5b9d">aligned_dmat3x4</a></td></tr>
<tr class="memdesc:gaf488c6ad88c185054595d4d5c7ba5b9d"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 4 matrix tightly aligned in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gaf488c6ad88c185054595d4d5c7ba5b9d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga001bb387ae8192fa94dbd8b23b600439"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga001bb387ae8192fa94dbd8b23b600439"></a>
typedef aligned_highp_dmat4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga001bb387ae8192fa94dbd8b23b600439">aligned_dmat4</a></td></tr>
<tr class="memdesc:ga001bb387ae8192fa94dbd8b23b600439"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix tightly aligned in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga001bb387ae8192fa94dbd8b23b600439"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa409cfb737bd59b68dc683e9b03930cc"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaa409cfb737bd59b68dc683e9b03930cc"></a>
typedef aligned_highp_dmat4x2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaa409cfb737bd59b68dc683e9b03930cc">aligned_dmat4x2</a></td></tr>
<tr class="memdesc:gaa409cfb737bd59b68dc683e9b03930cc"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 2 matrix tightly aligned in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gaa409cfb737bd59b68dc683e9b03930cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga621e89ca1dbdcb7b5a3e7de237c44121"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga621e89ca1dbdcb7b5a3e7de237c44121"></a>
typedef aligned_highp_dmat4x3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga621e89ca1dbdcb7b5a3e7de237c44121">aligned_dmat4x3</a></td></tr>
<tr class="memdesc:ga621e89ca1dbdcb7b5a3e7de237c44121"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 3 matrix tightly aligned in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga621e89ca1dbdcb7b5a3e7de237c44121"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac9bda778d0b7ad82f656dab99b71857a"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gac9bda778d0b7ad82f656dab99b71857a"></a>
typedef aligned_highp_dmat4x4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gac9bda778d0b7ad82f656dab99b71857a">aligned_dmat4x4</a></td></tr>
<tr class="memdesc:gac9bda778d0b7ad82f656dab99b71857a"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix tightly aligned in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gac9bda778d0b7ad82f656dab99b71857a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4974f46ae5a19415d91316960a53617a"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga4974f46ae5a19415d91316960a53617a"></a>
typedef aligned_highp_dvec1&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga4974f46ae5a19415d91316960a53617a">aligned_dvec1</a></td></tr>
<tr class="memdesc:ga4974f46ae5a19415d91316960a53617a"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector aligned in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga4974f46ae5a19415d91316960a53617a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga18d859f87122b2b3b2992ffe86dbebc0"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga18d859f87122b2b3b2992ffe86dbebc0"></a>
typedef aligned_highp_dvec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga18d859f87122b2b3b2992ffe86dbebc0">aligned_dvec2</a></td></tr>
<tr class="memdesc:ga18d859f87122b2b3b2992ffe86dbebc0"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector aligned in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga18d859f87122b2b3b2992ffe86dbebc0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa37869eea77d28419b2fb0ff70b69bf0"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaa37869eea77d28419b2fb0ff70b69bf0"></a>
typedef aligned_highp_dvec3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaa37869eea77d28419b2fb0ff70b69bf0">aligned_dvec3</a></td></tr>
<tr class="memdesc:gaa37869eea77d28419b2fb0ff70b69bf0"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector aligned in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gaa37869eea77d28419b2fb0ff70b69bf0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8a9f0a4795ccc442fa9901845026f9f5"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga8a9f0a4795ccc442fa9901845026f9f5"></a>
typedef aligned_highp_dvec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga8a9f0a4795ccc442fa9901845026f9f5">aligned_dvec4</a></td></tr>
<tr class="memdesc:ga8a9f0a4795ccc442fa9901845026f9f5"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector aligned in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga8a9f0a4795ccc442fa9901845026f9f5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga862843a45b01c35ffe4d44c47ea774ad"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga862843a45b01c35ffe4d44c47ea774ad"></a>
typedef vec&lt; 1, bool, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga862843a45b01c35ffe4d44c47ea774ad">aligned_highp_bvec1</a></td></tr>
<tr class="memdesc:ga862843a45b01c35ffe4d44c47ea774ad"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector aligned in memory of bool values. <br /></td></tr>
<tr class="separator:ga862843a45b01c35ffe4d44c47ea774ad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0731b593c5e33559954c80f8687e76c6"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga0731b593c5e33559954c80f8687e76c6"></a>
typedef vec&lt; 2, bool, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga0731b593c5e33559954c80f8687e76c6">aligned_highp_bvec2</a></td></tr>
<tr class="memdesc:ga0731b593c5e33559954c80f8687e76c6"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector aligned in memory of bool values. <br /></td></tr>
<tr class="separator:ga0731b593c5e33559954c80f8687e76c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0913bdf048d0cb74af1d2512aec675bc"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga0913bdf048d0cb74af1d2512aec675bc"></a>
typedef vec&lt; 3, bool, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga0913bdf048d0cb74af1d2512aec675bc">aligned_highp_bvec3</a></td></tr>
<tr class="memdesc:ga0913bdf048d0cb74af1d2512aec675bc"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector aligned in memory of bool values. <br /></td></tr>
<tr class="separator:ga0913bdf048d0cb74af1d2512aec675bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9df1d0c425852cf63a57e533b7a83f4f"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga9df1d0c425852cf63a57e533b7a83f4f"></a>
typedef vec&lt; 4, bool, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga9df1d0c425852cf63a57e533b7a83f4f">aligned_highp_bvec4</a></td></tr>
<tr class="memdesc:ga9df1d0c425852cf63a57e533b7a83f4f"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector aligned in memory of bool values. <br /></td></tr>
<tr class="separator:ga9df1d0c425852cf63a57e533b7a83f4f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3a7eeae43cb7673e14cc89bf02f7dd45"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga3a7eeae43cb7673e14cc89bf02f7dd45"></a>
typedef mat&lt; 2, 2, double, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga3a7eeae43cb7673e14cc89bf02f7dd45">aligned_highp_dmat2</a></td></tr>
<tr class="memdesc:ga3a7eeae43cb7673e14cc89bf02f7dd45"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix aligned in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga3a7eeae43cb7673e14cc89bf02f7dd45"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaef26dfe3855a91644665b55c9096a8c8"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaef26dfe3855a91644665b55c9096a8c8"></a>
typedef mat&lt; 2, 2, double, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaef26dfe3855a91644665b55c9096a8c8">aligned_highp_dmat2x2</a></td></tr>
<tr class="memdesc:gaef26dfe3855a91644665b55c9096a8c8"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix aligned in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaef26dfe3855a91644665b55c9096a8c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa7c9d4ab7ab651cdf8001fe7843e238b"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaa7c9d4ab7ab651cdf8001fe7843e238b"></a>
typedef mat&lt; 2, 3, double, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaa7c9d4ab7ab651cdf8001fe7843e238b">aligned_highp_dmat2x3</a></td></tr>
<tr class="memdesc:gaa7c9d4ab7ab651cdf8001fe7843e238b"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 3 matrix aligned in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaa7c9d4ab7ab651cdf8001fe7843e238b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa0d2b8a75f1908dcf32c27f8524bdced"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaa0d2b8a75f1908dcf32c27f8524bdced"></a>
typedef mat&lt; 2, 4, double, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaa0d2b8a75f1908dcf32c27f8524bdced">aligned_highp_dmat2x4</a></td></tr>
<tr class="memdesc:gaa0d2b8a75f1908dcf32c27f8524bdced"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 4 matrix aligned in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaa0d2b8a75f1908dcf32c27f8524bdced"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad8f6abb2c9994850b5d5c04a5f979ed8"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gad8f6abb2c9994850b5d5c04a5f979ed8"></a>
typedef mat&lt; 3, 3, double, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gad8f6abb2c9994850b5d5c04a5f979ed8">aligned_highp_dmat3</a></td></tr>
<tr class="memdesc:gad8f6abb2c9994850b5d5c04a5f979ed8"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix aligned in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gad8f6abb2c9994850b5d5c04a5f979ed8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab069b2fc2ec785fc4e193cf26c022679"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gab069b2fc2ec785fc4e193cf26c022679"></a>
typedef mat&lt; 3, 2, double, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gab069b2fc2ec785fc4e193cf26c022679">aligned_highp_dmat3x2</a></td></tr>
<tr class="memdesc:gab069b2fc2ec785fc4e193cf26c022679"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 2 matrix aligned in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gab069b2fc2ec785fc4e193cf26c022679"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga66073b1ddef34b681741f572338ddb8e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga66073b1ddef34b681741f572338ddb8e"></a>
typedef mat&lt; 3, 3, double, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga66073b1ddef34b681741f572338ddb8e">aligned_highp_dmat3x3</a></td></tr>
<tr class="memdesc:ga66073b1ddef34b681741f572338ddb8e"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix aligned in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga66073b1ddef34b681741f572338ddb8e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga683c8ca66de323ea533a760abedd0efc"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga683c8ca66de323ea533a760abedd0efc"></a>
typedef mat&lt; 3, 4, double, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga683c8ca66de323ea533a760abedd0efc">aligned_highp_dmat3x4</a></td></tr>
<tr class="memdesc:ga683c8ca66de323ea533a760abedd0efc"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 4 matrix aligned in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga683c8ca66de323ea533a760abedd0efc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacaa7407ea00ffdd322ce86a57adb547e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gacaa7407ea00ffdd322ce86a57adb547e"></a>
typedef mat&lt; 4, 4, double, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gacaa7407ea00ffdd322ce86a57adb547e">aligned_highp_dmat4</a></td></tr>
<tr class="memdesc:gacaa7407ea00ffdd322ce86a57adb547e"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix aligned in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gacaa7407ea00ffdd322ce86a57adb547e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga93a23ca3d42818d56e0702213c66354b"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga93a23ca3d42818d56e0702213c66354b"></a>
typedef mat&lt; 4, 2, double, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga93a23ca3d42818d56e0702213c66354b">aligned_highp_dmat4x2</a></td></tr>
<tr class="memdesc:ga93a23ca3d42818d56e0702213c66354b"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 2 matrix aligned in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga93a23ca3d42818d56e0702213c66354b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacab7374b560745cb1d0a306a90353f58"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gacab7374b560745cb1d0a306a90353f58"></a>
typedef mat&lt; 4, 3, double, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gacab7374b560745cb1d0a306a90353f58">aligned_highp_dmat4x3</a></td></tr>
<tr class="memdesc:gacab7374b560745cb1d0a306a90353f58"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 3 matrix aligned in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gacab7374b560745cb1d0a306a90353f58"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1fbfba14368b742972d3b58a0a303682"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga1fbfba14368b742972d3b58a0a303682"></a>
typedef mat&lt; 4, 4, double, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga1fbfba14368b742972d3b58a0a303682">aligned_highp_dmat4x4</a></td></tr>
<tr class="memdesc:ga1fbfba14368b742972d3b58a0a303682"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix aligned in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga1fbfba14368b742972d3b58a0a303682"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf0448b0f7ceb8273f7eda3a92205eefc"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaf0448b0f7ceb8273f7eda3a92205eefc"></a>
typedef vec&lt; 1, double, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaf0448b0f7ceb8273f7eda3a92205eefc">aligned_highp_dvec1</a></td></tr>
<tr class="memdesc:gaf0448b0f7ceb8273f7eda3a92205eefc"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector aligned in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaf0448b0f7ceb8273f7eda3a92205eefc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab173a333e6b7ce153ceba66ac4a321cf"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gab173a333e6b7ce153ceba66ac4a321cf"></a>
typedef vec&lt; 2, double, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gab173a333e6b7ce153ceba66ac4a321cf">aligned_highp_dvec2</a></td></tr>
<tr class="memdesc:gab173a333e6b7ce153ceba66ac4a321cf"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector aligned in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gab173a333e6b7ce153ceba66ac4a321cf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae94ef61edfa047d05bc69b6065fc42ba"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gae94ef61edfa047d05bc69b6065fc42ba"></a>
typedef vec&lt; 3, double, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gae94ef61edfa047d05bc69b6065fc42ba">aligned_highp_dvec3</a></td></tr>
<tr class="memdesc:gae94ef61edfa047d05bc69b6065fc42ba"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector aligned in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gae94ef61edfa047d05bc69b6065fc42ba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8fad35c5677f228e261fe541f15363a4"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga8fad35c5677f228e261fe541f15363a4"></a>
typedef vec&lt; 4, double, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga8fad35c5677f228e261fe541f15363a4">aligned_highp_dvec4</a></td></tr>
<tr class="memdesc:ga8fad35c5677f228e261fe541f15363a4"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector aligned in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga8fad35c5677f228e261fe541f15363a4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad63b8c5b4dc0500d54d7414ef555178f"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gad63b8c5b4dc0500d54d7414ef555178f"></a>
typedef vec&lt; 1, int, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gad63b8c5b4dc0500d54d7414ef555178f">aligned_highp_ivec1</a></td></tr>
<tr class="memdesc:gad63b8c5b4dc0500d54d7414ef555178f"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector aligned in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:gad63b8c5b4dc0500d54d7414ef555178f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga41563650f36cb7f479e080de21e08418"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga41563650f36cb7f479e080de21e08418"></a>
typedef vec&lt; 2, int, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga41563650f36cb7f479e080de21e08418">aligned_highp_ivec2</a></td></tr>
<tr class="memdesc:ga41563650f36cb7f479e080de21e08418"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector aligned in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:ga41563650f36cb7f479e080de21e08418"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6eca5170bb35eac90b4972590fd31a06"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga6eca5170bb35eac90b4972590fd31a06"></a>
typedef vec&lt; 3, int, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga6eca5170bb35eac90b4972590fd31a06">aligned_highp_ivec3</a></td></tr>
<tr class="memdesc:ga6eca5170bb35eac90b4972590fd31a06"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector aligned in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:ga6eca5170bb35eac90b4972590fd31a06"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga31bfa801e1579fdba752ec3f7a45ec91"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga31bfa801e1579fdba752ec3f7a45ec91"></a>
typedef vec&lt; 4, int, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga31bfa801e1579fdba752ec3f7a45ec91">aligned_highp_ivec4</a></td></tr>
<tr class="memdesc:ga31bfa801e1579fdba752ec3f7a45ec91"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector aligned in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:ga31bfa801e1579fdba752ec3f7a45ec91"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf9db5e8a929c317da5aa12cc53741b63"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaf9db5e8a929c317da5aa12cc53741b63"></a>
typedef mat&lt; 2, 2, float, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaf9db5e8a929c317da5aa12cc53741b63">aligned_highp_mat2</a></td></tr>
<tr class="memdesc:gaf9db5e8a929c317da5aa12cc53741b63"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix aligned in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaf9db5e8a929c317da5aa12cc53741b63"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab559d943abf92bc588bcd3f4c0e4664b"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gab559d943abf92bc588bcd3f4c0e4664b"></a>
typedef mat&lt; 2, 2, float, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gab559d943abf92bc588bcd3f4c0e4664b">aligned_highp_mat2x2</a></td></tr>
<tr class="memdesc:gab559d943abf92bc588bcd3f4c0e4664b"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix aligned in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gab559d943abf92bc588bcd3f4c0e4664b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga50c9af5aa3a848956d625fc64dc8488e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga50c9af5aa3a848956d625fc64dc8488e"></a>
typedef mat&lt; 2, 3, float, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga50c9af5aa3a848956d625fc64dc8488e">aligned_highp_mat2x3</a></td></tr>
<tr class="memdesc:ga50c9af5aa3a848956d625fc64dc8488e"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 3 matrix aligned in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga50c9af5aa3a848956d625fc64dc8488e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0edcfdd179f8a158342eead48a4d0c2a"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga0edcfdd179f8a158342eead48a4d0c2a"></a>
typedef mat&lt; 2, 4, float, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga0edcfdd179f8a158342eead48a4d0c2a">aligned_highp_mat2x4</a></td></tr>
<tr class="memdesc:ga0edcfdd179f8a158342eead48a4d0c2a"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 4 matrix aligned in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga0edcfdd179f8a158342eead48a4d0c2a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabab3afcc04459c7b123604ae5dc663f6"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gabab3afcc04459c7b123604ae5dc663f6"></a>
typedef mat&lt; 3, 3, float, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gabab3afcc04459c7b123604ae5dc663f6">aligned_highp_mat3</a></td></tr>
<tr class="memdesc:gabab3afcc04459c7b123604ae5dc663f6"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix aligned in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gabab3afcc04459c7b123604ae5dc663f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9fc2167b47c9be9295f2d8eea7f0ca75"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga9fc2167b47c9be9295f2d8eea7f0ca75"></a>
typedef mat&lt; 3, 2, float, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga9fc2167b47c9be9295f2d8eea7f0ca75">aligned_highp_mat3x2</a></td></tr>
<tr class="memdesc:ga9fc2167b47c9be9295f2d8eea7f0ca75"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 2 matrix aligned in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga9fc2167b47c9be9295f2d8eea7f0ca75"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2f7b8c99ba6f2d07c73a195a8143c259"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga2f7b8c99ba6f2d07c73a195a8143c259"></a>
typedef mat&lt; 3, 3, float, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga2f7b8c99ba6f2d07c73a195a8143c259">aligned_highp_mat3x3</a></td></tr>
<tr class="memdesc:ga2f7b8c99ba6f2d07c73a195a8143c259"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix aligned in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga2f7b8c99ba6f2d07c73a195a8143c259"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga52e00afd0eb181e6738f40cf41787049"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga52e00afd0eb181e6738f40cf41787049"></a>
typedef mat&lt; 3, 4, float, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga52e00afd0eb181e6738f40cf41787049">aligned_highp_mat3x4</a></td></tr>
<tr class="memdesc:ga52e00afd0eb181e6738f40cf41787049"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 4 matrix aligned in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga52e00afd0eb181e6738f40cf41787049"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga058ae939bfdbcbb80521dd4a3b01afba"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga058ae939bfdbcbb80521dd4a3b01afba"></a>
typedef mat&lt; 4, 4, float, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga058ae939bfdbcbb80521dd4a3b01afba">aligned_highp_mat4</a></td></tr>
<tr class="memdesc:ga058ae939bfdbcbb80521dd4a3b01afba"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix aligned in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga058ae939bfdbcbb80521dd4a3b01afba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga84e1f5e0718952a079b748825c03f956"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga84e1f5e0718952a079b748825c03f956"></a>
typedef mat&lt; 4, 2, float, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga84e1f5e0718952a079b748825c03f956">aligned_highp_mat4x2</a></td></tr>
<tr class="memdesc:ga84e1f5e0718952a079b748825c03f956"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 2 matrix aligned in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga84e1f5e0718952a079b748825c03f956"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafff1684c4ff19b4a818138ccacc1e78d"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gafff1684c4ff19b4a818138ccacc1e78d"></a>
typedef mat&lt; 4, 3, float, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gafff1684c4ff19b4a818138ccacc1e78d">aligned_highp_mat4x3</a></td></tr>
<tr class="memdesc:gafff1684c4ff19b4a818138ccacc1e78d"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 3 matrix aligned in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gafff1684c4ff19b4a818138ccacc1e78d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga40d49648083a0498a12a4bb41ae6ece8"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga40d49648083a0498a12a4bb41ae6ece8"></a>
typedef mat&lt; 4, 4, float, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga40d49648083a0498a12a4bb41ae6ece8">aligned_highp_mat4x4</a></td></tr>
<tr class="memdesc:ga40d49648083a0498a12a4bb41ae6ece8"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix aligned in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga40d49648083a0498a12a4bb41ae6ece8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5b80e28396c6ef7d32c6fd18df498451"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga5b80e28396c6ef7d32c6fd18df498451"></a>
typedef vec&lt; 1, uint, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga5b80e28396c6ef7d32c6fd18df498451">aligned_highp_uvec1</a></td></tr>
<tr class="memdesc:ga5b80e28396c6ef7d32c6fd18df498451"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector aligned in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:ga5b80e28396c6ef7d32c6fd18df498451"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga04db692662a4908beeaf5a5ba6e19483"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga04db692662a4908beeaf5a5ba6e19483"></a>
typedef vec&lt; 2, uint, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga04db692662a4908beeaf5a5ba6e19483">aligned_highp_uvec2</a></td></tr>
<tr class="memdesc:ga04db692662a4908beeaf5a5ba6e19483"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector aligned in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:ga04db692662a4908beeaf5a5ba6e19483"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga073fd6e8b241afade6d8afbd676b2667"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga073fd6e8b241afade6d8afbd676b2667"></a>
typedef vec&lt; 3, uint, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga073fd6e8b241afade6d8afbd676b2667">aligned_highp_uvec3</a></td></tr>
<tr class="memdesc:ga073fd6e8b241afade6d8afbd676b2667"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector aligned in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:ga073fd6e8b241afade6d8afbd676b2667"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabdd60462042859f876c17c7346c732a5"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gabdd60462042859f876c17c7346c732a5"></a>
typedef vec&lt; 4, uint, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gabdd60462042859f876c17c7346c732a5">aligned_highp_uvec4</a></td></tr>
<tr class="memdesc:gabdd60462042859f876c17c7346c732a5"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector aligned in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:gabdd60462042859f876c17c7346c732a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4d0bd70d5fac49b800546d608b707513"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga4d0bd70d5fac49b800546d608b707513"></a>
typedef vec&lt; 1, float, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga4d0bd70d5fac49b800546d608b707513">aligned_highp_vec1</a></td></tr>
<tr class="memdesc:ga4d0bd70d5fac49b800546d608b707513"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector aligned in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga4d0bd70d5fac49b800546d608b707513"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac9f8482dde741fb6bab7248b81a45465"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gac9f8482dde741fb6bab7248b81a45465"></a>
typedef vec&lt; 2, float, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gac9f8482dde741fb6bab7248b81a45465">aligned_highp_vec2</a></td></tr>
<tr class="memdesc:gac9f8482dde741fb6bab7248b81a45465"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector aligned in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gac9f8482dde741fb6bab7248b81a45465"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga65415d2d68c9cc0ca554524a8f5510b2"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga65415d2d68c9cc0ca554524a8f5510b2"></a>
typedef vec&lt; 3, float, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga65415d2d68c9cc0ca554524a8f5510b2">aligned_highp_vec3</a></td></tr>
<tr class="memdesc:ga65415d2d68c9cc0ca554524a8f5510b2"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector aligned in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga65415d2d68c9cc0ca554524a8f5510b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7cb26d354dd69d23849c34c4fba88da9"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga7cb26d354dd69d23849c34c4fba88da9"></a>
typedef vec&lt; 4, float, aligned_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga7cb26d354dd69d23849c34c4fba88da9">aligned_highp_vec4</a></td></tr>
<tr class="memdesc:ga7cb26d354dd69d23849c34c4fba88da9"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector aligned in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga7cb26d354dd69d23849c34c4fba88da9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga76298aed82a439063c3d55980c84aa0b"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga76298aed82a439063c3d55980c84aa0b"></a>
typedef aligned_highp_ivec1&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga76298aed82a439063c3d55980c84aa0b">aligned_ivec1</a></td></tr>
<tr class="memdesc:ga76298aed82a439063c3d55980c84aa0b"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector aligned in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:ga76298aed82a439063c3d55980c84aa0b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae4f38fd2c86cee6940986197777b3ca4"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gae4f38fd2c86cee6940986197777b3ca4"></a>
typedef aligned_highp_ivec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gae4f38fd2c86cee6940986197777b3ca4">aligned_ivec2</a></td></tr>
<tr class="memdesc:gae4f38fd2c86cee6940986197777b3ca4"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector aligned in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:gae4f38fd2c86cee6940986197777b3ca4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga32794322d294e5ace7fed4a61896f270"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga32794322d294e5ace7fed4a61896f270"></a>
typedef aligned_highp_ivec3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga32794322d294e5ace7fed4a61896f270">aligned_ivec3</a></td></tr>
<tr class="memdesc:ga32794322d294e5ace7fed4a61896f270"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector aligned in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:ga32794322d294e5ace7fed4a61896f270"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7f79eae5927c9033d84617e49f6f34e4"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga7f79eae5927c9033d84617e49f6f34e4"></a>
typedef aligned_highp_ivec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga7f79eae5927c9033d84617e49f6f34e4">aligned_ivec4</a></td></tr>
<tr class="memdesc:ga7f79eae5927c9033d84617e49f6f34e4"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector aligned in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:ga7f79eae5927c9033d84617e49f6f34e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac6036449ab1c4abf8efe1ea00fcdd1c9"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gac6036449ab1c4abf8efe1ea00fcdd1c9"></a>
typedef vec&lt; 1, bool, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gac6036449ab1c4abf8efe1ea00fcdd1c9">aligned_lowp_bvec1</a></td></tr>
<tr class="memdesc:gac6036449ab1c4abf8efe1ea00fcdd1c9"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector aligned in memory of bool values. <br /></td></tr>
<tr class="separator:gac6036449ab1c4abf8efe1ea00fcdd1c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga59fadcd3835646e419372ae8b43c5d37"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga59fadcd3835646e419372ae8b43c5d37"></a>
typedef vec&lt; 2, bool, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga59fadcd3835646e419372ae8b43c5d37">aligned_lowp_bvec2</a></td></tr>
<tr class="memdesc:ga59fadcd3835646e419372ae8b43c5d37"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector aligned in memory of bool values. <br /></td></tr>
<tr class="separator:ga59fadcd3835646e419372ae8b43c5d37"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga83aab4d191053f169c93a3e364f2e118"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga83aab4d191053f169c93a3e364f2e118"></a>
typedef vec&lt; 3, bool, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga83aab4d191053f169c93a3e364f2e118">aligned_lowp_bvec3</a></td></tr>
<tr class="memdesc:ga83aab4d191053f169c93a3e364f2e118"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector aligned in memory of bool values. <br /></td></tr>
<tr class="separator:ga83aab4d191053f169c93a3e364f2e118"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa7a76555ee4853614e5755181a8dd54e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaa7a76555ee4853614e5755181a8dd54e"></a>
typedef vec&lt; 4, bool, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaa7a76555ee4853614e5755181a8dd54e">aligned_lowp_bvec4</a></td></tr>
<tr class="memdesc:gaa7a76555ee4853614e5755181a8dd54e"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector aligned in memory of bool values. <br /></td></tr>
<tr class="separator:gaa7a76555ee4853614e5755181a8dd54e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga79a90173d8faa9816dc852ce447d66ca"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga79a90173d8faa9816dc852ce447d66ca"></a>
typedef mat&lt; 2, 2, double, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga79a90173d8faa9816dc852ce447d66ca">aligned_lowp_dmat2</a></td></tr>
<tr class="memdesc:ga79a90173d8faa9816dc852ce447d66ca"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix aligned in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga79a90173d8faa9816dc852ce447d66ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga07cb8e846666cbf56045b064fb553d2e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga07cb8e846666cbf56045b064fb553d2e"></a>
typedef mat&lt; 2, 2, double, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga07cb8e846666cbf56045b064fb553d2e">aligned_lowp_dmat2x2</a></td></tr>
<tr class="memdesc:ga07cb8e846666cbf56045b064fb553d2e"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix aligned in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga07cb8e846666cbf56045b064fb553d2e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7a4536b6e1f2ebb690f63816b5d7e48b"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga7a4536b6e1f2ebb690f63816b5d7e48b"></a>
typedef mat&lt; 2, 3, double, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga7a4536b6e1f2ebb690f63816b5d7e48b">aligned_lowp_dmat2x3</a></td></tr>
<tr class="memdesc:ga7a4536b6e1f2ebb690f63816b5d7e48b"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 3 matrix aligned in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga7a4536b6e1f2ebb690f63816b5d7e48b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab0cf4f7c9a264941519acad286e055ea"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gab0cf4f7c9a264941519acad286e055ea"></a>
typedef mat&lt; 2, 4, double, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gab0cf4f7c9a264941519acad286e055ea">aligned_lowp_dmat2x4</a></td></tr>
<tr class="memdesc:gab0cf4f7c9a264941519acad286e055ea"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 4 matrix aligned in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gab0cf4f7c9a264941519acad286e055ea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac00e15efded8a57c9dec3aed0fb547e7"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gac00e15efded8a57c9dec3aed0fb547e7"></a>
typedef mat&lt; 3, 3, double, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gac00e15efded8a57c9dec3aed0fb547e7">aligned_lowp_dmat3</a></td></tr>
<tr class="memdesc:gac00e15efded8a57c9dec3aed0fb547e7"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix aligned in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gac00e15efded8a57c9dec3aed0fb547e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa281a47d5d627313984d0f8df993b648"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaa281a47d5d627313984d0f8df993b648"></a>
typedef mat&lt; 3, 2, double, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaa281a47d5d627313984d0f8df993b648">aligned_lowp_dmat3x2</a></td></tr>
<tr class="memdesc:gaa281a47d5d627313984d0f8df993b648"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 2 matrix aligned in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaa281a47d5d627313984d0f8df993b648"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7f3148a72355e39932d6855baca42ebc"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga7f3148a72355e39932d6855baca42ebc"></a>
typedef mat&lt; 3, 3, double, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga7f3148a72355e39932d6855baca42ebc">aligned_lowp_dmat3x3</a></td></tr>
<tr class="memdesc:ga7f3148a72355e39932d6855baca42ebc"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix aligned in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga7f3148a72355e39932d6855baca42ebc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaea3ccc5ef5b178e6e49b4fa1427605d3"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaea3ccc5ef5b178e6e49b4fa1427605d3"></a>
typedef mat&lt; 3, 4, double, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaea3ccc5ef5b178e6e49b4fa1427605d3">aligned_lowp_dmat3x4</a></td></tr>
<tr class="memdesc:gaea3ccc5ef5b178e6e49b4fa1427605d3"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 4 matrix aligned in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaea3ccc5ef5b178e6e49b4fa1427605d3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab92c6d7d58d43dfb8147e9aedfe8351b"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gab92c6d7d58d43dfb8147e9aedfe8351b"></a>
typedef mat&lt; 4, 4, double, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gab92c6d7d58d43dfb8147e9aedfe8351b">aligned_lowp_dmat4</a></td></tr>
<tr class="memdesc:gab92c6d7d58d43dfb8147e9aedfe8351b"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix aligned in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gab92c6d7d58d43dfb8147e9aedfe8351b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf806dfdaffb2e9f7681b1cd2825898ce"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaf806dfdaffb2e9f7681b1cd2825898ce"></a>
typedef mat&lt; 4, 2, double, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaf806dfdaffb2e9f7681b1cd2825898ce">aligned_lowp_dmat4x2</a></td></tr>
<tr class="memdesc:gaf806dfdaffb2e9f7681b1cd2825898ce"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 2 matrix aligned in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaf806dfdaffb2e9f7681b1cd2825898ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab0931ac7807fa1428c7bbf249efcdf0d"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gab0931ac7807fa1428c7bbf249efcdf0d"></a>
typedef mat&lt; 4, 3, double, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gab0931ac7807fa1428c7bbf249efcdf0d">aligned_lowp_dmat4x3</a></td></tr>
<tr class="memdesc:gab0931ac7807fa1428c7bbf249efcdf0d"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 3 matrix aligned in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gab0931ac7807fa1428c7bbf249efcdf0d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad8220a93d2fca2dd707821b4ab6f809e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gad8220a93d2fca2dd707821b4ab6f809e"></a>
typedef mat&lt; 4, 4, double, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gad8220a93d2fca2dd707821b4ab6f809e">aligned_lowp_dmat4x4</a></td></tr>
<tr class="memdesc:gad8220a93d2fca2dd707821b4ab6f809e"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix aligned in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gad8220a93d2fca2dd707821b4ab6f809e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7f8a2cc5a686e52b1615761f4978ca62"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga7f8a2cc5a686e52b1615761f4978ca62"></a>
typedef vec&lt; 1, double, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga7f8a2cc5a686e52b1615761f4978ca62">aligned_lowp_dvec1</a></td></tr>
<tr class="memdesc:ga7f8a2cc5a686e52b1615761f4978ca62"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector aligned in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga7f8a2cc5a686e52b1615761f4978ca62"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0e37cff4a43cca866101f0a35f01db6d"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga0e37cff4a43cca866101f0a35f01db6d"></a>
typedef vec&lt; 2, double, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga0e37cff4a43cca866101f0a35f01db6d">aligned_lowp_dvec2</a></td></tr>
<tr class="memdesc:ga0e37cff4a43cca866101f0a35f01db6d"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector aligned in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga0e37cff4a43cca866101f0a35f01db6d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab9e669c4efd52d3347fc6d5f6b20fd59"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gab9e669c4efd52d3347fc6d5f6b20fd59"></a>
typedef vec&lt; 3, double, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gab9e669c4efd52d3347fc6d5f6b20fd59">aligned_lowp_dvec3</a></td></tr>
<tr class="memdesc:gab9e669c4efd52d3347fc6d5f6b20fd59"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector aligned in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gab9e669c4efd52d3347fc6d5f6b20fd59"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga226f5ec7a953cea559c16fe3aff9924f"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga226f5ec7a953cea559c16fe3aff9924f"></a>
typedef vec&lt; 4, double, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga226f5ec7a953cea559c16fe3aff9924f">aligned_lowp_dvec4</a></td></tr>
<tr class="memdesc:ga226f5ec7a953cea559c16fe3aff9924f"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector aligned in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga226f5ec7a953cea559c16fe3aff9924f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1101d3a82b2e3f5f8828bd8f3adab3e1"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga1101d3a82b2e3f5f8828bd8f3adab3e1"></a>
typedef vec&lt; 1, int, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga1101d3a82b2e3f5f8828bd8f3adab3e1">aligned_lowp_ivec1</a></td></tr>
<tr class="memdesc:ga1101d3a82b2e3f5f8828bd8f3adab3e1"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector aligned in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:ga1101d3a82b2e3f5f8828bd8f3adab3e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga44c4accad582cfbd7226a19b83b0cadc"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga44c4accad582cfbd7226a19b83b0cadc"></a>
typedef vec&lt; 2, int, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga44c4accad582cfbd7226a19b83b0cadc">aligned_lowp_ivec2</a></td></tr>
<tr class="memdesc:ga44c4accad582cfbd7226a19b83b0cadc"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector aligned in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:ga44c4accad582cfbd7226a19b83b0cadc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga65663f10a02e52cedcddbcfe36ddf38d"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga65663f10a02e52cedcddbcfe36ddf38d"></a>
typedef vec&lt; 3, int, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga65663f10a02e52cedcddbcfe36ddf38d">aligned_lowp_ivec3</a></td></tr>
<tr class="memdesc:ga65663f10a02e52cedcddbcfe36ddf38d"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector aligned in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:ga65663f10a02e52cedcddbcfe36ddf38d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaae92fcec8b2e0328ffbeac31cc4fc419"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaae92fcec8b2e0328ffbeac31cc4fc419"></a>
typedef vec&lt; 4, int, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaae92fcec8b2e0328ffbeac31cc4fc419">aligned_lowp_ivec4</a></td></tr>
<tr class="memdesc:gaae92fcec8b2e0328ffbeac31cc4fc419"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector aligned in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:gaae92fcec8b2e0328ffbeac31cc4fc419"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga17c424412207b00dba1cf587b099eea3"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga17c424412207b00dba1cf587b099eea3"></a>
typedef mat&lt; 2, 2, float, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga17c424412207b00dba1cf587b099eea3">aligned_lowp_mat2</a></td></tr>
<tr class="memdesc:ga17c424412207b00dba1cf587b099eea3"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix aligned in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga17c424412207b00dba1cf587b099eea3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0e44aeb930a47f9cbf2db15b56433b0f"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga0e44aeb930a47f9cbf2db15b56433b0f"></a>
typedef mat&lt; 2, 2, float, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga0e44aeb930a47f9cbf2db15b56433b0f">aligned_lowp_mat2x2</a></td></tr>
<tr class="memdesc:ga0e44aeb930a47f9cbf2db15b56433b0f"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix aligned in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga0e44aeb930a47f9cbf2db15b56433b0f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7dec6d96bc61312b1e56d137c9c74030"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga7dec6d96bc61312b1e56d137c9c74030"></a>
typedef mat&lt; 2, 3, float, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga7dec6d96bc61312b1e56d137c9c74030">aligned_lowp_mat2x3</a></td></tr>
<tr class="memdesc:ga7dec6d96bc61312b1e56d137c9c74030"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 3 matrix aligned in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga7dec6d96bc61312b1e56d137c9c74030"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa694fab1f8df5f658846573ba8ffc563"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaa694fab1f8df5f658846573ba8ffc563"></a>
typedef mat&lt; 2, 4, float, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaa694fab1f8df5f658846573ba8ffc563">aligned_lowp_mat2x4</a></td></tr>
<tr class="memdesc:gaa694fab1f8df5f658846573ba8ffc563"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 4 matrix aligned in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaa694fab1f8df5f658846573ba8ffc563"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1eb9076cc28ead5020fd3029fd0472c5"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga1eb9076cc28ead5020fd3029fd0472c5"></a>
typedef mat&lt; 3, 3, float, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga1eb9076cc28ead5020fd3029fd0472c5">aligned_lowp_mat3</a></td></tr>
<tr class="memdesc:ga1eb9076cc28ead5020fd3029fd0472c5"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix aligned in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga1eb9076cc28ead5020fd3029fd0472c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2d6639f0bd777bae1ee0eba71cd7bfdc"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga2d6639f0bd777bae1ee0eba71cd7bfdc"></a>
typedef mat&lt; 3, 2, float, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga2d6639f0bd777bae1ee0eba71cd7bfdc">aligned_lowp_mat3x2</a></td></tr>
<tr class="memdesc:ga2d6639f0bd777bae1ee0eba71cd7bfdc"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 2 matrix aligned in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga2d6639f0bd777bae1ee0eba71cd7bfdc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaeaab04e378a90956eec8d68a99d777ed"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaeaab04e378a90956eec8d68a99d777ed"></a>
typedef mat&lt; 3, 3, float, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaeaab04e378a90956eec8d68a99d777ed">aligned_lowp_mat3x3</a></td></tr>
<tr class="memdesc:gaeaab04e378a90956eec8d68a99d777ed"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix aligned in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaeaab04e378a90956eec8d68a99d777ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1f03696ab066572c6c044e63edf635a2"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga1f03696ab066572c6c044e63edf635a2"></a>
typedef mat&lt; 3, 4, float, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga1f03696ab066572c6c044e63edf635a2">aligned_lowp_mat3x4</a></td></tr>
<tr class="memdesc:ga1f03696ab066572c6c044e63edf635a2"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 4 matrix aligned in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga1f03696ab066572c6c044e63edf635a2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga25ea2f684e36aa5e978b4f2f86593824"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga25ea2f684e36aa5e978b4f2f86593824"></a>
typedef mat&lt; 4, 4, float, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga25ea2f684e36aa5e978b4f2f86593824">aligned_lowp_mat4</a></td></tr>
<tr class="memdesc:ga25ea2f684e36aa5e978b4f2f86593824"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix aligned in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga25ea2f684e36aa5e978b4f2f86593824"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2cb16c3fdfb15e0719d942ee3b548bc4"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga2cb16c3fdfb15e0719d942ee3b548bc4"></a>
typedef mat&lt; 4, 2, float, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga2cb16c3fdfb15e0719d942ee3b548bc4">aligned_lowp_mat4x2</a></td></tr>
<tr class="memdesc:ga2cb16c3fdfb15e0719d942ee3b548bc4"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 2 matrix aligned in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga2cb16c3fdfb15e0719d942ee3b548bc4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7e96981e872f17a780d9f1c22dc1f512"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga7e96981e872f17a780d9f1c22dc1f512"></a>
typedef mat&lt; 4, 3, float, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga7e96981e872f17a780d9f1c22dc1f512">aligned_lowp_mat4x3</a></td></tr>
<tr class="memdesc:ga7e96981e872f17a780d9f1c22dc1f512"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 3 matrix aligned in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga7e96981e872f17a780d9f1c22dc1f512"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadae3dcfc22d28c64d0548cbfd9d08719"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gadae3dcfc22d28c64d0548cbfd9d08719"></a>
typedef mat&lt; 4, 4, float, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gadae3dcfc22d28c64d0548cbfd9d08719">aligned_lowp_mat4x4</a></td></tr>
<tr class="memdesc:gadae3dcfc22d28c64d0548cbfd9d08719"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix aligned in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gadae3dcfc22d28c64d0548cbfd9d08719"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad09b93acc43c43423408d17a64f6d7ca"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gad09b93acc43c43423408d17a64f6d7ca"></a>
typedef vec&lt; 1, uint, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gad09b93acc43c43423408d17a64f6d7ca">aligned_lowp_uvec1</a></td></tr>
<tr class="memdesc:gad09b93acc43c43423408d17a64f6d7ca"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector aligned in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:gad09b93acc43c43423408d17a64f6d7ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6f94fcd28dde906fc6cad5f742b55c1a"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga6f94fcd28dde906fc6cad5f742b55c1a"></a>
typedef vec&lt; 2, uint, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga6f94fcd28dde906fc6cad5f742b55c1a">aligned_lowp_uvec2</a></td></tr>
<tr class="memdesc:ga6f94fcd28dde906fc6cad5f742b55c1a"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector aligned in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:ga6f94fcd28dde906fc6cad5f742b55c1a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9e9f006970b1a00862e3e6e599eedd4c"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga9e9f006970b1a00862e3e6e599eedd4c"></a>
typedef vec&lt; 3, uint, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga9e9f006970b1a00862e3e6e599eedd4c">aligned_lowp_uvec3</a></td></tr>
<tr class="memdesc:ga9e9f006970b1a00862e3e6e599eedd4c"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector aligned in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:ga9e9f006970b1a00862e3e6e599eedd4c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga46b1b0b9eb8625a5d69137bd66cd13dc"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga46b1b0b9eb8625a5d69137bd66cd13dc"></a>
typedef vec&lt; 4, uint, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga46b1b0b9eb8625a5d69137bd66cd13dc">aligned_lowp_uvec4</a></td></tr>
<tr class="memdesc:ga46b1b0b9eb8625a5d69137bd66cd13dc"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector aligned in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:ga46b1b0b9eb8625a5d69137bd66cd13dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab34aee3d5e121c543fea11d2c50ecc43"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gab34aee3d5e121c543fea11d2c50ecc43"></a>
typedef vec&lt; 1, float, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gab34aee3d5e121c543fea11d2c50ecc43">aligned_lowp_vec1</a></td></tr>
<tr class="memdesc:gab34aee3d5e121c543fea11d2c50ecc43"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector aligned in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gab34aee3d5e121c543fea11d2c50ecc43"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga53ac5d252317f1fa43c2ef921857bf13"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga53ac5d252317f1fa43c2ef921857bf13"></a>
typedef vec&lt; 2, float, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga53ac5d252317f1fa43c2ef921857bf13">aligned_lowp_vec2</a></td></tr>
<tr class="memdesc:ga53ac5d252317f1fa43c2ef921857bf13"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector aligned in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga53ac5d252317f1fa43c2ef921857bf13"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga98f0b5cd65fce164ff1367c2a3b3aa1e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga98f0b5cd65fce164ff1367c2a3b3aa1e"></a>
typedef vec&lt; 3, float, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga98f0b5cd65fce164ff1367c2a3b3aa1e">aligned_lowp_vec3</a></td></tr>
<tr class="memdesc:ga98f0b5cd65fce164ff1367c2a3b3aa1e"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector aligned in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga98f0b5cd65fce164ff1367c2a3b3aa1e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga82f7275d6102593a69ce38cdad680409"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga82f7275d6102593a69ce38cdad680409"></a>
typedef vec&lt; 4, float, aligned_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga82f7275d6102593a69ce38cdad680409">aligned_lowp_vec4</a></td></tr>
<tr class="memdesc:ga82f7275d6102593a69ce38cdad680409"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector aligned in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga82f7275d6102593a69ce38cdad680409"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5a8a5f8c47cd7d5502dd9932f83472b9"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga5a8a5f8c47cd7d5502dd9932f83472b9"></a>
typedef aligned_highp_mat2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga5a8a5f8c47cd7d5502dd9932f83472b9">aligned_mat2</a></td></tr>
<tr class="memdesc:ga5a8a5f8c47cd7d5502dd9932f83472b9"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix tightly aligned in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga5a8a5f8c47cd7d5502dd9932f83472b9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabb04f459d81d753d278b2072e2375e8e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gabb04f459d81d753d278b2072e2375e8e"></a>
typedef aligned_highp_mat2x2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gabb04f459d81d753d278b2072e2375e8e">aligned_mat2x2</a></td></tr>
<tr class="memdesc:gabb04f459d81d753d278b2072e2375e8e"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix tightly aligned in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gabb04f459d81d753d278b2072e2375e8e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga832476bb1c59ef673db37433ff34e399"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga832476bb1c59ef673db37433ff34e399"></a>
typedef aligned_highp_mat2x3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga832476bb1c59ef673db37433ff34e399">aligned_mat2x3</a></td></tr>
<tr class="memdesc:ga832476bb1c59ef673db37433ff34e399"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 3 matrix tightly aligned in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga832476bb1c59ef673db37433ff34e399"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadab11a7504430825b648ff7c7e36b725"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gadab11a7504430825b648ff7c7e36b725"></a>
typedef aligned_highp_mat2x4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gadab11a7504430825b648ff7c7e36b725">aligned_mat2x4</a></td></tr>
<tr class="memdesc:gadab11a7504430825b648ff7c7e36b725"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 4 matrix tightly aligned in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gadab11a7504430825b648ff7c7e36b725"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga43a92a24ca863e0e0f3b65834b3cf714"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga43a92a24ca863e0e0f3b65834b3cf714"></a>
typedef aligned_highp_mat3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga43a92a24ca863e0e0f3b65834b3cf714">aligned_mat3</a></td></tr>
<tr class="memdesc:ga43a92a24ca863e0e0f3b65834b3cf714"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix tightly aligned in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga43a92a24ca863e0e0f3b65834b3cf714"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5c0df24ba85eafafc0eb0c90690510ed"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga5c0df24ba85eafafc0eb0c90690510ed"></a>
typedef aligned_highp_mat3x2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga5c0df24ba85eafafc0eb0c90690510ed">aligned_mat3x2</a></td></tr>
<tr class="memdesc:ga5c0df24ba85eafafc0eb0c90690510ed"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 2 matrix tightly aligned in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga5c0df24ba85eafafc0eb0c90690510ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadb065dbe5c11271fef8cf2ea8608f187"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gadb065dbe5c11271fef8cf2ea8608f187"></a>
typedef aligned_highp_mat3x3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gadb065dbe5c11271fef8cf2ea8608f187">aligned_mat3x3</a></td></tr>
<tr class="memdesc:gadb065dbe5c11271fef8cf2ea8608f187"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix tightly aligned in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gadb065dbe5c11271fef8cf2ea8608f187"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga88061c72c997b94c420f2b0a60d9df26"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga88061c72c997b94c420f2b0a60d9df26"></a>
typedef aligned_highp_mat3x4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga88061c72c997b94c420f2b0a60d9df26">aligned_mat3x4</a></td></tr>
<tr class="memdesc:ga88061c72c997b94c420f2b0a60d9df26"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 4 matrix tightly aligned in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga88061c72c997b94c420f2b0a60d9df26"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab0fddcf95dd51cbcbf624ea7c40dfeb8"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gab0fddcf95dd51cbcbf624ea7c40dfeb8"></a>
typedef aligned_highp_mat4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gab0fddcf95dd51cbcbf624ea7c40dfeb8">aligned_mat4</a></td></tr>
<tr class="memdesc:gab0fddcf95dd51cbcbf624ea7c40dfeb8"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix tightly aligned in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gab0fddcf95dd51cbcbf624ea7c40dfeb8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac9a2d0fb815fd5c2bd58b869c55e32d3"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gac9a2d0fb815fd5c2bd58b869c55e32d3"></a>
typedef aligned_highp_mat4x2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gac9a2d0fb815fd5c2bd58b869c55e32d3">aligned_mat4x2</a></td></tr>
<tr class="memdesc:gac9a2d0fb815fd5c2bd58b869c55e32d3"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 2 matrix tightly aligned in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gac9a2d0fb815fd5c2bd58b869c55e32d3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga452bbbfd26e244de216e4d004d50bb74"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga452bbbfd26e244de216e4d004d50bb74"></a>
typedef aligned_highp_mat4x3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga452bbbfd26e244de216e4d004d50bb74">aligned_mat4x3</a></td></tr>
<tr class="memdesc:ga452bbbfd26e244de216e4d004d50bb74"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 3 matrix tightly aligned in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga452bbbfd26e244de216e4d004d50bb74"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8b8fb86973a0b768c5bd802c92fac1a1"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga8b8fb86973a0b768c5bd802c92fac1a1"></a>
typedef aligned_highp_mat4x4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga8b8fb86973a0b768c5bd802c92fac1a1">aligned_mat4x4</a></td></tr>
<tr class="memdesc:ga8b8fb86973a0b768c5bd802c92fac1a1"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix tightly aligned in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga8b8fb86973a0b768c5bd802c92fac1a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadd3b8bd71a758f7fb0da8e525156f34e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gadd3b8bd71a758f7fb0da8e525156f34e"></a>
typedef vec&lt; 1, bool, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gadd3b8bd71a758f7fb0da8e525156f34e">aligned_mediump_bvec1</a></td></tr>
<tr class="memdesc:gadd3b8bd71a758f7fb0da8e525156f34e"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector aligned in memory of bool values. <br /></td></tr>
<tr class="separator:gadd3b8bd71a758f7fb0da8e525156f34e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacb183eb5e67ec0d0ea5a016cba962810"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gacb183eb5e67ec0d0ea5a016cba962810"></a>
typedef vec&lt; 2, bool, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gacb183eb5e67ec0d0ea5a016cba962810">aligned_mediump_bvec2</a></td></tr>
<tr class="memdesc:gacb183eb5e67ec0d0ea5a016cba962810"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector aligned in memory of bool values. <br /></td></tr>
<tr class="separator:gacb183eb5e67ec0d0ea5a016cba962810"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacfa4a542f1b20a5b63ad702dfb6fd587"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gacfa4a542f1b20a5b63ad702dfb6fd587"></a>
typedef vec&lt; 3, bool, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gacfa4a542f1b20a5b63ad702dfb6fd587">aligned_mediump_bvec3</a></td></tr>
<tr class="memdesc:gacfa4a542f1b20a5b63ad702dfb6fd587"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector aligned in memory of bool values. <br /></td></tr>
<tr class="separator:gacfa4a542f1b20a5b63ad702dfb6fd587"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga91bc1f513bb9b0fd60281d57ded9a48c"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga91bc1f513bb9b0fd60281d57ded9a48c"></a>
typedef vec&lt; 4, bool, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga91bc1f513bb9b0fd60281d57ded9a48c">aligned_mediump_bvec4</a></td></tr>
<tr class="memdesc:ga91bc1f513bb9b0fd60281d57ded9a48c"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector aligned in memory of bool values. <br /></td></tr>
<tr class="separator:ga91bc1f513bb9b0fd60281d57ded9a48c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga62a2dfd668c91072b72c3109fc6cda28"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga62a2dfd668c91072b72c3109fc6cda28"></a>
typedef mat&lt; 2, 2, double, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga62a2dfd668c91072b72c3109fc6cda28">aligned_mediump_dmat2</a></td></tr>
<tr class="memdesc:ga62a2dfd668c91072b72c3109fc6cda28"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix aligned in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga62a2dfd668c91072b72c3109fc6cda28"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9b7feec247d378dd407ba81f56ea96c8"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga9b7feec247d378dd407ba81f56ea96c8"></a>
typedef mat&lt; 2, 2, double, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga9b7feec247d378dd407ba81f56ea96c8">aligned_mediump_dmat2x2</a></td></tr>
<tr class="memdesc:ga9b7feec247d378dd407ba81f56ea96c8"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix aligned in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga9b7feec247d378dd407ba81f56ea96c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafcb189f4f93648fe7ca802ca4aca2eb8"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gafcb189f4f93648fe7ca802ca4aca2eb8"></a>
typedef mat&lt; 2, 3, double, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gafcb189f4f93648fe7ca802ca4aca2eb8">aligned_mediump_dmat2x3</a></td></tr>
<tr class="memdesc:gafcb189f4f93648fe7ca802ca4aca2eb8"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 3 matrix aligned in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gafcb189f4f93648fe7ca802ca4aca2eb8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga92f8873e3bbd5ca1323c8bbe5725cc5e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga92f8873e3bbd5ca1323c8bbe5725cc5e"></a>
typedef mat&lt; 2, 4, double, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga92f8873e3bbd5ca1323c8bbe5725cc5e">aligned_mediump_dmat2x4</a></td></tr>
<tr class="memdesc:ga92f8873e3bbd5ca1323c8bbe5725cc5e"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 4 matrix aligned in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga92f8873e3bbd5ca1323c8bbe5725cc5e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6dc2832b747c00e0a0df621aba196960"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga6dc2832b747c00e0a0df621aba196960"></a>
typedef mat&lt; 3, 3, double, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga6dc2832b747c00e0a0df621aba196960">aligned_mediump_dmat3</a></td></tr>
<tr class="memdesc:ga6dc2832b747c00e0a0df621aba196960"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix aligned in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga6dc2832b747c00e0a0df621aba196960"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5a97f0355d801de3444d42c1d5b40438"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga5a97f0355d801de3444d42c1d5b40438"></a>
typedef mat&lt; 3, 2, double, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga5a97f0355d801de3444d42c1d5b40438">aligned_mediump_dmat3x2</a></td></tr>
<tr class="memdesc:ga5a97f0355d801de3444d42c1d5b40438"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 2 matrix aligned in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga5a97f0355d801de3444d42c1d5b40438"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga649d0acf01054b17e679cf00e150e025"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga649d0acf01054b17e679cf00e150e025"></a>
typedef mat&lt; 3, 3, double, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga649d0acf01054b17e679cf00e150e025">aligned_mediump_dmat3x3</a></td></tr>
<tr class="memdesc:ga649d0acf01054b17e679cf00e150e025"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix aligned in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga649d0acf01054b17e679cf00e150e025"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga45e155a4840f69b2fa4ed8047a676860"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga45e155a4840f69b2fa4ed8047a676860"></a>
typedef mat&lt; 3, 4, double, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga45e155a4840f69b2fa4ed8047a676860">aligned_mediump_dmat3x4</a></td></tr>
<tr class="memdesc:ga45e155a4840f69b2fa4ed8047a676860"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 4 matrix aligned in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga45e155a4840f69b2fa4ed8047a676860"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8a9376d82f0e946e25137eb55543e6ce"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga8a9376d82f0e946e25137eb55543e6ce"></a>
typedef mat&lt; 4, 4, double, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga8a9376d82f0e946e25137eb55543e6ce">aligned_mediump_dmat4</a></td></tr>
<tr class="memdesc:ga8a9376d82f0e946e25137eb55543e6ce"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix aligned in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga8a9376d82f0e946e25137eb55543e6ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabc25e547f4de4af62403492532cd1b6d"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gabc25e547f4de4af62403492532cd1b6d"></a>
typedef mat&lt; 4, 2, double, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gabc25e547f4de4af62403492532cd1b6d">aligned_mediump_dmat4x2</a></td></tr>
<tr class="memdesc:gabc25e547f4de4af62403492532cd1b6d"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 2 matrix aligned in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gabc25e547f4de4af62403492532cd1b6d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae84f4763ecdc7457ecb7930bad12057c"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gae84f4763ecdc7457ecb7930bad12057c"></a>
typedef mat&lt; 4, 3, double, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gae84f4763ecdc7457ecb7930bad12057c">aligned_mediump_dmat4x3</a></td></tr>
<tr class="memdesc:gae84f4763ecdc7457ecb7930bad12057c"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 3 matrix aligned in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gae84f4763ecdc7457ecb7930bad12057c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa292ebaa907afdecb2d5967fb4fb1247"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaa292ebaa907afdecb2d5967fb4fb1247"></a>
typedef mat&lt; 4, 4, double, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaa292ebaa907afdecb2d5967fb4fb1247">aligned_mediump_dmat4x4</a></td></tr>
<tr class="memdesc:gaa292ebaa907afdecb2d5967fb4fb1247"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix aligned in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaa292ebaa907afdecb2d5967fb4fb1247"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7180b685c581adb224406a7f831608e3"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga7180b685c581adb224406a7f831608e3"></a>
typedef vec&lt; 1, double, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga7180b685c581adb224406a7f831608e3">aligned_mediump_dvec1</a></td></tr>
<tr class="memdesc:ga7180b685c581adb224406a7f831608e3"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector aligned in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga7180b685c581adb224406a7f831608e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9af1eabe22f569e70d9893be72eda0f5"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga9af1eabe22f569e70d9893be72eda0f5"></a>
typedef vec&lt; 2, double, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga9af1eabe22f569e70d9893be72eda0f5">aligned_mediump_dvec2</a></td></tr>
<tr class="memdesc:ga9af1eabe22f569e70d9893be72eda0f5"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector aligned in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga9af1eabe22f569e70d9893be72eda0f5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga058e7ddab1428e47f2197bdd3a5a6953"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga058e7ddab1428e47f2197bdd3a5a6953"></a>
typedef vec&lt; 3, double, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga058e7ddab1428e47f2197bdd3a5a6953">aligned_mediump_dvec3</a></td></tr>
<tr class="memdesc:ga058e7ddab1428e47f2197bdd3a5a6953"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector aligned in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga058e7ddab1428e47f2197bdd3a5a6953"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaffd747ea2aea1e69c2ecb04e68521b21"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaffd747ea2aea1e69c2ecb04e68521b21"></a>
typedef vec&lt; 4, double, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaffd747ea2aea1e69c2ecb04e68521b21">aligned_mediump_dvec4</a></td></tr>
<tr class="memdesc:gaffd747ea2aea1e69c2ecb04e68521b21"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector aligned in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaffd747ea2aea1e69c2ecb04e68521b21"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga20e63dd980b81af10cadbbe219316650"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga20e63dd980b81af10cadbbe219316650"></a>
typedef vec&lt; 1, int, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga20e63dd980b81af10cadbbe219316650">aligned_mediump_ivec1</a></td></tr>
<tr class="memdesc:ga20e63dd980b81af10cadbbe219316650"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector aligned in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:ga20e63dd980b81af10cadbbe219316650"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaea13d89d49daca2c796aeaa82fc2c2f2"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaea13d89d49daca2c796aeaa82fc2c2f2"></a>
typedef vec&lt; 2, int, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaea13d89d49daca2c796aeaa82fc2c2f2">aligned_mediump_ivec2</a></td></tr>
<tr class="memdesc:gaea13d89d49daca2c796aeaa82fc2c2f2"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector aligned in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:gaea13d89d49daca2c796aeaa82fc2c2f2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabbf0f15e9c3d9868e43241ad018f82bd"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gabbf0f15e9c3d9868e43241ad018f82bd"></a>
typedef vec&lt; 3, int, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gabbf0f15e9c3d9868e43241ad018f82bd">aligned_mediump_ivec3</a></td></tr>
<tr class="memdesc:gabbf0f15e9c3d9868e43241ad018f82bd"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector aligned in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:gabbf0f15e9c3d9868e43241ad018f82bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6099dd7878d0a78101a4250d8cd2d736"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga6099dd7878d0a78101a4250d8cd2d736"></a>
typedef vec&lt; 4, int, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga6099dd7878d0a78101a4250d8cd2d736">aligned_mediump_ivec4</a></td></tr>
<tr class="memdesc:ga6099dd7878d0a78101a4250d8cd2d736"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector aligned in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:ga6099dd7878d0a78101a4250d8cd2d736"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf6f041b212c57664d88bc6aefb7e36f3"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaf6f041b212c57664d88bc6aefb7e36f3"></a>
typedef mat&lt; 2, 2, float, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaf6f041b212c57664d88bc6aefb7e36f3">aligned_mediump_mat2</a></td></tr>
<tr class="memdesc:gaf6f041b212c57664d88bc6aefb7e36f3"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix aligned in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaf6f041b212c57664d88bc6aefb7e36f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga04bf49316ee777d42fcfe681ee37d7be"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga04bf49316ee777d42fcfe681ee37d7be"></a>
typedef mat&lt; 2, 2, float, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga04bf49316ee777d42fcfe681ee37d7be">aligned_mediump_mat2x2</a></td></tr>
<tr class="memdesc:ga04bf49316ee777d42fcfe681ee37d7be"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix aligned in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga04bf49316ee777d42fcfe681ee37d7be"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga26a0b61e444a51a37b9737cf4d84291b"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga26a0b61e444a51a37b9737cf4d84291b"></a>
typedef mat&lt; 2, 3, float, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga26a0b61e444a51a37b9737cf4d84291b">aligned_mediump_mat2x3</a></td></tr>
<tr class="memdesc:ga26a0b61e444a51a37b9737cf4d84291b"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 3 matrix aligned in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga26a0b61e444a51a37b9737cf4d84291b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga163facc9ed2692ea1300ed57c5d12b17"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga163facc9ed2692ea1300ed57c5d12b17"></a>
typedef mat&lt; 2, 4, float, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga163facc9ed2692ea1300ed57c5d12b17">aligned_mediump_mat2x4</a></td></tr>
<tr class="memdesc:ga163facc9ed2692ea1300ed57c5d12b17"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 4 matrix aligned in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga163facc9ed2692ea1300ed57c5d12b17"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3b76ba17ae5d53debeb6f7e55919a57c"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga3b76ba17ae5d53debeb6f7e55919a57c"></a>
typedef mat&lt; 3, 3, float, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga3b76ba17ae5d53debeb6f7e55919a57c">aligned_mediump_mat3</a></td></tr>
<tr class="memdesc:ga3b76ba17ae5d53debeb6f7e55919a57c"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix aligned in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga3b76ba17ae5d53debeb6f7e55919a57c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga80dee705d714300378e0847f45059097"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga80dee705d714300378e0847f45059097"></a>
typedef mat&lt; 3, 2, float, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga80dee705d714300378e0847f45059097">aligned_mediump_mat3x2</a></td></tr>
<tr class="memdesc:ga80dee705d714300378e0847f45059097"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 2 matrix aligned in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga80dee705d714300378e0847f45059097"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga721f5404caf40d68962dcc0529de71d9"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga721f5404caf40d68962dcc0529de71d9"></a>
typedef mat&lt; 3, 3, float, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga721f5404caf40d68962dcc0529de71d9">aligned_mediump_mat3x3</a></td></tr>
<tr class="memdesc:ga721f5404caf40d68962dcc0529de71d9"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix aligned in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga721f5404caf40d68962dcc0529de71d9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga98f4dc6722a2541a990918c074075359"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga98f4dc6722a2541a990918c074075359"></a>
typedef mat&lt; 3, 4, float, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga98f4dc6722a2541a990918c074075359">aligned_mediump_mat3x4</a></td></tr>
<tr class="memdesc:ga98f4dc6722a2541a990918c074075359"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 4 matrix aligned in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga98f4dc6722a2541a990918c074075359"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaeefee8317192174596852ce19b602720"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaeefee8317192174596852ce19b602720"></a>
typedef mat&lt; 4, 4, float, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaeefee8317192174596852ce19b602720">aligned_mediump_mat4</a></td></tr>
<tr class="memdesc:gaeefee8317192174596852ce19b602720"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix aligned in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaeefee8317192174596852ce19b602720"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga46f372a006345c252a41267657cc22c0"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga46f372a006345c252a41267657cc22c0"></a>
typedef mat&lt; 4, 2, float, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga46f372a006345c252a41267657cc22c0">aligned_mediump_mat4x2</a></td></tr>
<tr class="memdesc:ga46f372a006345c252a41267657cc22c0"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 2 matrix aligned in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga46f372a006345c252a41267657cc22c0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0effece4545acdebdc2a5512a303110e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga0effece4545acdebdc2a5512a303110e"></a>
typedef mat&lt; 4, 3, float, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga0effece4545acdebdc2a5512a303110e">aligned_mediump_mat4x3</a></td></tr>
<tr class="memdesc:ga0effece4545acdebdc2a5512a303110e"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 3 matrix aligned in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga0effece4545acdebdc2a5512a303110e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga312864244cae4e8f10f478cffd0f76de"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga312864244cae4e8f10f478cffd0f76de"></a>
typedef mat&lt; 4, 4, float, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga312864244cae4e8f10f478cffd0f76de">aligned_mediump_mat4x4</a></td></tr>
<tr class="memdesc:ga312864244cae4e8f10f478cffd0f76de"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix aligned in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga312864244cae4e8f10f478cffd0f76de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacb78126ea2eb779b41c7511128ff1283"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gacb78126ea2eb779b41c7511128ff1283"></a>
typedef vec&lt; 1, uint, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gacb78126ea2eb779b41c7511128ff1283">aligned_mediump_uvec1</a></td></tr>
<tr class="memdesc:gacb78126ea2eb779b41c7511128ff1283"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector aligned in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:gacb78126ea2eb779b41c7511128ff1283"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga081d53e0a71443d0b68ea61c870f9adc"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga081d53e0a71443d0b68ea61c870f9adc"></a>
typedef vec&lt; 2, uint, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga081d53e0a71443d0b68ea61c870f9adc">aligned_mediump_uvec2</a></td></tr>
<tr class="memdesc:ga081d53e0a71443d0b68ea61c870f9adc"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector aligned in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:ga081d53e0a71443d0b68ea61c870f9adc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad6fc921bdde2bdbc7e09b028e1e9b379"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gad6fc921bdde2bdbc7e09b028e1e9b379"></a>
typedef vec&lt; 3, uint, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gad6fc921bdde2bdbc7e09b028e1e9b379">aligned_mediump_uvec3</a></td></tr>
<tr class="memdesc:gad6fc921bdde2bdbc7e09b028e1e9b379"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector aligned in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:gad6fc921bdde2bdbc7e09b028e1e9b379"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga73ea0c1ba31580e107d21270883f51fc"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga73ea0c1ba31580e107d21270883f51fc"></a>
typedef vec&lt; 4, uint, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga73ea0c1ba31580e107d21270883f51fc">aligned_mediump_uvec4</a></td></tr>
<tr class="memdesc:ga73ea0c1ba31580e107d21270883f51fc"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector aligned in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:ga73ea0c1ba31580e107d21270883f51fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6b797eec76fa471e300158f3453b3b2e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga6b797eec76fa471e300158f3453b3b2e"></a>
typedef vec&lt; 1, float, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga6b797eec76fa471e300158f3453b3b2e">aligned_mediump_vec1</a></td></tr>
<tr class="memdesc:ga6b797eec76fa471e300158f3453b3b2e"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector aligned in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga6b797eec76fa471e300158f3453b3b2e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga026a55ddbf2bafb1432f1157a2708616"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga026a55ddbf2bafb1432f1157a2708616"></a>
typedef vec&lt; 2, float, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga026a55ddbf2bafb1432f1157a2708616">aligned_mediump_vec2</a></td></tr>
<tr class="memdesc:ga026a55ddbf2bafb1432f1157a2708616"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector aligned in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga026a55ddbf2bafb1432f1157a2708616"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3a25e494173f6a64637b08a1b50a2132"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga3a25e494173f6a64637b08a1b50a2132"></a>
typedef vec&lt; 3, float, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga3a25e494173f6a64637b08a1b50a2132">aligned_mediump_vec3</a></td></tr>
<tr class="memdesc:ga3a25e494173f6a64637b08a1b50a2132"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector aligned in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga3a25e494173f6a64637b08a1b50a2132"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga320d1c661cff2ef214eb50241f2928b2"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga320d1c661cff2ef214eb50241f2928b2"></a>
typedef vec&lt; 4, float, aligned_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga320d1c661cff2ef214eb50241f2928b2">aligned_mediump_vec4</a></td></tr>
<tr class="memdesc:ga320d1c661cff2ef214eb50241f2928b2"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector aligned in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga320d1c661cff2ef214eb50241f2928b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1ff8ed402c93d280ff0597c1c5e7c548"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga1ff8ed402c93d280ff0597c1c5e7c548"></a>
typedef aligned_highp_uvec1&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga1ff8ed402c93d280ff0597c1c5e7c548">aligned_uvec1</a></td></tr>
<tr class="memdesc:ga1ff8ed402c93d280ff0597c1c5e7c548"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector aligned in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:ga1ff8ed402c93d280ff0597c1c5e7c548"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga074137e3be58528d67041c223d49f398"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga074137e3be58528d67041c223d49f398"></a>
typedef aligned_highp_uvec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga074137e3be58528d67041c223d49f398">aligned_uvec2</a></td></tr>
<tr class="memdesc:ga074137e3be58528d67041c223d49f398"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector aligned in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:ga074137e3be58528d67041c223d49f398"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2a8d9c3046f89d854eb758adfa0811c0"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga2a8d9c3046f89d854eb758adfa0811c0"></a>
typedef aligned_highp_uvec3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga2a8d9c3046f89d854eb758adfa0811c0">aligned_uvec3</a></td></tr>
<tr class="memdesc:ga2a8d9c3046f89d854eb758adfa0811c0"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector aligned in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:ga2a8d9c3046f89d854eb758adfa0811c0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabf842c45eea186170c267a328e3f3b7d"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gabf842c45eea186170c267a328e3f3b7d"></a>
typedef aligned_highp_uvec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gabf842c45eea186170c267a328e3f3b7d">aligned_uvec4</a></td></tr>
<tr class="memdesc:gabf842c45eea186170c267a328e3f3b7d"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector aligned in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:gabf842c45eea186170c267a328e3f3b7d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga05e6d4c908965d04191c2070a8d0a65e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga05e6d4c908965d04191c2070a8d0a65e"></a>
typedef aligned_highp_vec1&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga05e6d4c908965d04191c2070a8d0a65e">aligned_vec1</a></td></tr>
<tr class="memdesc:ga05e6d4c908965d04191c2070a8d0a65e"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector aligned in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga05e6d4c908965d04191c2070a8d0a65e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0682462f8096a226773e20fac993cde5"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga0682462f8096a226773e20fac993cde5"></a>
typedef aligned_highp_vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga0682462f8096a226773e20fac993cde5">aligned_vec2</a></td></tr>
<tr class="memdesc:ga0682462f8096a226773e20fac993cde5"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector aligned in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga0682462f8096a226773e20fac993cde5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7cf643b66664e0cd3c48759ae66c2bd0"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga7cf643b66664e0cd3c48759ae66c2bd0"></a>
typedef aligned_highp_vec3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga7cf643b66664e0cd3c48759ae66c2bd0">aligned_vec3</a></td></tr>
<tr class="memdesc:ga7cf643b66664e0cd3c48759ae66c2bd0"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector aligned in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga7cf643b66664e0cd3c48759ae66c2bd0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga85d89e83cb8137e1be1446de8c3b643a"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga85d89e83cb8137e1be1446de8c3b643a"></a>
typedef aligned_highp_vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga85d89e83cb8137e1be1446de8c3b643a">aligned_vec4</a></td></tr>
<tr class="memdesc:ga85d89e83cb8137e1be1446de8c3b643a"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector aligned in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga85d89e83cb8137e1be1446de8c3b643a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga88632cea9008ac0ac1388e94e804a53c"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga88632cea9008ac0ac1388e94e804a53c"></a>
typedef packed_highp_bvec1&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga88632cea9008ac0ac1388e94e804a53c">packed_bvec1</a></td></tr>
<tr class="memdesc:ga88632cea9008ac0ac1388e94e804a53c"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 components vector tightly packed in memory of bool values. <br /></td></tr>
<tr class="separator:ga88632cea9008ac0ac1388e94e804a53c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab85245913eaa40ab82adabcae37086cb"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gab85245913eaa40ab82adabcae37086cb"></a>
typedef packed_highp_bvec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gab85245913eaa40ab82adabcae37086cb">packed_bvec2</a></td></tr>
<tr class="memdesc:gab85245913eaa40ab82adabcae37086cb"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector tightly packed in memory of bool values. <br /></td></tr>
<tr class="separator:gab85245913eaa40ab82adabcae37086cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0c48f9417f649e27f3fb0c9f733a18bd"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga0c48f9417f649e27f3fb0c9f733a18bd"></a>
typedef packed_highp_bvec3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga0c48f9417f649e27f3fb0c9f733a18bd">packed_bvec3</a></td></tr>
<tr class="memdesc:ga0c48f9417f649e27f3fb0c9f733a18bd"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector tightly packed in memory of bool values. <br /></td></tr>
<tr class="separator:ga0c48f9417f649e27f3fb0c9f733a18bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3180d7db84a74c402157df3bbc0ae3ed"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga3180d7db84a74c402157df3bbc0ae3ed"></a>
typedef packed_highp_bvec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga3180d7db84a74c402157df3bbc0ae3ed">packed_bvec4</a></td></tr>
<tr class="memdesc:ga3180d7db84a74c402157df3bbc0ae3ed"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector tightly packed in memory of bool values. <br /></td></tr>
<tr class="separator:ga3180d7db84a74c402157df3bbc0ae3ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad87408a8350918711f845f071bbe43fb"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gad87408a8350918711f845f071bbe43fb"></a>
typedef packed_highp_dmat2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gad87408a8350918711f845f071bbe43fb">packed_dmat2</a></td></tr>
<tr class="memdesc:gad87408a8350918711f845f071bbe43fb"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix tightly packed in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gad87408a8350918711f845f071bbe43fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaa33d8e06657a777efb0c72c44ce87a9"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaaa33d8e06657a777efb0c72c44ce87a9"></a>
typedef packed_highp_dmat2x2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaaa33d8e06657a777efb0c72c44ce87a9">packed_dmat2x2</a></td></tr>
<tr class="memdesc:gaaa33d8e06657a777efb0c72c44ce87a9"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix tightly packed in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gaaa33d8e06657a777efb0c72c44ce87a9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac3a5315f588ba04ad255188071ec4e22"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gac3a5315f588ba04ad255188071ec4e22"></a>
typedef packed_highp_dmat2x3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gac3a5315f588ba04ad255188071ec4e22">packed_dmat2x3</a></td></tr>
<tr class="memdesc:gac3a5315f588ba04ad255188071ec4e22"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 3 matrix tightly packed in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gac3a5315f588ba04ad255188071ec4e22"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae398fc3156f51d3684b08f62c1a5a6d4"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gae398fc3156f51d3684b08f62c1a5a6d4"></a>
typedef packed_highp_dmat2x4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gae398fc3156f51d3684b08f62c1a5a6d4">packed_dmat2x4</a></td></tr>
<tr class="memdesc:gae398fc3156f51d3684b08f62c1a5a6d4"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 4 matrix tightly packed in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gae398fc3156f51d3684b08f62c1a5a6d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga03dfc90d539cc87ea3a15a9caa5d2245"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga03dfc90d539cc87ea3a15a9caa5d2245"></a>
typedef packed_highp_dmat3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga03dfc90d539cc87ea3a15a9caa5d2245">packed_dmat3</a></td></tr>
<tr class="memdesc:ga03dfc90d539cc87ea3a15a9caa5d2245"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix tightly packed in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga03dfc90d539cc87ea3a15a9caa5d2245"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae36de20a4c0e0b1444b7903ae811d94e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gae36de20a4c0e0b1444b7903ae811d94e"></a>
typedef packed_highp_dmat3x2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gae36de20a4c0e0b1444b7903ae811d94e">packed_dmat3x2</a></td></tr>
<tr class="memdesc:gae36de20a4c0e0b1444b7903ae811d94e"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 2 matrix tightly packed in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gae36de20a4c0e0b1444b7903ae811d94e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab9b909f1392d86854334350efcae85f5"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gab9b909f1392d86854334350efcae85f5"></a>
typedef packed_highp_dmat3x3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gab9b909f1392d86854334350efcae85f5">packed_dmat3x3</a></td></tr>
<tr class="memdesc:gab9b909f1392d86854334350efcae85f5"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix tightly packed in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gab9b909f1392d86854334350efcae85f5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga199131fd279c92c2ac12df6d978f1dd6"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga199131fd279c92c2ac12df6d978f1dd6"></a>
typedef packed_highp_dmat3x4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga199131fd279c92c2ac12df6d978f1dd6">packed_dmat3x4</a></td></tr>
<tr class="memdesc:ga199131fd279c92c2ac12df6d978f1dd6"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 4 matrix tightly packed in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga199131fd279c92c2ac12df6d978f1dd6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gada980a3485640aa8151f368f17ad3086"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gada980a3485640aa8151f368f17ad3086"></a>
typedef packed_highp_dmat4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gada980a3485640aa8151f368f17ad3086">packed_dmat4</a></td></tr>
<tr class="memdesc:gada980a3485640aa8151f368f17ad3086"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix tightly packed in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gada980a3485640aa8151f368f17ad3086"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6dc65249730698d3cc9ac5d7e1bc4d72"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga6dc65249730698d3cc9ac5d7e1bc4d72"></a>
typedef packed_highp_dmat4x2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga6dc65249730698d3cc9ac5d7e1bc4d72">packed_dmat4x2</a></td></tr>
<tr class="memdesc:ga6dc65249730698d3cc9ac5d7e1bc4d72"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 2 matrix tightly packed in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga6dc65249730698d3cc9ac5d7e1bc4d72"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadf202aaa9ed71c09f9bbe347e43f8764"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gadf202aaa9ed71c09f9bbe347e43f8764"></a>
typedef packed_highp_dmat4x3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gadf202aaa9ed71c09f9bbe347e43f8764">packed_dmat4x3</a></td></tr>
<tr class="memdesc:gadf202aaa9ed71c09f9bbe347e43f8764"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 3 matrix tightly packed in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gadf202aaa9ed71c09f9bbe347e43f8764"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae20617435a6d042d7c38da2badd64a09"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gae20617435a6d042d7c38da2badd64a09"></a>
typedef packed_highp_dmat4x4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gae20617435a6d042d7c38da2badd64a09">packed_dmat4x4</a></td></tr>
<tr class="memdesc:gae20617435a6d042d7c38da2badd64a09"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix tightly packed in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gae20617435a6d042d7c38da2badd64a09"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga532f0c940649b1ee303acd572fc35531"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga532f0c940649b1ee303acd572fc35531"></a>
typedef packed_highp_dvec1&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga532f0c940649b1ee303acd572fc35531">packed_dvec1</a></td></tr>
<tr class="memdesc:ga532f0c940649b1ee303acd572fc35531"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector tightly packed in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga532f0c940649b1ee303acd572fc35531"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5c194b11fbda636f2ab20c3bd0079196"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga5c194b11fbda636f2ab20c3bd0079196"></a>
typedef packed_highp_dvec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga5c194b11fbda636f2ab20c3bd0079196">packed_dvec2</a></td></tr>
<tr class="memdesc:ga5c194b11fbda636f2ab20c3bd0079196"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector tightly packed in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga5c194b11fbda636f2ab20c3bd0079196"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0581ea552d86b2b5de7a2804bed80e72"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga0581ea552d86b2b5de7a2804bed80e72"></a>
typedef packed_highp_dvec3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga0581ea552d86b2b5de7a2804bed80e72">packed_dvec3</a></td></tr>
<tr class="memdesc:ga0581ea552d86b2b5de7a2804bed80e72"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector tightly packed in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga0581ea552d86b2b5de7a2804bed80e72"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae8a9b181f9dc813ad6e125a52b14b935"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gae8a9b181f9dc813ad6e125a52b14b935"></a>
typedef packed_highp_dvec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gae8a9b181f9dc813ad6e125a52b14b935">packed_dvec4</a></td></tr>
<tr class="memdesc:gae8a9b181f9dc813ad6e125a52b14b935"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector tightly packed in memory of double-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gae8a9b181f9dc813ad6e125a52b14b935"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga439e97795314b81cd15abd4e5c2e6e7a"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga439e97795314b81cd15abd4e5c2e6e7a"></a>
typedef vec&lt; 1, bool, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga439e97795314b81cd15abd4e5c2e6e7a">packed_highp_bvec1</a></td></tr>
<tr class="memdesc:ga439e97795314b81cd15abd4e5c2e6e7a"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector tightly packed in memory of bool values. <br /></td></tr>
<tr class="separator:ga439e97795314b81cd15abd4e5c2e6e7a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad791d671f4fcf1ed1ea41f752916b70a"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gad791d671f4fcf1ed1ea41f752916b70a"></a>
typedef vec&lt; 2, bool, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gad791d671f4fcf1ed1ea41f752916b70a">packed_highp_bvec2</a></td></tr>
<tr class="memdesc:gad791d671f4fcf1ed1ea41f752916b70a"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector tightly packed in memory of bool values. <br /></td></tr>
<tr class="separator:gad791d671f4fcf1ed1ea41f752916b70a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6a5a3250b57dfadc66735bc72911437f"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga6a5a3250b57dfadc66735bc72911437f"></a>
typedef vec&lt; 3, bool, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga6a5a3250b57dfadc66735bc72911437f">packed_highp_bvec3</a></td></tr>
<tr class="memdesc:ga6a5a3250b57dfadc66735bc72911437f"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector tightly packed in memory of bool values. <br /></td></tr>
<tr class="separator:ga6a5a3250b57dfadc66735bc72911437f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga09f517d88b996ef1b2f42fd54222b82d"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga09f517d88b996ef1b2f42fd54222b82d"></a>
typedef vec&lt; 4, bool, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga09f517d88b996ef1b2f42fd54222b82d">packed_highp_bvec4</a></td></tr>
<tr class="memdesc:ga09f517d88b996ef1b2f42fd54222b82d"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector tightly packed in memory of bool values. <br /></td></tr>
<tr class="separator:ga09f517d88b996ef1b2f42fd54222b82d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae29686632fd05efac0675d9a6370d77b"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gae29686632fd05efac0675d9a6370d77b"></a>
typedef mat&lt; 2, 2, double, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gae29686632fd05efac0675d9a6370d77b">packed_highp_dmat2</a></td></tr>
<tr class="memdesc:gae29686632fd05efac0675d9a6370d77b"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix tightly packed in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gae29686632fd05efac0675d9a6370d77b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga22bd6382b16052e301edbfc031b9f37a"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga22bd6382b16052e301edbfc031b9f37a"></a>
typedef mat&lt; 2, 2, double, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga22bd6382b16052e301edbfc031b9f37a">packed_highp_dmat2x2</a></td></tr>
<tr class="memdesc:ga22bd6382b16052e301edbfc031b9f37a"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix tightly packed in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga22bd6382b16052e301edbfc031b9f37a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga999d82719696d4c59f4d236dd08f273d"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga999d82719696d4c59f4d236dd08f273d"></a>
typedef mat&lt; 2, 3, double, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga999d82719696d4c59f4d236dd08f273d">packed_highp_dmat2x3</a></td></tr>
<tr class="memdesc:ga999d82719696d4c59f4d236dd08f273d"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 3 matrix tightly packed in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga999d82719696d4c59f4d236dd08f273d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6998ac2a8d7fe456b651a6336ed26bb0"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga6998ac2a8d7fe456b651a6336ed26bb0"></a>
typedef mat&lt; 2, 4, double, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga6998ac2a8d7fe456b651a6336ed26bb0">packed_highp_dmat2x4</a></td></tr>
<tr class="memdesc:ga6998ac2a8d7fe456b651a6336ed26bb0"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 4 matrix tightly packed in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga6998ac2a8d7fe456b651a6336ed26bb0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadac7c040c4810dd52b36fcd09d097400"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gadac7c040c4810dd52b36fcd09d097400"></a>
typedef mat&lt; 3, 3, double, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gadac7c040c4810dd52b36fcd09d097400">packed_highp_dmat3</a></td></tr>
<tr class="memdesc:gadac7c040c4810dd52b36fcd09d097400"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix tightly packed in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gadac7c040c4810dd52b36fcd09d097400"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab462744977beb85fb5c782bc2eea7b15"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gab462744977beb85fb5c782bc2eea7b15"></a>
typedef mat&lt; 3, 2, double, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gab462744977beb85fb5c782bc2eea7b15">packed_highp_dmat3x2</a></td></tr>
<tr class="memdesc:gab462744977beb85fb5c782bc2eea7b15"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 2 matrix tightly packed in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gab462744977beb85fb5c782bc2eea7b15"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga49e5a709d098523823b2f824e48672a6"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga49e5a709d098523823b2f824e48672a6"></a>
typedef mat&lt; 3, 3, double, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga49e5a709d098523823b2f824e48672a6">packed_highp_dmat3x3</a></td></tr>
<tr class="memdesc:ga49e5a709d098523823b2f824e48672a6"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix tightly packed in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga49e5a709d098523823b2f824e48672a6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2c67b3b0adab71c8680c3d819f1fa9b7"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga2c67b3b0adab71c8680c3d819f1fa9b7"></a>
typedef mat&lt; 3, 4, double, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga2c67b3b0adab71c8680c3d819f1fa9b7">packed_highp_dmat3x4</a></td></tr>
<tr class="memdesc:ga2c67b3b0adab71c8680c3d819f1fa9b7"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 4 matrix tightly packed in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga2c67b3b0adab71c8680c3d819f1fa9b7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6718822cd7af005a9b5bd6ee282f6ba6"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga6718822cd7af005a9b5bd6ee282f6ba6"></a>
typedef mat&lt; 4, 4, double, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga6718822cd7af005a9b5bd6ee282f6ba6">packed_highp_dmat4</a></td></tr>
<tr class="memdesc:ga6718822cd7af005a9b5bd6ee282f6ba6"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix tightly packed in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga6718822cd7af005a9b5bd6ee282f6ba6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga12e39e797fb724a5b51fcbea2513a7da"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga12e39e797fb724a5b51fcbea2513a7da"></a>
typedef mat&lt; 4, 2, double, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga12e39e797fb724a5b51fcbea2513a7da">packed_highp_dmat4x2</a></td></tr>
<tr class="memdesc:ga12e39e797fb724a5b51fcbea2513a7da"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 2 matrix tightly packed in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga12e39e797fb724a5b51fcbea2513a7da"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga79c2e9f82e67963c1ecad0ad6d0ec72e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga79c2e9f82e67963c1ecad0ad6d0ec72e"></a>
typedef mat&lt; 4, 3, double, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga79c2e9f82e67963c1ecad0ad6d0ec72e">packed_highp_dmat4x3</a></td></tr>
<tr class="memdesc:ga79c2e9f82e67963c1ecad0ad6d0ec72e"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 3 matrix tightly packed in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga79c2e9f82e67963c1ecad0ad6d0ec72e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2df58e03e5afded28707b4f7d077afb4"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga2df58e03e5afded28707b4f7d077afb4"></a>
typedef mat&lt; 4, 4, double, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga2df58e03e5afded28707b4f7d077afb4">packed_highp_dmat4x4</a></td></tr>
<tr class="memdesc:ga2df58e03e5afded28707b4f7d077afb4"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix tightly packed in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga2df58e03e5afded28707b4f7d077afb4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab472b2d917b5e6efd76e8c7dbfbbf9f1"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gab472b2d917b5e6efd76e8c7dbfbbf9f1"></a>
typedef vec&lt; 1, double, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gab472b2d917b5e6efd76e8c7dbfbbf9f1">packed_highp_dvec1</a></td></tr>
<tr class="memdesc:gab472b2d917b5e6efd76e8c7dbfbbf9f1"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector tightly packed in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gab472b2d917b5e6efd76e8c7dbfbbf9f1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5b2dc48fa19b684d207d69c6b145eb63"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga5b2dc48fa19b684d207d69c6b145eb63"></a>
typedef vec&lt; 2, double, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga5b2dc48fa19b684d207d69c6b145eb63">packed_highp_dvec2</a></td></tr>
<tr class="memdesc:ga5b2dc48fa19b684d207d69c6b145eb63"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector tightly packed in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga5b2dc48fa19b684d207d69c6b145eb63"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaac6b356ef00154da41aaae7d1549193"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaaac6b356ef00154da41aaae7d1549193"></a>
typedef vec&lt; 3, double, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaaac6b356ef00154da41aaae7d1549193">packed_highp_dvec3</a></td></tr>
<tr class="memdesc:gaaac6b356ef00154da41aaae7d1549193"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector tightly packed in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaaac6b356ef00154da41aaae7d1549193"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga81b5368fe485e2630aa9b44832d592e7"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga81b5368fe485e2630aa9b44832d592e7"></a>
typedef vec&lt; 4, double, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga81b5368fe485e2630aa9b44832d592e7">packed_highp_dvec4</a></td></tr>
<tr class="memdesc:ga81b5368fe485e2630aa9b44832d592e7"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector tightly packed in memory of double-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga81b5368fe485e2630aa9b44832d592e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7245acc887a5438f46fd85fdf076bb3b"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga7245acc887a5438f46fd85fdf076bb3b"></a>
typedef vec&lt; 1, int, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga7245acc887a5438f46fd85fdf076bb3b">packed_highp_ivec1</a></td></tr>
<tr class="memdesc:ga7245acc887a5438f46fd85fdf076bb3b"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector tightly packed in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:ga7245acc887a5438f46fd85fdf076bb3b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga54f368ec6b514a5aa4f28d40e6f93ef7"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga54f368ec6b514a5aa4f28d40e6f93ef7"></a>
typedef vec&lt; 2, int, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga54f368ec6b514a5aa4f28d40e6f93ef7">packed_highp_ivec2</a></td></tr>
<tr class="memdesc:ga54f368ec6b514a5aa4f28d40e6f93ef7"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector tightly packed in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:ga54f368ec6b514a5aa4f28d40e6f93ef7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga865a9c7bb22434b1b8c5ac31e164b628"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga865a9c7bb22434b1b8c5ac31e164b628"></a>
typedef vec&lt; 3, int, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga865a9c7bb22434b1b8c5ac31e164b628">packed_highp_ivec3</a></td></tr>
<tr class="memdesc:ga865a9c7bb22434b1b8c5ac31e164b628"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector tightly packed in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:ga865a9c7bb22434b1b8c5ac31e164b628"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad6f1b4e3a51c2c051814b60d5d1b8895"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gad6f1b4e3a51c2c051814b60d5d1b8895"></a>
typedef vec&lt; 4, int, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gad6f1b4e3a51c2c051814b60d5d1b8895">packed_highp_ivec4</a></td></tr>
<tr class="memdesc:gad6f1b4e3a51c2c051814b60d5d1b8895"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector tightly packed in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:gad6f1b4e3a51c2c051814b60d5d1b8895"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2f2d913d8cca2f935b2522964408c0b2"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga2f2d913d8cca2f935b2522964408c0b2"></a>
typedef mat&lt; 2, 2, float, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga2f2d913d8cca2f935b2522964408c0b2">packed_highp_mat2</a></td></tr>
<tr class="memdesc:ga2f2d913d8cca2f935b2522964408c0b2"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix tightly packed in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga2f2d913d8cca2f935b2522964408c0b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga245c12d2daf67feecaa2d3277c8f6661"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga245c12d2daf67feecaa2d3277c8f6661"></a>
typedef mat&lt; 2, 2, float, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga245c12d2daf67feecaa2d3277c8f6661">packed_highp_mat2x2</a></td></tr>
<tr class="memdesc:ga245c12d2daf67feecaa2d3277c8f6661"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix tightly packed in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga245c12d2daf67feecaa2d3277c8f6661"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga069cc8892aadae144c00f35297617d44"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga069cc8892aadae144c00f35297617d44"></a>
typedef mat&lt; 2, 3, float, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga069cc8892aadae144c00f35297617d44">packed_highp_mat2x3</a></td></tr>
<tr class="memdesc:ga069cc8892aadae144c00f35297617d44"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 3 matrix tightly packed in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga069cc8892aadae144c00f35297617d44"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6904d09b62141d09712b76983892f95b"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga6904d09b62141d09712b76983892f95b"></a>
typedef mat&lt; 2, 4, float, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga6904d09b62141d09712b76983892f95b">packed_highp_mat2x4</a></td></tr>
<tr class="memdesc:ga6904d09b62141d09712b76983892f95b"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 4 matrix tightly packed in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga6904d09b62141d09712b76983892f95b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabdd5fbffe8b8b8a7b33523f25b120dbe"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gabdd5fbffe8b8b8a7b33523f25b120dbe"></a>
typedef mat&lt; 3, 3, float, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gabdd5fbffe8b8b8a7b33523f25b120dbe">packed_highp_mat3</a></td></tr>
<tr class="memdesc:gabdd5fbffe8b8b8a7b33523f25b120dbe"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix tightly packed in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gabdd5fbffe8b8b8a7b33523f25b120dbe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2624719cb251d8de8cad1beaefc3a3f9"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga2624719cb251d8de8cad1beaefc3a3f9"></a>
typedef mat&lt; 3, 2, float, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga2624719cb251d8de8cad1beaefc3a3f9">packed_highp_mat3x2</a></td></tr>
<tr class="memdesc:ga2624719cb251d8de8cad1beaefc3a3f9"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 2 matrix tightly packed in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga2624719cb251d8de8cad1beaefc3a3f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf2e07527d678440bf0c20adbeb9177c5"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaf2e07527d678440bf0c20adbeb9177c5"></a>
typedef mat&lt; 3, 3, float, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaf2e07527d678440bf0c20adbeb9177c5">packed_highp_mat3x3</a></td></tr>
<tr class="memdesc:gaf2e07527d678440bf0c20adbeb9177c5"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix tightly packed in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaf2e07527d678440bf0c20adbeb9177c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga72102fa6ac2445aa3bb203128ad52449"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga72102fa6ac2445aa3bb203128ad52449"></a>
typedef mat&lt; 3, 4, float, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga72102fa6ac2445aa3bb203128ad52449">packed_highp_mat3x4</a></td></tr>
<tr class="memdesc:ga72102fa6ac2445aa3bb203128ad52449"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 4 matrix tightly packed in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga72102fa6ac2445aa3bb203128ad52449"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga253e8379b08d2dc6fe2800b2fb913203"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga253e8379b08d2dc6fe2800b2fb913203"></a>
typedef mat&lt; 4, 4, float, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga253e8379b08d2dc6fe2800b2fb913203">packed_highp_mat4</a></td></tr>
<tr class="memdesc:ga253e8379b08d2dc6fe2800b2fb913203"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix tightly packed in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga253e8379b08d2dc6fe2800b2fb913203"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae389c2071cf3cdb33e7812c6fd156710"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gae389c2071cf3cdb33e7812c6fd156710"></a>
typedef mat&lt; 4, 2, float, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gae389c2071cf3cdb33e7812c6fd156710">packed_highp_mat4x2</a></td></tr>
<tr class="memdesc:gae389c2071cf3cdb33e7812c6fd156710"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 2 matrix tightly packed in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gae389c2071cf3cdb33e7812c6fd156710"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4584f64394bd7123b7a8534741e4916c"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga4584f64394bd7123b7a8534741e4916c"></a>
typedef mat&lt; 4, 3, float, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga4584f64394bd7123b7a8534741e4916c">packed_highp_mat4x3</a></td></tr>
<tr class="memdesc:ga4584f64394bd7123b7a8534741e4916c"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 3 matrix tightly packed in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga4584f64394bd7123b7a8534741e4916c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0149fe15668925147e07c94fd2c2d6ae"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga0149fe15668925147e07c94fd2c2d6ae"></a>
typedef mat&lt; 4, 4, float, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga0149fe15668925147e07c94fd2c2d6ae">packed_highp_mat4x4</a></td></tr>
<tr class="memdesc:ga0149fe15668925147e07c94fd2c2d6ae"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix tightly packed in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga0149fe15668925147e07c94fd2c2d6ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8c32b53f628a3616aa5061e58d66fe74"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga8c32b53f628a3616aa5061e58d66fe74"></a>
typedef vec&lt; 1, uint, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga8c32b53f628a3616aa5061e58d66fe74">packed_highp_uvec1</a></td></tr>
<tr class="memdesc:ga8c32b53f628a3616aa5061e58d66fe74"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector tightly packed in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:ga8c32b53f628a3616aa5061e58d66fe74"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab704d4fb15f6f96d70e363d5db7060cd"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gab704d4fb15f6f96d70e363d5db7060cd"></a>
typedef vec&lt; 2, uint, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gab704d4fb15f6f96d70e363d5db7060cd">packed_highp_uvec2</a></td></tr>
<tr class="memdesc:gab704d4fb15f6f96d70e363d5db7060cd"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector tightly packed in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:gab704d4fb15f6f96d70e363d5db7060cd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0b570da473fec4619db5aa0dce5133b0"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga0b570da473fec4619db5aa0dce5133b0"></a>
typedef vec&lt; 3, uint, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga0b570da473fec4619db5aa0dce5133b0">packed_highp_uvec3</a></td></tr>
<tr class="memdesc:ga0b570da473fec4619db5aa0dce5133b0"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector tightly packed in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:ga0b570da473fec4619db5aa0dce5133b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa582f38c82aef61dea7aaedf15bb06a6"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaa582f38c82aef61dea7aaedf15bb06a6"></a>
typedef vec&lt; 4, uint, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaa582f38c82aef61dea7aaedf15bb06a6">packed_highp_uvec4</a></td></tr>
<tr class="memdesc:gaa582f38c82aef61dea7aaedf15bb06a6"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector tightly packed in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:gaa582f38c82aef61dea7aaedf15bb06a6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga56473759d2702ee19ab7f91d0017fa70"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga56473759d2702ee19ab7f91d0017fa70"></a>
typedef vec&lt; 1, float, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga56473759d2702ee19ab7f91d0017fa70">packed_highp_vec1</a></td></tr>
<tr class="memdesc:ga56473759d2702ee19ab7f91d0017fa70"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector tightly packed in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga56473759d2702ee19ab7f91d0017fa70"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6b8b9475e7c3b16aed13edbc460bbc4d"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga6b8b9475e7c3b16aed13edbc460bbc4d"></a>
typedef vec&lt; 2, float, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga6b8b9475e7c3b16aed13edbc460bbc4d">packed_highp_vec2</a></td></tr>
<tr class="memdesc:ga6b8b9475e7c3b16aed13edbc460bbc4d"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector tightly packed in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga6b8b9475e7c3b16aed13edbc460bbc4d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3815661df0e2de79beff8168c09adf1e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga3815661df0e2de79beff8168c09adf1e"></a>
typedef vec&lt; 3, float, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga3815661df0e2de79beff8168c09adf1e">packed_highp_vec3</a></td></tr>
<tr class="memdesc:ga3815661df0e2de79beff8168c09adf1e"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector tightly packed in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga3815661df0e2de79beff8168c09adf1e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4015f36bf5a5adb6ac5d45beed959867"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga4015f36bf5a5adb6ac5d45beed959867"></a>
typedef vec&lt; 4, float, packed_highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga4015f36bf5a5adb6ac5d45beed959867">packed_highp_vec4</a></td></tr>
<tr class="memdesc:ga4015f36bf5a5adb6ac5d45beed959867"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector tightly packed in memory of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga4015f36bf5a5adb6ac5d45beed959867"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga11581a06fc7bf941fa4d4b6aca29812c"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga11581a06fc7bf941fa4d4b6aca29812c"></a>
typedef packed_highp_ivec1&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga11581a06fc7bf941fa4d4b6aca29812c">packed_ivec1</a></td></tr>
<tr class="memdesc:ga11581a06fc7bf941fa4d4b6aca29812c"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector tightly packed in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:ga11581a06fc7bf941fa4d4b6aca29812c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1fe4c5f56b8087d773aa90dc88a257a7"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga1fe4c5f56b8087d773aa90dc88a257a7"></a>
typedef packed_highp_ivec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga1fe4c5f56b8087d773aa90dc88a257a7">packed_ivec2</a></td></tr>
<tr class="memdesc:ga1fe4c5f56b8087d773aa90dc88a257a7"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector tightly packed in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:ga1fe4c5f56b8087d773aa90dc88a257a7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae157682a7847161787951ba1db4cf325"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gae157682a7847161787951ba1db4cf325"></a>
typedef packed_highp_ivec3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gae157682a7847161787951ba1db4cf325">packed_ivec3</a></td></tr>
<tr class="memdesc:gae157682a7847161787951ba1db4cf325"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector tightly packed in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:gae157682a7847161787951ba1db4cf325"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac228b70372abd561340d5f926a7c1778"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gac228b70372abd561340d5f926a7c1778"></a>
typedef packed_highp_ivec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gac228b70372abd561340d5f926a7c1778">packed_ivec4</a></td></tr>
<tr class="memdesc:gac228b70372abd561340d5f926a7c1778"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector tightly packed in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:gac228b70372abd561340d5f926a7c1778"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae3c8750f53259ece334d3aa3b3649a40"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gae3c8750f53259ece334d3aa3b3649a40"></a>
typedef vec&lt; 1, bool, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gae3c8750f53259ece334d3aa3b3649a40">packed_lowp_bvec1</a></td></tr>
<tr class="memdesc:gae3c8750f53259ece334d3aa3b3649a40"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector tightly packed in memory of bool values. <br /></td></tr>
<tr class="separator:gae3c8750f53259ece334d3aa3b3649a40"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac969befedbda69eb78d4e23f751fdbee"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gac969befedbda69eb78d4e23f751fdbee"></a>
typedef vec&lt; 2, bool, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gac969befedbda69eb78d4e23f751fdbee">packed_lowp_bvec2</a></td></tr>
<tr class="memdesc:gac969befedbda69eb78d4e23f751fdbee"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector tightly packed in memory of bool values. <br /></td></tr>
<tr class="separator:gac969befedbda69eb78d4e23f751fdbee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7c20adbe1409e3fe4544677a7f6fe954"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga7c20adbe1409e3fe4544677a7f6fe954"></a>
typedef vec&lt; 3, bool, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga7c20adbe1409e3fe4544677a7f6fe954">packed_lowp_bvec3</a></td></tr>
<tr class="memdesc:ga7c20adbe1409e3fe4544677a7f6fe954"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector tightly packed in memory of bool values. <br /></td></tr>
<tr class="separator:ga7c20adbe1409e3fe4544677a7f6fe954"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae473587cff3092edc0877fc691c26a0b"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gae473587cff3092edc0877fc691c26a0b"></a>
typedef vec&lt; 4, bool, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gae473587cff3092edc0877fc691c26a0b">packed_lowp_bvec4</a></td></tr>
<tr class="memdesc:gae473587cff3092edc0877fc691c26a0b"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector tightly packed in memory of bool values. <br /></td></tr>
<tr class="separator:gae473587cff3092edc0877fc691c26a0b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac93f9b1a35b9de4f456b9f2dfeaf1097"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gac93f9b1a35b9de4f456b9f2dfeaf1097"></a>
typedef mat&lt; 2, 2, double, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gac93f9b1a35b9de4f456b9f2dfeaf1097">packed_lowp_dmat2</a></td></tr>
<tr class="memdesc:gac93f9b1a35b9de4f456b9f2dfeaf1097"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix tightly packed in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gac93f9b1a35b9de4f456b9f2dfeaf1097"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaeeaff6c132ec91ebd21da3a2399548ea"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaeeaff6c132ec91ebd21da3a2399548ea"></a>
typedef mat&lt; 2, 2, double, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaeeaff6c132ec91ebd21da3a2399548ea">packed_lowp_dmat2x2</a></td></tr>
<tr class="memdesc:gaeeaff6c132ec91ebd21da3a2399548ea"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix tightly packed in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaeeaff6c132ec91ebd21da3a2399548ea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2ccdcd4846775cbe4f9d12e71d55b5d2"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga2ccdcd4846775cbe4f9d12e71d55b5d2"></a>
typedef mat&lt; 2, 3, double, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga2ccdcd4846775cbe4f9d12e71d55b5d2">packed_lowp_dmat2x3</a></td></tr>
<tr class="memdesc:ga2ccdcd4846775cbe4f9d12e71d55b5d2"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 3 matrix tightly packed in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga2ccdcd4846775cbe4f9d12e71d55b5d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac870c47d2d9d48503f6c9ee3baec8ce1"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gac870c47d2d9d48503f6c9ee3baec8ce1"></a>
typedef mat&lt; 2, 4, double, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gac870c47d2d9d48503f6c9ee3baec8ce1">packed_lowp_dmat2x4</a></td></tr>
<tr class="memdesc:gac870c47d2d9d48503f6c9ee3baec8ce1"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 4 matrix tightly packed in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gac870c47d2d9d48503f6c9ee3baec8ce1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3894a059eeaacec8791c25de398d9955"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga3894a059eeaacec8791c25de398d9955"></a>
typedef mat&lt; 3, 3, double, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga3894a059eeaacec8791c25de398d9955">packed_lowp_dmat3</a></td></tr>
<tr class="memdesc:ga3894a059eeaacec8791c25de398d9955"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix tightly packed in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga3894a059eeaacec8791c25de398d9955"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga23ec236950f5859f59197663266b535d"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga23ec236950f5859f59197663266b535d"></a>
typedef mat&lt; 3, 2, double, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga23ec236950f5859f59197663266b535d">packed_lowp_dmat3x2</a></td></tr>
<tr class="memdesc:ga23ec236950f5859f59197663266b535d"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 2 matrix tightly packed in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga23ec236950f5859f59197663266b535d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4a7c7d8c3a663d0ec2a858cbfa14e54c"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga4a7c7d8c3a663d0ec2a858cbfa14e54c"></a>
typedef mat&lt; 3, 3, double, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga4a7c7d8c3a663d0ec2a858cbfa14e54c">packed_lowp_dmat3x3</a></td></tr>
<tr class="memdesc:ga4a7c7d8c3a663d0ec2a858cbfa14e54c"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix tightly packed in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga4a7c7d8c3a663d0ec2a858cbfa14e54c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8fc0e66da83599071b7ec17510686cd9"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga8fc0e66da83599071b7ec17510686cd9"></a>
typedef mat&lt; 3, 4, double, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga8fc0e66da83599071b7ec17510686cd9">packed_lowp_dmat3x4</a></td></tr>
<tr class="memdesc:ga8fc0e66da83599071b7ec17510686cd9"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 4 matrix tightly packed in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga8fc0e66da83599071b7ec17510686cd9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga03e1edf5666c40affe39aee35c87956f"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga03e1edf5666c40affe39aee35c87956f"></a>
typedef mat&lt; 4, 4, double, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga03e1edf5666c40affe39aee35c87956f">packed_lowp_dmat4</a></td></tr>
<tr class="memdesc:ga03e1edf5666c40affe39aee35c87956f"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix tightly packed in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga03e1edf5666c40affe39aee35c87956f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga39658fb13369db869d363684bd8399c0"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga39658fb13369db869d363684bd8399c0"></a>
typedef mat&lt; 4, 2, double, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga39658fb13369db869d363684bd8399c0">packed_lowp_dmat4x2</a></td></tr>
<tr class="memdesc:ga39658fb13369db869d363684bd8399c0"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 2 matrix tightly packed in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga39658fb13369db869d363684bd8399c0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga30b0351eebc18c6056101359bdd3a359"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga30b0351eebc18c6056101359bdd3a359"></a>
typedef mat&lt; 4, 3, double, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga30b0351eebc18c6056101359bdd3a359">packed_lowp_dmat4x3</a></td></tr>
<tr class="memdesc:ga30b0351eebc18c6056101359bdd3a359"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 3 matrix tightly packed in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga30b0351eebc18c6056101359bdd3a359"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0294d4c45151425c86a11deee7693c0e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga0294d4c45151425c86a11deee7693c0e"></a>
typedef mat&lt; 4, 4, double, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga0294d4c45151425c86a11deee7693c0e">packed_lowp_dmat4x4</a></td></tr>
<tr class="memdesc:ga0294d4c45151425c86a11deee7693c0e"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix tightly packed in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga0294d4c45151425c86a11deee7693c0e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga054050e9d4e78d81db0e6d1573b1c624"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga054050e9d4e78d81db0e6d1573b1c624"></a>
typedef vec&lt; 1, double, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga054050e9d4e78d81db0e6d1573b1c624">packed_lowp_dvec1</a></td></tr>
<tr class="memdesc:ga054050e9d4e78d81db0e6d1573b1c624"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector tightly packed in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga054050e9d4e78d81db0e6d1573b1c624"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadc19938ddb204bfcb4d9ef35b1e2bf93"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gadc19938ddb204bfcb4d9ef35b1e2bf93"></a>
typedef vec&lt; 2, double, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gadc19938ddb204bfcb4d9ef35b1e2bf93">packed_lowp_dvec2</a></td></tr>
<tr class="memdesc:gadc19938ddb204bfcb4d9ef35b1e2bf93"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector tightly packed in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gadc19938ddb204bfcb4d9ef35b1e2bf93"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9189210cabd6651a5e14a4c46fb20598"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga9189210cabd6651a5e14a4c46fb20598"></a>
typedef vec&lt; 3, double, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga9189210cabd6651a5e14a4c46fb20598">packed_lowp_dvec3</a></td></tr>
<tr class="memdesc:ga9189210cabd6651a5e14a4c46fb20598"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector tightly packed in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga9189210cabd6651a5e14a4c46fb20598"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga262dafd0c001c3a38d1cc91d024ca738"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga262dafd0c001c3a38d1cc91d024ca738"></a>
typedef vec&lt; 4, double, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga262dafd0c001c3a38d1cc91d024ca738">packed_lowp_dvec4</a></td></tr>
<tr class="memdesc:ga262dafd0c001c3a38d1cc91d024ca738"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector tightly packed in memory of double-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga262dafd0c001c3a38d1cc91d024ca738"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf22b77f1cf3e73b8b1dddfe7f959357c"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaf22b77f1cf3e73b8b1dddfe7f959357c"></a>
typedef vec&lt; 1, int, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaf22b77f1cf3e73b8b1dddfe7f959357c">packed_lowp_ivec1</a></td></tr>
<tr class="memdesc:gaf22b77f1cf3e73b8b1dddfe7f959357c"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector tightly packed in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:gaf22b77f1cf3e73b8b1dddfe7f959357c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga52635859f5ef660ab999d22c11b7867f"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga52635859f5ef660ab999d22c11b7867f"></a>
typedef vec&lt; 2, int, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga52635859f5ef660ab999d22c11b7867f">packed_lowp_ivec2</a></td></tr>
<tr class="memdesc:ga52635859f5ef660ab999d22c11b7867f"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector tightly packed in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:ga52635859f5ef660ab999d22c11b7867f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga98c9d122a959e9f3ce10a5623c310f5d"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga98c9d122a959e9f3ce10a5623c310f5d"></a>
typedef vec&lt; 3, int, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga98c9d122a959e9f3ce10a5623c310f5d">packed_lowp_ivec3</a></td></tr>
<tr class="memdesc:ga98c9d122a959e9f3ce10a5623c310f5d"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector tightly packed in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:ga98c9d122a959e9f3ce10a5623c310f5d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga931731b8ae3b54c7ecc221509dae96bc"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga931731b8ae3b54c7ecc221509dae96bc"></a>
typedef vec&lt; 4, int, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga931731b8ae3b54c7ecc221509dae96bc">packed_lowp_ivec4</a></td></tr>
<tr class="memdesc:ga931731b8ae3b54c7ecc221509dae96bc"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector tightly packed in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:ga931731b8ae3b54c7ecc221509dae96bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga70dcb9ef0b24e832772a7405efa9669a"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga70dcb9ef0b24e832772a7405efa9669a"></a>
typedef mat&lt; 2, 2, float, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga70dcb9ef0b24e832772a7405efa9669a">packed_lowp_mat2</a></td></tr>
<tr class="memdesc:ga70dcb9ef0b24e832772a7405efa9669a"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix tightly packed in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga70dcb9ef0b24e832772a7405efa9669a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac70667c7642ec8d50245e6e6936a3927"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gac70667c7642ec8d50245e6e6936a3927"></a>
typedef mat&lt; 2, 2, float, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gac70667c7642ec8d50245e6e6936a3927">packed_lowp_mat2x2</a></td></tr>
<tr class="memdesc:gac70667c7642ec8d50245e6e6936a3927"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix tightly packed in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gac70667c7642ec8d50245e6e6936a3927"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3e7df5a11e1be27bc29a4c0d3956f234"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga3e7df5a11e1be27bc29a4c0d3956f234"></a>
typedef mat&lt; 2, 3, float, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga3e7df5a11e1be27bc29a4c0d3956f234">packed_lowp_mat2x3</a></td></tr>
<tr class="memdesc:ga3e7df5a11e1be27bc29a4c0d3956f234"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 3 matrix tightly packed in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga3e7df5a11e1be27bc29a4c0d3956f234"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaea9c555e669dc56c45d95dcc75d59bf3"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaea9c555e669dc56c45d95dcc75d59bf3"></a>
typedef mat&lt; 2, 4, float, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaea9c555e669dc56c45d95dcc75d59bf3">packed_lowp_mat2x4</a></td></tr>
<tr class="memdesc:gaea9c555e669dc56c45d95dcc75d59bf3"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 4 matrix tightly packed in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaea9c555e669dc56c45d95dcc75d59bf3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0d22400969dd223465b2900fecfb4f53"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga0d22400969dd223465b2900fecfb4f53"></a>
typedef mat&lt; 3, 3, float, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga0d22400969dd223465b2900fecfb4f53">packed_lowp_mat3</a></td></tr>
<tr class="memdesc:ga0d22400969dd223465b2900fecfb4f53"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix tightly packed in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga0d22400969dd223465b2900fecfb4f53"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga128cd52649621861635fab746df91735"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga128cd52649621861635fab746df91735"></a>
typedef mat&lt; 3, 2, float, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga128cd52649621861635fab746df91735">packed_lowp_mat3x2</a></td></tr>
<tr class="memdesc:ga128cd52649621861635fab746df91735"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 2 matrix tightly packed in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga128cd52649621861635fab746df91735"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5adf1802c5375a9dfb1729691bedd94e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga5adf1802c5375a9dfb1729691bedd94e"></a>
typedef mat&lt; 3, 3, float, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga5adf1802c5375a9dfb1729691bedd94e">packed_lowp_mat3x3</a></td></tr>
<tr class="memdesc:ga5adf1802c5375a9dfb1729691bedd94e"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix tightly packed in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga5adf1802c5375a9dfb1729691bedd94e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga92247ca09fa03c4013ba364f3a0fca7f"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga92247ca09fa03c4013ba364f3a0fca7f"></a>
typedef mat&lt; 3, 4, float, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga92247ca09fa03c4013ba364f3a0fca7f">packed_lowp_mat3x4</a></td></tr>
<tr class="memdesc:ga92247ca09fa03c4013ba364f3a0fca7f"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 4 matrix tightly packed in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga92247ca09fa03c4013ba364f3a0fca7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2a1dd2387725a335413d4c4fee8609c4"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga2a1dd2387725a335413d4c4fee8609c4"></a>
typedef mat&lt; 4, 4, float, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga2a1dd2387725a335413d4c4fee8609c4">packed_lowp_mat4</a></td></tr>
<tr class="memdesc:ga2a1dd2387725a335413d4c4fee8609c4"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix tightly packed in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga2a1dd2387725a335413d4c4fee8609c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8f22607dcd090cd280071ccc689f4079"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga8f22607dcd090cd280071ccc689f4079"></a>
typedef mat&lt; 4, 2, float, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga8f22607dcd090cd280071ccc689f4079">packed_lowp_mat4x2</a></td></tr>
<tr class="memdesc:ga8f22607dcd090cd280071ccc689f4079"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 2 matrix tightly packed in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga8f22607dcd090cd280071ccc689f4079"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7661d759d6ad218e132e3d051e7b2c6c"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga7661d759d6ad218e132e3d051e7b2c6c"></a>
typedef mat&lt; 4, 3, float, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga7661d759d6ad218e132e3d051e7b2c6c">packed_lowp_mat4x3</a></td></tr>
<tr class="memdesc:ga7661d759d6ad218e132e3d051e7b2c6c"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 3 matrix tightly packed in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga7661d759d6ad218e132e3d051e7b2c6c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga776f18d1a6e7d399f05d386167dc60f5"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga776f18d1a6e7d399f05d386167dc60f5"></a>
typedef mat&lt; 4, 4, float, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga776f18d1a6e7d399f05d386167dc60f5">packed_lowp_mat4x4</a></td></tr>
<tr class="memdesc:ga776f18d1a6e7d399f05d386167dc60f5"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix tightly packed in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga776f18d1a6e7d399f05d386167dc60f5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf111fed760ecce16cb1988807569bee5"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaf111fed760ecce16cb1988807569bee5"></a>
typedef vec&lt; 1, uint, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaf111fed760ecce16cb1988807569bee5">packed_lowp_uvec1</a></td></tr>
<tr class="memdesc:gaf111fed760ecce16cb1988807569bee5"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector tightly packed in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:gaf111fed760ecce16cb1988807569bee5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga958210fe245a75b058325d367c951132"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga958210fe245a75b058325d367c951132"></a>
typedef vec&lt; 2, uint, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga958210fe245a75b058325d367c951132">packed_lowp_uvec2</a></td></tr>
<tr class="memdesc:ga958210fe245a75b058325d367c951132"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector tightly packed in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:ga958210fe245a75b058325d367c951132"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga576a3f8372197a56a79dee1c8280f485"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga576a3f8372197a56a79dee1c8280f485"></a>
typedef vec&lt; 3, uint, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga576a3f8372197a56a79dee1c8280f485">packed_lowp_uvec3</a></td></tr>
<tr class="memdesc:ga576a3f8372197a56a79dee1c8280f485"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector tightly packed in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:ga576a3f8372197a56a79dee1c8280f485"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafdd97922b4a2a42cd0c99a13877ff4da"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gafdd97922b4a2a42cd0c99a13877ff4da"></a>
typedef vec&lt; 4, uint, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gafdd97922b4a2a42cd0c99a13877ff4da">packed_lowp_uvec4</a></td></tr>
<tr class="memdesc:gafdd97922b4a2a42cd0c99a13877ff4da"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector tightly packed in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:gafdd97922b4a2a42cd0c99a13877ff4da"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0a6198fe64166a6a61084d43c71518a9"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga0a6198fe64166a6a61084d43c71518a9"></a>
typedef vec&lt; 1, float, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga0a6198fe64166a6a61084d43c71518a9">packed_lowp_vec1</a></td></tr>
<tr class="memdesc:ga0a6198fe64166a6a61084d43c71518a9"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector tightly packed in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga0a6198fe64166a6a61084d43c71518a9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafbf1c2cce307c5594b165819ed83bf5d"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gafbf1c2cce307c5594b165819ed83bf5d"></a>
typedef vec&lt; 2, float, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gafbf1c2cce307c5594b165819ed83bf5d">packed_lowp_vec2</a></td></tr>
<tr class="memdesc:gafbf1c2cce307c5594b165819ed83bf5d"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector tightly packed in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gafbf1c2cce307c5594b165819ed83bf5d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3a30c137c1f8cce478c28eab0427a570"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga3a30c137c1f8cce478c28eab0427a570"></a>
typedef vec&lt; 3, float, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga3a30c137c1f8cce478c28eab0427a570">packed_lowp_vec3</a></td></tr>
<tr class="memdesc:ga3a30c137c1f8cce478c28eab0427a570"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector tightly packed in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga3a30c137c1f8cce478c28eab0427a570"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3cc94fb8de80bbd8a4aa7a5b206d304a"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga3cc94fb8de80bbd8a4aa7a5b206d304a"></a>
typedef vec&lt; 4, float, packed_lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga3cc94fb8de80bbd8a4aa7a5b206d304a">packed_lowp_vec4</a></td></tr>
<tr class="memdesc:ga3cc94fb8de80bbd8a4aa7a5b206d304a"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector tightly packed in memory of single-precision floating-point numbers using low precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga3cc94fb8de80bbd8a4aa7a5b206d304a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadd019b43fcf42e1590d45dddaa504a1a"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gadd019b43fcf42e1590d45dddaa504a1a"></a>
typedef packed_highp_mat2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gadd019b43fcf42e1590d45dddaa504a1a">packed_mat2</a></td></tr>
<tr class="memdesc:gadd019b43fcf42e1590d45dddaa504a1a"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix tightly packed in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gadd019b43fcf42e1590d45dddaa504a1a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga51eaadcdc292c8750f746a5dc3e6c517"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga51eaadcdc292c8750f746a5dc3e6c517"></a>
typedef packed_highp_mat2x2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga51eaadcdc292c8750f746a5dc3e6c517">packed_mat2x2</a></td></tr>
<tr class="memdesc:ga51eaadcdc292c8750f746a5dc3e6c517"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix tightly packed in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga51eaadcdc292c8750f746a5dc3e6c517"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga301b76a89b8a9625501ca58815017f20"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga301b76a89b8a9625501ca58815017f20"></a>
typedef packed_highp_mat2x3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga301b76a89b8a9625501ca58815017f20">packed_mat2x3</a></td></tr>
<tr class="memdesc:ga301b76a89b8a9625501ca58815017f20"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 3 matrix tightly packed in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga301b76a89b8a9625501ca58815017f20"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac401da1dd9177ad81d7618a2a5541e23"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gac401da1dd9177ad81d7618a2a5541e23"></a>
typedef packed_highp_mat2x4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gac401da1dd9177ad81d7618a2a5541e23">packed_mat2x4</a></td></tr>
<tr class="memdesc:gac401da1dd9177ad81d7618a2a5541e23"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 4 matrix tightly packed in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gac401da1dd9177ad81d7618a2a5541e23"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9bc12b0ab7be8448836711b77cc7b83a"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga9bc12b0ab7be8448836711b77cc7b83a"></a>
typedef packed_highp_mat3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga9bc12b0ab7be8448836711b77cc7b83a">packed_mat3</a></td></tr>
<tr class="memdesc:ga9bc12b0ab7be8448836711b77cc7b83a"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix tightly packed in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga9bc12b0ab7be8448836711b77cc7b83a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga134f0d99fbd2459c13cd9ebd056509fa"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga134f0d99fbd2459c13cd9ebd056509fa"></a>
typedef packed_highp_mat3x2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga134f0d99fbd2459c13cd9ebd056509fa">packed_mat3x2</a></td></tr>
<tr class="memdesc:ga134f0d99fbd2459c13cd9ebd056509fa"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 2 matrix tightly packed in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga134f0d99fbd2459c13cd9ebd056509fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6c1dbe8cde9fbb231284b01f8aeaaa99"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga6c1dbe8cde9fbb231284b01f8aeaaa99"></a>
typedef packed_highp_mat3x3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga6c1dbe8cde9fbb231284b01f8aeaaa99">packed_mat3x3</a></td></tr>
<tr class="memdesc:ga6c1dbe8cde9fbb231284b01f8aeaaa99"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix tightly packed in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga6c1dbe8cde9fbb231284b01f8aeaaa99"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad63515526cccfe88ffa8fe5ed64f95f8"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gad63515526cccfe88ffa8fe5ed64f95f8"></a>
typedef packed_highp_mat3x4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gad63515526cccfe88ffa8fe5ed64f95f8">packed_mat3x4</a></td></tr>
<tr class="memdesc:gad63515526cccfe88ffa8fe5ed64f95f8"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 4 matrix tightly packed in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gad63515526cccfe88ffa8fe5ed64f95f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2c139854e5b04cf08a957dee3b510441"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga2c139854e5b04cf08a957dee3b510441"></a>
typedef packed_highp_mat4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga2c139854e5b04cf08a957dee3b510441">packed_mat4</a></td></tr>
<tr class="memdesc:ga2c139854e5b04cf08a957dee3b510441"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix tightly packed in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga2c139854e5b04cf08a957dee3b510441"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga379c1153f1339bdeaefd592bebf538e8"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga379c1153f1339bdeaefd592bebf538e8"></a>
typedef packed_highp_mat4x2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga379c1153f1339bdeaefd592bebf538e8">packed_mat4x2</a></td></tr>
<tr class="memdesc:ga379c1153f1339bdeaefd592bebf538e8"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 2 matrix tightly packed in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga379c1153f1339bdeaefd592bebf538e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab286466e19f7399c8d25089da9400d43"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gab286466e19f7399c8d25089da9400d43"></a>
typedef packed_highp_mat4x3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gab286466e19f7399c8d25089da9400d43">packed_mat4x3</a></td></tr>
<tr class="memdesc:gab286466e19f7399c8d25089da9400d43"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 3 matrix tightly packed in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gab286466e19f7399c8d25089da9400d43"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga67e7102557d6067bb6ac00d4ad0e1374"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga67e7102557d6067bb6ac00d4ad0e1374"></a>
typedef packed_highp_mat4x4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga67e7102557d6067bb6ac00d4ad0e1374">packed_mat4x4</a></td></tr>
<tr class="memdesc:ga67e7102557d6067bb6ac00d4ad0e1374"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix tightly packed in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga67e7102557d6067bb6ac00d4ad0e1374"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5546d828d63010a8f9cf81161ad0275a"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga5546d828d63010a8f9cf81161ad0275a"></a>
typedef vec&lt; 1, bool, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga5546d828d63010a8f9cf81161ad0275a">packed_mediump_bvec1</a></td></tr>
<tr class="memdesc:ga5546d828d63010a8f9cf81161ad0275a"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector tightly packed in memory of bool values. <br /></td></tr>
<tr class="separator:ga5546d828d63010a8f9cf81161ad0275a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab4c6414a59539e66a242ad4cf4b476b4"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gab4c6414a59539e66a242ad4cf4b476b4"></a>
typedef vec&lt; 2, bool, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gab4c6414a59539e66a242ad4cf4b476b4">packed_mediump_bvec2</a></td></tr>
<tr class="memdesc:gab4c6414a59539e66a242ad4cf4b476b4"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector tightly packed in memory of bool values. <br /></td></tr>
<tr class="separator:gab4c6414a59539e66a242ad4cf4b476b4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga70147763edff3fe96b03a0b98d6339a2"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga70147763edff3fe96b03a0b98d6339a2"></a>
typedef vec&lt; 3, bool, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga70147763edff3fe96b03a0b98d6339a2">packed_mediump_bvec3</a></td></tr>
<tr class="memdesc:ga70147763edff3fe96b03a0b98d6339a2"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector tightly packed in memory of bool values. <br /></td></tr>
<tr class="separator:ga70147763edff3fe96b03a0b98d6339a2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7b1620f259595b9da47a6374fc44588a"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga7b1620f259595b9da47a6374fc44588a"></a>
typedef vec&lt; 4, bool, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga7b1620f259595b9da47a6374fc44588a">packed_mediump_bvec4</a></td></tr>
<tr class="memdesc:ga7b1620f259595b9da47a6374fc44588a"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector tightly packed in memory of bool values. <br /></td></tr>
<tr class="separator:ga7b1620f259595b9da47a6374fc44588a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9d60e32d3fcb51f817046cd881fdbf57"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga9d60e32d3fcb51f817046cd881fdbf57"></a>
typedef mat&lt; 2, 2, double, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga9d60e32d3fcb51f817046cd881fdbf57">packed_mediump_dmat2</a></td></tr>
<tr class="memdesc:ga9d60e32d3fcb51f817046cd881fdbf57"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix tightly packed in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga9d60e32d3fcb51f817046cd881fdbf57"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga39e8bb9b70e5694964e8266a21ba534e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga39e8bb9b70e5694964e8266a21ba534e"></a>
typedef mat&lt; 2, 2, double, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga39e8bb9b70e5694964e8266a21ba534e">packed_mediump_dmat2x2</a></td></tr>
<tr class="memdesc:ga39e8bb9b70e5694964e8266a21ba534e"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix tightly packed in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga39e8bb9b70e5694964e8266a21ba534e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8897c6d9adb4140b1c3b0a07b8f0a430"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga8897c6d9adb4140b1c3b0a07b8f0a430"></a>
typedef mat&lt; 2, 3, double, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga8897c6d9adb4140b1c3b0a07b8f0a430">packed_mediump_dmat2x3</a></td></tr>
<tr class="memdesc:ga8897c6d9adb4140b1c3b0a07b8f0a430"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 3 matrix tightly packed in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga8897c6d9adb4140b1c3b0a07b8f0a430"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaa4126969c765e7faa2ebf6951c22ffb"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaaa4126969c765e7faa2ebf6951c22ffb"></a>
typedef mat&lt; 2, 4, double, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaaa4126969c765e7faa2ebf6951c22ffb">packed_mediump_dmat2x4</a></td></tr>
<tr class="memdesc:gaaa4126969c765e7faa2ebf6951c22ffb"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 4 matrix tightly packed in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaaa4126969c765e7faa2ebf6951c22ffb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf969eb879c76a5f4576e4a1e10095cf6"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaf969eb879c76a5f4576e4a1e10095cf6"></a>
typedef mat&lt; 3, 3, double, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaf969eb879c76a5f4576e4a1e10095cf6">packed_mediump_dmat3</a></td></tr>
<tr class="memdesc:gaf969eb879c76a5f4576e4a1e10095cf6"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix tightly packed in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaf969eb879c76a5f4576e4a1e10095cf6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga86efe91cdaa2864c828a5d6d46356c6a"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga86efe91cdaa2864c828a5d6d46356c6a"></a>
typedef mat&lt; 3, 2, double, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga86efe91cdaa2864c828a5d6d46356c6a">packed_mediump_dmat3x2</a></td></tr>
<tr class="memdesc:ga86efe91cdaa2864c828a5d6d46356c6a"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 2 matrix tightly packed in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga86efe91cdaa2864c828a5d6d46356c6a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf85877d38d8cfbc21d59d939afd72375"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaf85877d38d8cfbc21d59d939afd72375"></a>
typedef mat&lt; 3, 3, double, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaf85877d38d8cfbc21d59d939afd72375">packed_mediump_dmat3x3</a></td></tr>
<tr class="memdesc:gaf85877d38d8cfbc21d59d939afd72375"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix tightly packed in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaf85877d38d8cfbc21d59d939afd72375"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad5dcaf93df267bc3029174e430e0907f"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gad5dcaf93df267bc3029174e430e0907f"></a>
typedef mat&lt; 3, 4, double, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gad5dcaf93df267bc3029174e430e0907f">packed_mediump_dmat3x4</a></td></tr>
<tr class="memdesc:gad5dcaf93df267bc3029174e430e0907f"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 4 matrix tightly packed in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gad5dcaf93df267bc3029174e430e0907f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4b0ee7996651ddd04eaa0c4cdbb66332"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga4b0ee7996651ddd04eaa0c4cdbb66332"></a>
typedef mat&lt; 4, 4, double, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga4b0ee7996651ddd04eaa0c4cdbb66332">packed_mediump_dmat4</a></td></tr>
<tr class="memdesc:ga4b0ee7996651ddd04eaa0c4cdbb66332"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix tightly packed in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga4b0ee7996651ddd04eaa0c4cdbb66332"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9a15514a0631f700de6312b9d5db3a73"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga9a15514a0631f700de6312b9d5db3a73"></a>
typedef mat&lt; 4, 2, double, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga9a15514a0631f700de6312b9d5db3a73">packed_mediump_dmat4x2</a></td></tr>
<tr class="memdesc:ga9a15514a0631f700de6312b9d5db3a73"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 2 matrix tightly packed in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga9a15514a0631f700de6312b9d5db3a73"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab5b36cc9caee1bb1c5178fe191bf5713"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gab5b36cc9caee1bb1c5178fe191bf5713"></a>
typedef mat&lt; 4, 3, double, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gab5b36cc9caee1bb1c5178fe191bf5713">packed_mediump_dmat4x3</a></td></tr>
<tr class="memdesc:gab5b36cc9caee1bb1c5178fe191bf5713"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 3 matrix tightly packed in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gab5b36cc9caee1bb1c5178fe191bf5713"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga21e86cf2f6c126bacf31b8985db06bd4"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga21e86cf2f6c126bacf31b8985db06bd4"></a>
typedef mat&lt; 4, 4, double, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga21e86cf2f6c126bacf31b8985db06bd4">packed_mediump_dmat4x4</a></td></tr>
<tr class="memdesc:ga21e86cf2f6c126bacf31b8985db06bd4"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix tightly packed in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga21e86cf2f6c126bacf31b8985db06bd4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8920e90ea9c01d9c97e604a938ce2cbd"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga8920e90ea9c01d9c97e604a938ce2cbd"></a>
typedef vec&lt; 1, double, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga8920e90ea9c01d9c97e604a938ce2cbd">packed_mediump_dvec1</a></td></tr>
<tr class="memdesc:ga8920e90ea9c01d9c97e604a938ce2cbd"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector tightly packed in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga8920e90ea9c01d9c97e604a938ce2cbd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0c754a783b6fcf80374c013371c4dae9"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga0c754a783b6fcf80374c013371c4dae9"></a>
typedef vec&lt; 2, double, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga0c754a783b6fcf80374c013371c4dae9">packed_mediump_dvec2</a></td></tr>
<tr class="memdesc:ga0c754a783b6fcf80374c013371c4dae9"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector tightly packed in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga0c754a783b6fcf80374c013371c4dae9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1f18ada6f7cdd8c46db33ba987280fc4"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga1f18ada6f7cdd8c46db33ba987280fc4"></a>
typedef vec&lt; 3, double, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga1f18ada6f7cdd8c46db33ba987280fc4">packed_mediump_dvec3</a></td></tr>
<tr class="memdesc:ga1f18ada6f7cdd8c46db33ba987280fc4"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector tightly packed in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga1f18ada6f7cdd8c46db33ba987280fc4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga568b850f1116b667043533cf77826968"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga568b850f1116b667043533cf77826968"></a>
typedef vec&lt; 4, double, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga568b850f1116b667043533cf77826968">packed_mediump_dvec4</a></td></tr>
<tr class="memdesc:ga568b850f1116b667043533cf77826968"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector tightly packed in memory of double-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga568b850f1116b667043533cf77826968"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga09507ef020a49517a7bcd50438f05056"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga09507ef020a49517a7bcd50438f05056"></a>
typedef vec&lt; 1, int, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga09507ef020a49517a7bcd50438f05056">packed_mediump_ivec1</a></td></tr>
<tr class="memdesc:ga09507ef020a49517a7bcd50438f05056"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector tightly packed in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:ga09507ef020a49517a7bcd50438f05056"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaa891048dddef4627df33809ec726219"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaaa891048dddef4627df33809ec726219"></a>
typedef vec&lt; 2, int, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaaa891048dddef4627df33809ec726219">packed_mediump_ivec2</a></td></tr>
<tr class="memdesc:gaaa891048dddef4627df33809ec726219"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector tightly packed in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:gaaa891048dddef4627df33809ec726219"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga06f26d54dca30994eb1fdadb8e69f4a2"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga06f26d54dca30994eb1fdadb8e69f4a2"></a>
typedef vec&lt; 3, int, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga06f26d54dca30994eb1fdadb8e69f4a2">packed_mediump_ivec3</a></td></tr>
<tr class="memdesc:ga06f26d54dca30994eb1fdadb8e69f4a2"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector tightly packed in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:ga06f26d54dca30994eb1fdadb8e69f4a2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga70130dc8ed9c966ec2a221ce586d45d8"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga70130dc8ed9c966ec2a221ce586d45d8"></a>
typedef vec&lt; 4, int, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga70130dc8ed9c966ec2a221ce586d45d8">packed_mediump_ivec4</a></td></tr>
<tr class="memdesc:ga70130dc8ed9c966ec2a221ce586d45d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector tightly packed in memory of signed integer numbers. <br /></td></tr>
<tr class="separator:ga70130dc8ed9c966ec2a221ce586d45d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga43cd36d430c5187bfdca34a23cb41581"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga43cd36d430c5187bfdca34a23cb41581"></a>
typedef mat&lt; 2, 2, float, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga43cd36d430c5187bfdca34a23cb41581">packed_mediump_mat2</a></td></tr>
<tr class="memdesc:ga43cd36d430c5187bfdca34a23cb41581"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix tightly packed in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga43cd36d430c5187bfdca34a23cb41581"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2d2a73e662759e301c22b8931ff6a526"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga2d2a73e662759e301c22b8931ff6a526"></a>
typedef mat&lt; 2, 2, float, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga2d2a73e662759e301c22b8931ff6a526">packed_mediump_mat2x2</a></td></tr>
<tr class="memdesc:ga2d2a73e662759e301c22b8931ff6a526"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 2 matrix tightly packed in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga2d2a73e662759e301c22b8931ff6a526"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga99049db01faf1e95ed9fb875a47dffe2"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga99049db01faf1e95ed9fb875a47dffe2"></a>
typedef mat&lt; 2, 3, float, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga99049db01faf1e95ed9fb875a47dffe2">packed_mediump_mat2x3</a></td></tr>
<tr class="memdesc:ga99049db01faf1e95ed9fb875a47dffe2"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 3 matrix tightly packed in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga99049db01faf1e95ed9fb875a47dffe2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad43a240533f388ce0504b495d9df3d52"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gad43a240533f388ce0504b495d9df3d52"></a>
typedef mat&lt; 2, 4, float, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gad43a240533f388ce0504b495d9df3d52">packed_mediump_mat2x4</a></td></tr>
<tr class="memdesc:gad43a240533f388ce0504b495d9df3d52"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 by 4 matrix tightly packed in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gad43a240533f388ce0504b495d9df3d52"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga13a75c6cbd0a411f694bc82486cd1e55"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga13a75c6cbd0a411f694bc82486cd1e55"></a>
typedef mat&lt; 3, 3, float, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga13a75c6cbd0a411f694bc82486cd1e55">packed_mediump_mat3</a></td></tr>
<tr class="memdesc:ga13a75c6cbd0a411f694bc82486cd1e55"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix tightly packed in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga13a75c6cbd0a411f694bc82486cd1e55"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga04cfaf1421284df3c24ea0985dab24e7"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga04cfaf1421284df3c24ea0985dab24e7"></a>
typedef mat&lt; 3, 2, float, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga04cfaf1421284df3c24ea0985dab24e7">packed_mediump_mat3x2</a></td></tr>
<tr class="memdesc:ga04cfaf1421284df3c24ea0985dab24e7"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 2 matrix tightly packed in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga04cfaf1421284df3c24ea0985dab24e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaa9cea174d342dd9650e3436823cab23"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaaa9cea174d342dd9650e3436823cab23"></a>
typedef mat&lt; 3, 3, float, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaaa9cea174d342dd9650e3436823cab23">packed_mediump_mat3x3</a></td></tr>
<tr class="memdesc:gaaa9cea174d342dd9650e3436823cab23"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 3 matrix tightly packed in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaaa9cea174d342dd9650e3436823cab23"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabc93a9560593bd32e099c908531305f5"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gabc93a9560593bd32e099c908531305f5"></a>
typedef mat&lt; 3, 4, float, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gabc93a9560593bd32e099c908531305f5">packed_mediump_mat3x4</a></td></tr>
<tr class="memdesc:gabc93a9560593bd32e099c908531305f5"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 by 4 matrix tightly packed in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gabc93a9560593bd32e099c908531305f5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae89d72ffc149147f61df701bbc8755bf"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gae89d72ffc149147f61df701bbc8755bf"></a>
typedef mat&lt; 4, 4, float, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gae89d72ffc149147f61df701bbc8755bf">packed_mediump_mat4</a></td></tr>
<tr class="memdesc:gae89d72ffc149147f61df701bbc8755bf"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix tightly packed in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gae89d72ffc149147f61df701bbc8755bf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa458f9d9e0934bae3097e2a373b24707"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaa458f9d9e0934bae3097e2a373b24707"></a>
typedef mat&lt; 4, 2, float, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaa458f9d9e0934bae3097e2a373b24707">packed_mediump_mat4x2</a></td></tr>
<tr class="memdesc:gaa458f9d9e0934bae3097e2a373b24707"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 2 matrix tightly packed in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaa458f9d9e0934bae3097e2a373b24707"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga02ca6255394aa778abaeb0f733c4d2b6"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga02ca6255394aa778abaeb0f733c4d2b6"></a>
typedef mat&lt; 4, 3, float, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga02ca6255394aa778abaeb0f733c4d2b6">packed_mediump_mat4x3</a></td></tr>
<tr class="memdesc:ga02ca6255394aa778abaeb0f733c4d2b6"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 3 matrix tightly packed in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga02ca6255394aa778abaeb0f733c4d2b6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf304f64c06743c1571401504d3f50259"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaf304f64c06743c1571401504d3f50259"></a>
typedef mat&lt; 4, 4, float, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaf304f64c06743c1571401504d3f50259">packed_mediump_mat4x4</a></td></tr>
<tr class="memdesc:gaf304f64c06743c1571401504d3f50259"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 by 4 matrix tightly packed in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaf304f64c06743c1571401504d3f50259"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2c29fb42bab9a4f9b66bc60b2e514a34"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga2c29fb42bab9a4f9b66bc60b2e514a34"></a>
typedef vec&lt; 1, uint, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga2c29fb42bab9a4f9b66bc60b2e514a34">packed_mediump_uvec1</a></td></tr>
<tr class="memdesc:ga2c29fb42bab9a4f9b66bc60b2e514a34"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector tightly packed in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:ga2c29fb42bab9a4f9b66bc60b2e514a34"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa1f95690a78dc12e39da32943243aeef"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaa1f95690a78dc12e39da32943243aeef"></a>
typedef vec&lt; 2, uint, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaa1f95690a78dc12e39da32943243aeef">packed_mediump_uvec2</a></td></tr>
<tr class="memdesc:gaa1f95690a78dc12e39da32943243aeef"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector tightly packed in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:gaa1f95690a78dc12e39da32943243aeef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1ea2bbdbcb0a69242f6d884663c1b0ab"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga1ea2bbdbcb0a69242f6d884663c1b0ab"></a>
typedef vec&lt; 3, uint, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga1ea2bbdbcb0a69242f6d884663c1b0ab">packed_mediump_uvec3</a></td></tr>
<tr class="memdesc:ga1ea2bbdbcb0a69242f6d884663c1b0ab"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector tightly packed in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:ga1ea2bbdbcb0a69242f6d884663c1b0ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga63a73be86a4f07ea7a7499ab0bfebe45"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga63a73be86a4f07ea7a7499ab0bfebe45"></a>
typedef vec&lt; 4, uint, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga63a73be86a4f07ea7a7499ab0bfebe45">packed_mediump_uvec4</a></td></tr>
<tr class="memdesc:ga63a73be86a4f07ea7a7499ab0bfebe45"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector tightly packed in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:ga63a73be86a4f07ea7a7499ab0bfebe45"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga71d63cead1e113fca0bcdaaa33aad050"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga71d63cead1e113fca0bcdaaa33aad050"></a>
typedef vec&lt; 1, float, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga71d63cead1e113fca0bcdaaa33aad050">packed_mediump_vec1</a></td></tr>
<tr class="memdesc:ga71d63cead1e113fca0bcdaaa33aad050"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector tightly packed in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga71d63cead1e113fca0bcdaaa33aad050"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6844c6f4691d1bf67673240850430948"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga6844c6f4691d1bf67673240850430948"></a>
typedef vec&lt; 2, float, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga6844c6f4691d1bf67673240850430948">packed_mediump_vec2</a></td></tr>
<tr class="memdesc:ga6844c6f4691d1bf67673240850430948"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector tightly packed in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga6844c6f4691d1bf67673240850430948"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab0eb771b708c5b2205d9b14dd1434fd8"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gab0eb771b708c5b2205d9b14dd1434fd8"></a>
typedef vec&lt; 3, float, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gab0eb771b708c5b2205d9b14dd1434fd8">packed_mediump_vec3</a></td></tr>
<tr class="memdesc:gab0eb771b708c5b2205d9b14dd1434fd8"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector tightly packed in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gab0eb771b708c5b2205d9b14dd1434fd8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga68c9bb24f387b312bae6a0a68e74d95e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga68c9bb24f387b312bae6a0a68e74d95e"></a>
typedef vec&lt; 4, float, packed_mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga68c9bb24f387b312bae6a0a68e74d95e">packed_mediump_vec4</a></td></tr>
<tr class="memdesc:ga68c9bb24f387b312bae6a0a68e74d95e"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector tightly packed in memory of single-precision floating-point numbers using medium precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:ga68c9bb24f387b312bae6a0a68e74d95e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5621493caac01bdd22ab6be4416b0314"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga5621493caac01bdd22ab6be4416b0314"></a>
typedef packed_highp_uvec1&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga5621493caac01bdd22ab6be4416b0314">packed_uvec1</a></td></tr>
<tr class="memdesc:ga5621493caac01bdd22ab6be4416b0314"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector tightly packed in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:ga5621493caac01bdd22ab6be4416b0314"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabcc33efb4d5e83b8fe4706360e75b932"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gabcc33efb4d5e83b8fe4706360e75b932"></a>
typedef packed_highp_uvec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gabcc33efb4d5e83b8fe4706360e75b932">packed_uvec2</a></td></tr>
<tr class="memdesc:gabcc33efb4d5e83b8fe4706360e75b932"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector tightly packed in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:gabcc33efb4d5e83b8fe4706360e75b932"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab96804e99e3a72a35740fec690c79617"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gab96804e99e3a72a35740fec690c79617"></a>
typedef packed_highp_uvec3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gab96804e99e3a72a35740fec690c79617">packed_uvec3</a></td></tr>
<tr class="memdesc:gab96804e99e3a72a35740fec690c79617"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector tightly packed in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:gab96804e99e3a72a35740fec690c79617"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8e5d92e84ebdbe2480cf96bc17d6e2f2"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga8e5d92e84ebdbe2480cf96bc17d6e2f2"></a>
typedef packed_highp_uvec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga8e5d92e84ebdbe2480cf96bc17d6e2f2">packed_uvec4</a></td></tr>
<tr class="memdesc:ga8e5d92e84ebdbe2480cf96bc17d6e2f2"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector tightly packed in memory of unsigned integer numbers. <br /></td></tr>
<tr class="separator:ga8e5d92e84ebdbe2480cf96bc17d6e2f2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga14741e3d9da9ae83765389927f837331"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga14741e3d9da9ae83765389927f837331"></a>
typedef packed_highp_vec1&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga14741e3d9da9ae83765389927f837331">packed_vec1</a></td></tr>
<tr class="memdesc:ga14741e3d9da9ae83765389927f837331"><td class="mdescLeft">&#160;</td><td class="mdescRight">1 component vector tightly packed in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga14741e3d9da9ae83765389927f837331"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3254defa5a8f0ae4b02b45fedba84a66"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga3254defa5a8f0ae4b02b45fedba84a66"></a>
typedef packed_highp_vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga3254defa5a8f0ae4b02b45fedba84a66">packed_vec2</a></td></tr>
<tr class="memdesc:ga3254defa5a8f0ae4b02b45fedba84a66"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector tightly packed in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga3254defa5a8f0ae4b02b45fedba84a66"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaccccd090e185450caa28b5b63ad4e8f0"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaccccd090e185450caa28b5b63ad4e8f0"></a>
typedef packed_highp_vec3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#gaccccd090e185450caa28b5b63ad4e8f0">packed_vec3</a></td></tr>
<tr class="memdesc:gaccccd090e185450caa28b5b63ad4e8f0"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector tightly packed in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:gaccccd090e185450caa28b5b63ad4e8f0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga37a0e0bf653169b581c5eea3d547fa5d"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga37a0e0bf653169b581c5eea3d547fa5d"></a>
typedef packed_highp_vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html#ga37a0e0bf653169b581c5eea3d547fa5d">packed_vec4</a></td></tr>
<tr class="memdesc:ga37a0e0bf653169b581c5eea3d547fa5d"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector tightly packed in memory of single-precision floating-point numbers. <br /></td></tr>
<tr class="separator:ga37a0e0bf653169b581c5eea3d547fa5d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00161.html" title="GLM_GTC_type_aligned ">glm/gtc/type_aligned.hpp</a>&gt; to use the features of this extension. </p>
<p>Aligned types allowing SIMD optimizations of vectors and matrices types </p>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
