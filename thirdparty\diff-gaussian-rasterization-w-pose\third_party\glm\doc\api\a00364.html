<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_type_aligned</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_type_aligned<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00162.html" title="GLM_GTX_type_aligned ">glm/gtx/type_aligned.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gab5cd5c5fad228b25c782084f1cc30114"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gab5cd5c5fad228b25c782084f1cc30114">GLM_ALIGNED_TYPEDEF</a> (lowp_int8, aligned_lowp_int8, 1)</td></tr>
<tr class="memdesc:gab5cd5c5fad228b25c782084f1cc30114"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#gab5cd5c5fad228b25c782084f1cc30114">More...</a><br /></td></tr>
<tr class="separator:gab5cd5c5fad228b25c782084f1cc30114"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5bb5dd895ef625c1b113f2cf400186b0"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga5bb5dd895ef625c1b113f2cf400186b0">GLM_ALIGNED_TYPEDEF</a> (lowp_int16, aligned_lowp_int16, 2)</td></tr>
<tr class="memdesc:ga5bb5dd895ef625c1b113f2cf400186b0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#ga5bb5dd895ef625c1b113f2cf400186b0">More...</a><br /></td></tr>
<tr class="separator:ga5bb5dd895ef625c1b113f2cf400186b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac6efa54cf7c6c86f7158922abdb1a430"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gac6efa54cf7c6c86f7158922abdb1a430">GLM_ALIGNED_TYPEDEF</a> (lowp_int32, aligned_lowp_int32, 4)</td></tr>
<tr class="memdesc:gac6efa54cf7c6c86f7158922abdb1a430"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#gac6efa54cf7c6c86f7158922abdb1a430">More...</a><br /></td></tr>
<tr class="separator:gac6efa54cf7c6c86f7158922abdb1a430"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6612eb77c8607048e7552279a11eeb5f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga6612eb77c8607048e7552279a11eeb5f">GLM_ALIGNED_TYPEDEF</a> (lowp_int64, aligned_lowp_int64, 8)</td></tr>
<tr class="memdesc:ga6612eb77c8607048e7552279a11eeb5f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga6612eb77c8607048e7552279a11eeb5f">More...</a><br /></td></tr>
<tr class="separator:ga6612eb77c8607048e7552279a11eeb5f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7ddc1848ff2223026db8968ce0c97497"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga7ddc1848ff2223026db8968ce0c97497">GLM_ALIGNED_TYPEDEF</a> (lowp_int8_t, aligned_lowp_int8_t, 1)</td></tr>
<tr class="memdesc:ga7ddc1848ff2223026db8968ce0c97497"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#ga7ddc1848ff2223026db8968ce0c97497">More...</a><br /></td></tr>
<tr class="separator:ga7ddc1848ff2223026db8968ce0c97497"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga22240dd9458b0f8c11fbcc4f48714f68"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga22240dd9458b0f8c11fbcc4f48714f68">GLM_ALIGNED_TYPEDEF</a> (lowp_int16_t, aligned_lowp_int16_t, 2)</td></tr>
<tr class="memdesc:ga22240dd9458b0f8c11fbcc4f48714f68"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#ga22240dd9458b0f8c11fbcc4f48714f68">More...</a><br /></td></tr>
<tr class="separator:ga22240dd9458b0f8c11fbcc4f48714f68"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8130ea381d76a2cc34a93ccbb6cf487d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga8130ea381d76a2cc34a93ccbb6cf487d">GLM_ALIGNED_TYPEDEF</a> (lowp_int32_t, aligned_lowp_int32_t, 4)</td></tr>
<tr class="memdesc:ga8130ea381d76a2cc34a93ccbb6cf487d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#ga8130ea381d76a2cc34a93ccbb6cf487d">More...</a><br /></td></tr>
<tr class="separator:ga8130ea381d76a2cc34a93ccbb6cf487d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7ccb60f3215d293fd62b33b31ed0e7be"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga7ccb60f3215d293fd62b33b31ed0e7be">GLM_ALIGNED_TYPEDEF</a> (lowp_int64_t, aligned_lowp_int64_t, 8)</td></tr>
<tr class="memdesc:ga7ccb60f3215d293fd62b33b31ed0e7be"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga7ccb60f3215d293fd62b33b31ed0e7be">More...</a><br /></td></tr>
<tr class="separator:ga7ccb60f3215d293fd62b33b31ed0e7be"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac20d508d2ef5cc95ad3daf083c57ec2a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gac20d508d2ef5cc95ad3daf083c57ec2a">GLM_ALIGNED_TYPEDEF</a> (lowp_i8, aligned_lowp_i8, 1)</td></tr>
<tr class="memdesc:gac20d508d2ef5cc95ad3daf083c57ec2a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#gac20d508d2ef5cc95ad3daf083c57ec2a">More...</a><br /></td></tr>
<tr class="separator:gac20d508d2ef5cc95ad3daf083c57ec2a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga50257b48069a31d0c8d9c1f644d267de"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga50257b48069a31d0c8d9c1f644d267de">GLM_ALIGNED_TYPEDEF</a> (lowp_i16, aligned_lowp_i16, 2)</td></tr>
<tr class="memdesc:ga50257b48069a31d0c8d9c1f644d267de"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#ga50257b48069a31d0c8d9c1f644d267de">More...</a><br /></td></tr>
<tr class="separator:ga50257b48069a31d0c8d9c1f644d267de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa07e98e67b7a3435c0746018c7a2a839"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaa07e98e67b7a3435c0746018c7a2a839">GLM_ALIGNED_TYPEDEF</a> (lowp_i32, aligned_lowp_i32, 4)</td></tr>
<tr class="memdesc:gaa07e98e67b7a3435c0746018c7a2a839"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#gaa07e98e67b7a3435c0746018c7a2a839">More...</a><br /></td></tr>
<tr class="separator:gaa07e98e67b7a3435c0746018c7a2a839"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga62601fc6f8ca298b77285bedf03faffd"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga62601fc6f8ca298b77285bedf03faffd">GLM_ALIGNED_TYPEDEF</a> (lowp_i64, aligned_lowp_i64, 8)</td></tr>
<tr class="memdesc:ga62601fc6f8ca298b77285bedf03faffd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga62601fc6f8ca298b77285bedf03faffd">More...</a><br /></td></tr>
<tr class="separator:ga62601fc6f8ca298b77285bedf03faffd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac8cff825951aeb54dd846037113c72db"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gac8cff825951aeb54dd846037113c72db">GLM_ALIGNED_TYPEDEF</a> (mediump_int8, aligned_mediump_int8, 1)</td></tr>
<tr class="memdesc:gac8cff825951aeb54dd846037113c72db"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#gac8cff825951aeb54dd846037113c72db">More...</a><br /></td></tr>
<tr class="separator:gac8cff825951aeb54dd846037113c72db"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga78f443d88f438575a62b5df497cdf66b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga78f443d88f438575a62b5df497cdf66b">GLM_ALIGNED_TYPEDEF</a> (mediump_int16, aligned_mediump_int16, 2)</td></tr>
<tr class="memdesc:ga78f443d88f438575a62b5df497cdf66b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#ga78f443d88f438575a62b5df497cdf66b">More...</a><br /></td></tr>
<tr class="separator:ga78f443d88f438575a62b5df497cdf66b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0680cd3b5d4e8006985fb41a4f9b57af"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga0680cd3b5d4e8006985fb41a4f9b57af">GLM_ALIGNED_TYPEDEF</a> (mediump_int32, aligned_mediump_int32, 4)</td></tr>
<tr class="memdesc:ga0680cd3b5d4e8006985fb41a4f9b57af"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#ga0680cd3b5d4e8006985fb41a4f9b57af">More...</a><br /></td></tr>
<tr class="separator:ga0680cd3b5d4e8006985fb41a4f9b57af"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad9e5babb1dd3e3531b42c37bf25dd951"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gad9e5babb1dd3e3531b42c37bf25dd951">GLM_ALIGNED_TYPEDEF</a> (mediump_int64, aligned_mediump_int64, 8)</td></tr>
<tr class="memdesc:gad9e5babb1dd3e3531b42c37bf25dd951"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#gad9e5babb1dd3e3531b42c37bf25dd951">More...</a><br /></td></tr>
<tr class="separator:gad9e5babb1dd3e3531b42c37bf25dd951"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga353fd9fa8a9ad952fcabd0d53ad9a6dd"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga353fd9fa8a9ad952fcabd0d53ad9a6dd">GLM_ALIGNED_TYPEDEF</a> (mediump_int8_t, aligned_mediump_int8_t, 1)</td></tr>
<tr class="memdesc:ga353fd9fa8a9ad952fcabd0d53ad9a6dd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#ga353fd9fa8a9ad952fcabd0d53ad9a6dd">More...</a><br /></td></tr>
<tr class="separator:ga353fd9fa8a9ad952fcabd0d53ad9a6dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2196442c0e5c5e8c77842de388c42521"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga2196442c0e5c5e8c77842de388c42521">GLM_ALIGNED_TYPEDEF</a> (mediump_int16_t, aligned_mediump_int16_t, 2)</td></tr>
<tr class="memdesc:ga2196442c0e5c5e8c77842de388c42521"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#ga2196442c0e5c5e8c77842de388c42521">More...</a><br /></td></tr>
<tr class="separator:ga2196442c0e5c5e8c77842de388c42521"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1284488189daf897cf095c5eefad9744"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga1284488189daf897cf095c5eefad9744">GLM_ALIGNED_TYPEDEF</a> (mediump_int32_t, aligned_mediump_int32_t, 4)</td></tr>
<tr class="memdesc:ga1284488189daf897cf095c5eefad9744"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#ga1284488189daf897cf095c5eefad9744">More...</a><br /></td></tr>
<tr class="separator:ga1284488189daf897cf095c5eefad9744"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga73fdc86a539808af58808b7c60a1c4d8"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga73fdc86a539808af58808b7c60a1c4d8">GLM_ALIGNED_TYPEDEF</a> (mediump_int64_t, aligned_mediump_int64_t, 8)</td></tr>
<tr class="memdesc:ga73fdc86a539808af58808b7c60a1c4d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga73fdc86a539808af58808b7c60a1c4d8">More...</a><br /></td></tr>
<tr class="separator:ga73fdc86a539808af58808b7c60a1c4d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafafeea923e1983262c972e2b83922d3b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gafafeea923e1983262c972e2b83922d3b">GLM_ALIGNED_TYPEDEF</a> (mediump_i8, aligned_mediump_i8, 1)</td></tr>
<tr class="memdesc:gafafeea923e1983262c972e2b83922d3b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#gafafeea923e1983262c972e2b83922d3b">More...</a><br /></td></tr>
<tr class="separator:gafafeea923e1983262c972e2b83922d3b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4b35ca5fe8f55c9d2fe54fdb8d8896f4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga4b35ca5fe8f55c9d2fe54fdb8d8896f4">GLM_ALIGNED_TYPEDEF</a> (mediump_i16, aligned_mediump_i16, 2)</td></tr>
<tr class="memdesc:ga4b35ca5fe8f55c9d2fe54fdb8d8896f4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#ga4b35ca5fe8f55c9d2fe54fdb8d8896f4">More...</a><br /></td></tr>
<tr class="separator:ga4b35ca5fe8f55c9d2fe54fdb8d8896f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga63b882e29170d428463d99c3d630acc6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga63b882e29170d428463d99c3d630acc6">GLM_ALIGNED_TYPEDEF</a> (mediump_i32, aligned_mediump_i32, 4)</td></tr>
<tr class="memdesc:ga63b882e29170d428463d99c3d630acc6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#ga63b882e29170d428463d99c3d630acc6">More...</a><br /></td></tr>
<tr class="separator:ga63b882e29170d428463d99c3d630acc6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8b20507bb048c1edea2d441cc953e6f0"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga8b20507bb048c1edea2d441cc953e6f0">GLM_ALIGNED_TYPEDEF</a> (mediump_i64, aligned_mediump_i64, 8)</td></tr>
<tr class="memdesc:ga8b20507bb048c1edea2d441cc953e6f0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga8b20507bb048c1edea2d441cc953e6f0">More...</a><br /></td></tr>
<tr class="separator:ga8b20507bb048c1edea2d441cc953e6f0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga56c5ca60813027b603c7b61425a0479d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga56c5ca60813027b603c7b61425a0479d">GLM_ALIGNED_TYPEDEF</a> (highp_int8, aligned_highp_int8, 1)</td></tr>
<tr class="memdesc:ga56c5ca60813027b603c7b61425a0479d"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#ga56c5ca60813027b603c7b61425a0479d">More...</a><br /></td></tr>
<tr class="separator:ga56c5ca60813027b603c7b61425a0479d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7a751b3aff24c0259f4a7357c2969089"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga7a751b3aff24c0259f4a7357c2969089">GLM_ALIGNED_TYPEDEF</a> (highp_int16, aligned_highp_int16, 2)</td></tr>
<tr class="memdesc:ga7a751b3aff24c0259f4a7357c2969089"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#ga7a751b3aff24c0259f4a7357c2969089">More...</a><br /></td></tr>
<tr class="separator:ga7a751b3aff24c0259f4a7357c2969089"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga70cd2144351c556469ee6119e59971fc"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga70cd2144351c556469ee6119e59971fc">GLM_ALIGNED_TYPEDEF</a> (highp_int32, aligned_highp_int32, 4)</td></tr>
<tr class="memdesc:ga70cd2144351c556469ee6119e59971fc"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#ga70cd2144351c556469ee6119e59971fc">More...</a><br /></td></tr>
<tr class="separator:ga70cd2144351c556469ee6119e59971fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga46bbf08dc004d8c433041e0b5018a5d3"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga46bbf08dc004d8c433041e0b5018a5d3">GLM_ALIGNED_TYPEDEF</a> (highp_int64, aligned_highp_int64, 8)</td></tr>
<tr class="memdesc:ga46bbf08dc004d8c433041e0b5018a5d3"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga46bbf08dc004d8c433041e0b5018a5d3">More...</a><br /></td></tr>
<tr class="separator:ga46bbf08dc004d8c433041e0b5018a5d3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab3e10c77a20d1abad2de1c561c7a5c18"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gab3e10c77a20d1abad2de1c561c7a5c18">GLM_ALIGNED_TYPEDEF</a> (highp_int8_t, aligned_highp_int8_t, 1)</td></tr>
<tr class="memdesc:gab3e10c77a20d1abad2de1c561c7a5c18"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#gab3e10c77a20d1abad2de1c561c7a5c18">More...</a><br /></td></tr>
<tr class="separator:gab3e10c77a20d1abad2de1c561c7a5c18"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga968f30319ebeaca9ebcd3a25a8e139fb"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga968f30319ebeaca9ebcd3a25a8e139fb">GLM_ALIGNED_TYPEDEF</a> (highp_int16_t, aligned_highp_int16_t, 2)</td></tr>
<tr class="memdesc:ga968f30319ebeaca9ebcd3a25a8e139fb"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#ga968f30319ebeaca9ebcd3a25a8e139fb">More...</a><br /></td></tr>
<tr class="separator:ga968f30319ebeaca9ebcd3a25a8e139fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaae773c28e6390c6aa76f5b678b7098a3"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaae773c28e6390c6aa76f5b678b7098a3">GLM_ALIGNED_TYPEDEF</a> (highp_int32_t, aligned_highp_int32_t, 4)</td></tr>
<tr class="memdesc:gaae773c28e6390c6aa76f5b678b7098a3"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#gaae773c28e6390c6aa76f5b678b7098a3">More...</a><br /></td></tr>
<tr class="separator:gaae773c28e6390c6aa76f5b678b7098a3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga790cfff1ca39d0ed696ffed980809311"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga790cfff1ca39d0ed696ffed980809311">GLM_ALIGNED_TYPEDEF</a> (highp_int64_t, aligned_highp_int64_t, 8)</td></tr>
<tr class="memdesc:ga790cfff1ca39d0ed696ffed980809311"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga790cfff1ca39d0ed696ffed980809311">More...</a><br /></td></tr>
<tr class="separator:ga790cfff1ca39d0ed696ffed980809311"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8265b91eb23c120a9b0c3e381bc37b96"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga8265b91eb23c120a9b0c3e381bc37b96">GLM_ALIGNED_TYPEDEF</a> (highp_i8, aligned_highp_i8, 1)</td></tr>
<tr class="memdesc:ga8265b91eb23c120a9b0c3e381bc37b96"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#ga8265b91eb23c120a9b0c3e381bc37b96">More...</a><br /></td></tr>
<tr class="separator:ga8265b91eb23c120a9b0c3e381bc37b96"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae6d384de17588d8edb894fbe06e0d410"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gae6d384de17588d8edb894fbe06e0d410">GLM_ALIGNED_TYPEDEF</a> (highp_i16, aligned_highp_i16, 2)</td></tr>
<tr class="memdesc:gae6d384de17588d8edb894fbe06e0d410"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#gae6d384de17588d8edb894fbe06e0d410">More...</a><br /></td></tr>
<tr class="separator:gae6d384de17588d8edb894fbe06e0d410"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9c8172b745ee03fc5b2b91c350c2922f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga9c8172b745ee03fc5b2b91c350c2922f">GLM_ALIGNED_TYPEDEF</a> (highp_i32, aligned_highp_i32, 4)</td></tr>
<tr class="memdesc:ga9c8172b745ee03fc5b2b91c350c2922f"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#ga9c8172b745ee03fc5b2b91c350c2922f">More...</a><br /></td></tr>
<tr class="separator:ga9c8172b745ee03fc5b2b91c350c2922f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga77e0dff12aa4020ddc3f8cabbea7b2e6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga77e0dff12aa4020ddc3f8cabbea7b2e6">GLM_ALIGNED_TYPEDEF</a> (highp_i64, aligned_highp_i64, 8)</td></tr>
<tr class="memdesc:ga77e0dff12aa4020ddc3f8cabbea7b2e6"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga77e0dff12aa4020ddc3f8cabbea7b2e6">More...</a><br /></td></tr>
<tr class="separator:ga77e0dff12aa4020ddc3f8cabbea7b2e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabd82b9faa9d4d618dbbe0fc8a1efee63"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gabd82b9faa9d4d618dbbe0fc8a1efee63">GLM_ALIGNED_TYPEDEF</a> (int8, aligned_int8, 1)</td></tr>
<tr class="memdesc:gabd82b9faa9d4d618dbbe0fc8a1efee63"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#gabd82b9faa9d4d618dbbe0fc8a1efee63">More...</a><br /></td></tr>
<tr class="separator:gabd82b9faa9d4d618dbbe0fc8a1efee63"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga285649744560be21000cfd81bbb5d507"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga285649744560be21000cfd81bbb5d507">GLM_ALIGNED_TYPEDEF</a> (int16, aligned_int16, 2)</td></tr>
<tr class="memdesc:ga285649744560be21000cfd81bbb5d507"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#ga285649744560be21000cfd81bbb5d507">More...</a><br /></td></tr>
<tr class="separator:ga285649744560be21000cfd81bbb5d507"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga07732da630b2deda428ce95c0ecaf3ff"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga07732da630b2deda428ce95c0ecaf3ff">GLM_ALIGNED_TYPEDEF</a> (int32, aligned_int32, 4)</td></tr>
<tr class="memdesc:ga07732da630b2deda428ce95c0ecaf3ff"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#ga07732da630b2deda428ce95c0ecaf3ff">More...</a><br /></td></tr>
<tr class="separator:ga07732da630b2deda428ce95c0ecaf3ff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1a8da2a8c51f69c07a2e7f473aa420f4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga1a8da2a8c51f69c07a2e7f473aa420f4">GLM_ALIGNED_TYPEDEF</a> (int64, aligned_int64, 8)</td></tr>
<tr class="memdesc:ga1a8da2a8c51f69c07a2e7f473aa420f4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga1a8da2a8c51f69c07a2e7f473aa420f4">More...</a><br /></td></tr>
<tr class="separator:ga1a8da2a8c51f69c07a2e7f473aa420f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga848aedf13e2d9738acf0bb482c590174"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga848aedf13e2d9738acf0bb482c590174">GLM_ALIGNED_TYPEDEF</a> (int8_t, aligned_int8_t, 1)</td></tr>
<tr class="memdesc:ga848aedf13e2d9738acf0bb482c590174"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#ga848aedf13e2d9738acf0bb482c590174">More...</a><br /></td></tr>
<tr class="separator:ga848aedf13e2d9738acf0bb482c590174"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafd2803d39049dd45a37a63931e25d943"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gafd2803d39049dd45a37a63931e25d943">GLM_ALIGNED_TYPEDEF</a> (int16_t, aligned_int16_t, 2)</td></tr>
<tr class="memdesc:gafd2803d39049dd45a37a63931e25d943"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#gafd2803d39049dd45a37a63931e25d943">More...</a><br /></td></tr>
<tr class="separator:gafd2803d39049dd45a37a63931e25d943"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae553b33349d6da832cf0724f1e024094"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gae553b33349d6da832cf0724f1e024094">GLM_ALIGNED_TYPEDEF</a> (int32_t, aligned_int32_t, 4)</td></tr>
<tr class="memdesc:gae553b33349d6da832cf0724f1e024094"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#gae553b33349d6da832cf0724f1e024094">More...</a><br /></td></tr>
<tr class="separator:gae553b33349d6da832cf0724f1e024094"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga16d223a2b3409e812e1d3bd87f0e9e5c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga16d223a2b3409e812e1d3bd87f0e9e5c">GLM_ALIGNED_TYPEDEF</a> (int64_t, aligned_int64_t, 8)</td></tr>
<tr class="memdesc:ga16d223a2b3409e812e1d3bd87f0e9e5c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga16d223a2b3409e812e1d3bd87f0e9e5c">More...</a><br /></td></tr>
<tr class="separator:ga16d223a2b3409e812e1d3bd87f0e9e5c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2de065d2ddfdb366bcd0febca79ae2ad"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga2de065d2ddfdb366bcd0febca79ae2ad">GLM_ALIGNED_TYPEDEF</a> (i8, aligned_i8, 1)</td></tr>
<tr class="memdesc:ga2de065d2ddfdb366bcd0febca79ae2ad"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#ga2de065d2ddfdb366bcd0febca79ae2ad">More...</a><br /></td></tr>
<tr class="separator:ga2de065d2ddfdb366bcd0febca79ae2ad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabd786bdc20a11c8cb05c92c8212e28d3"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gabd786bdc20a11c8cb05c92c8212e28d3">GLM_ALIGNED_TYPEDEF</a> (i16, aligned_i16, 2)</td></tr>
<tr class="memdesc:gabd786bdc20a11c8cb05c92c8212e28d3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#gabd786bdc20a11c8cb05c92c8212e28d3">More...</a><br /></td></tr>
<tr class="separator:gabd786bdc20a11c8cb05c92c8212e28d3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad4aefe56691cdb640c72f0d46d3fb532"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gad4aefe56691cdb640c72f0d46d3fb532">GLM_ALIGNED_TYPEDEF</a> (i32, aligned_i32, 4)</td></tr>
<tr class="memdesc:gad4aefe56691cdb640c72f0d46d3fb532"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#gad4aefe56691cdb640c72f0d46d3fb532">More...</a><br /></td></tr>
<tr class="separator:gad4aefe56691cdb640c72f0d46d3fb532"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8fe9745f7de24a8394518152ff9fccdc"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga8fe9745f7de24a8394518152ff9fccdc">GLM_ALIGNED_TYPEDEF</a> (i64, aligned_i64, 8)</td></tr>
<tr class="memdesc:ga8fe9745f7de24a8394518152ff9fccdc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga8fe9745f7de24a8394518152ff9fccdc">More...</a><br /></td></tr>
<tr class="separator:ga8fe9745f7de24a8394518152ff9fccdc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaad735483450099f7f882d4e3a3569bd"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaaad735483450099f7f882d4e3a3569bd">GLM_ALIGNED_TYPEDEF</a> (ivec1, aligned_ivec1, 4)</td></tr>
<tr class="memdesc:gaaad735483450099f7f882d4e3a3569bd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#gaaad735483450099f7f882d4e3a3569bd">More...</a><br /></td></tr>
<tr class="separator:gaaad735483450099f7f882d4e3a3569bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac7b6f823802edbd6edbaf70ea25bf068"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gac7b6f823802edbd6edbaf70ea25bf068">GLM_ALIGNED_TYPEDEF</a> (ivec2, aligned_ivec2, 8)</td></tr>
<tr class="memdesc:gac7b6f823802edbd6edbaf70ea25bf068"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit signed integer aligned vector of 2 components type.  <a href="a00364.html#gac7b6f823802edbd6edbaf70ea25bf068">More...</a><br /></td></tr>
<tr class="separator:gac7b6f823802edbd6edbaf70ea25bf068"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3e235bcd2b8029613f25b8d40a2d3ef7"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga3e235bcd2b8029613f25b8d40a2d3ef7">GLM_ALIGNED_TYPEDEF</a> (ivec3, aligned_ivec3, 16)</td></tr>
<tr class="memdesc:ga3e235bcd2b8029613f25b8d40a2d3ef7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit signed integer aligned vector of 3 components type.  <a href="a00364.html#ga3e235bcd2b8029613f25b8d40a2d3ef7">More...</a><br /></td></tr>
<tr class="separator:ga3e235bcd2b8029613f25b8d40a2d3ef7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga50d8a9523968c77f8325b4c9bfbff41e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga50d8a9523968c77f8325b4c9bfbff41e">GLM_ALIGNED_TYPEDEF</a> (ivec4, aligned_ivec4, 16)</td></tr>
<tr class="memdesc:ga50d8a9523968c77f8325b4c9bfbff41e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit signed integer aligned vector of 4 components type.  <a href="a00364.html#ga50d8a9523968c77f8325b4c9bfbff41e">More...</a><br /></td></tr>
<tr class="separator:ga50d8a9523968c77f8325b4c9bfbff41e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9ec20fdfb729c702032da9378c79679f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga9ec20fdfb729c702032da9378c79679f">GLM_ALIGNED_TYPEDEF</a> (i8vec1, aligned_i8vec1, 1)</td></tr>
<tr class="memdesc:ga9ec20fdfb729c702032da9378c79679f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit signed integer aligned scalar type.  <a href="a00364.html#ga9ec20fdfb729c702032da9378c79679f">More...</a><br /></td></tr>
<tr class="separator:ga9ec20fdfb729c702032da9378c79679f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga25b3fe1d9e8d0a5e86c1949c1acd8131"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga25b3fe1d9e8d0a5e86c1949c1acd8131">GLM_ALIGNED_TYPEDEF</a> (i8vec2, aligned_i8vec2, 2)</td></tr>
<tr class="memdesc:ga25b3fe1d9e8d0a5e86c1949c1acd8131"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit signed integer aligned vector of 2 components type.  <a href="a00364.html#ga25b3fe1d9e8d0a5e86c1949c1acd8131">More...</a><br /></td></tr>
<tr class="separator:ga25b3fe1d9e8d0a5e86c1949c1acd8131"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2958f907719d94d8109b562540c910e2"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga2958f907719d94d8109b562540c910e2">GLM_ALIGNED_TYPEDEF</a> (i8vec3, aligned_i8vec3, 4)</td></tr>
<tr class="memdesc:ga2958f907719d94d8109b562540c910e2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit signed integer aligned vector of 3 components type.  <a href="a00364.html#ga2958f907719d94d8109b562540c910e2">More...</a><br /></td></tr>
<tr class="separator:ga2958f907719d94d8109b562540c910e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1fe6fc032a978f1c845fac9aa0668714"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga1fe6fc032a978f1c845fac9aa0668714">GLM_ALIGNED_TYPEDEF</a> (i8vec4, aligned_i8vec4, 4)</td></tr>
<tr class="memdesc:ga1fe6fc032a978f1c845fac9aa0668714"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit signed integer aligned vector of 4 components type.  <a href="a00364.html#ga1fe6fc032a978f1c845fac9aa0668714">More...</a><br /></td></tr>
<tr class="separator:ga1fe6fc032a978f1c845fac9aa0668714"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa4161e7a496dc96972254143fe873e55"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaa4161e7a496dc96972254143fe873e55">GLM_ALIGNED_TYPEDEF</a> (i16vec1, aligned_i16vec1, 2)</td></tr>
<tr class="memdesc:gaa4161e7a496dc96972254143fe873e55"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit signed integer aligned scalar type.  <a href="a00364.html#gaa4161e7a496dc96972254143fe873e55">More...</a><br /></td></tr>
<tr class="separator:gaa4161e7a496dc96972254143fe873e55"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9d7cb211ccda69b1c22ddeeb0f3e7aba"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga9d7cb211ccda69b1c22ddeeb0f3e7aba">GLM_ALIGNED_TYPEDEF</a> (i16vec2, aligned_i16vec2, 4)</td></tr>
<tr class="memdesc:ga9d7cb211ccda69b1c22ddeeb0f3e7aba"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit signed integer aligned vector of 2 components type.  <a href="a00364.html#ga9d7cb211ccda69b1c22ddeeb0f3e7aba">More...</a><br /></td></tr>
<tr class="separator:ga9d7cb211ccda69b1c22ddeeb0f3e7aba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaee91dd2ab34423bcc11072ef6bd0f02"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaaee91dd2ab34423bcc11072ef6bd0f02">GLM_ALIGNED_TYPEDEF</a> (i16vec3, aligned_i16vec3, 8)</td></tr>
<tr class="memdesc:gaaee91dd2ab34423bcc11072ef6bd0f02"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit signed integer aligned vector of 3 components type.  <a href="a00364.html#gaaee91dd2ab34423bcc11072ef6bd0f02">More...</a><br /></td></tr>
<tr class="separator:gaaee91dd2ab34423bcc11072ef6bd0f02"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga49f047ccaa8b31fad9f26c67bf9b3510"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga49f047ccaa8b31fad9f26c67bf9b3510">GLM_ALIGNED_TYPEDEF</a> (i16vec4, aligned_i16vec4, 8)</td></tr>
<tr class="memdesc:ga49f047ccaa8b31fad9f26c67bf9b3510"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit signed integer aligned vector of 4 components type.  <a href="a00364.html#ga49f047ccaa8b31fad9f26c67bf9b3510">More...</a><br /></td></tr>
<tr class="separator:ga49f047ccaa8b31fad9f26c67bf9b3510"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga904e9c2436bb099397c0823506a0771f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga904e9c2436bb099397c0823506a0771f">GLM_ALIGNED_TYPEDEF</a> (i32vec1, aligned_i32vec1, 4)</td></tr>
<tr class="memdesc:ga904e9c2436bb099397c0823506a0771f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit signed integer aligned scalar type.  <a href="a00364.html#ga904e9c2436bb099397c0823506a0771f">More...</a><br /></td></tr>
<tr class="separator:ga904e9c2436bb099397c0823506a0771f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf90651cf2f5e7ee2b11cfdc5a6749534"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaf90651cf2f5e7ee2b11cfdc5a6749534">GLM_ALIGNED_TYPEDEF</a> (i32vec2, aligned_i32vec2, 8)</td></tr>
<tr class="memdesc:gaf90651cf2f5e7ee2b11cfdc5a6749534"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit signed integer aligned vector of 2 components type.  <a href="a00364.html#gaf90651cf2f5e7ee2b11cfdc5a6749534">More...</a><br /></td></tr>
<tr class="separator:gaf90651cf2f5e7ee2b11cfdc5a6749534"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7354a4ead8cb17868aec36b9c30d6010"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga7354a4ead8cb17868aec36b9c30d6010">GLM_ALIGNED_TYPEDEF</a> (i32vec3, aligned_i32vec3, 16)</td></tr>
<tr class="memdesc:ga7354a4ead8cb17868aec36b9c30d6010"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit signed integer aligned vector of 3 components type.  <a href="a00364.html#ga7354a4ead8cb17868aec36b9c30d6010">More...</a><br /></td></tr>
<tr class="separator:ga7354a4ead8cb17868aec36b9c30d6010"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad2ecbdea18732163e2636e27b37981ee"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gad2ecbdea18732163e2636e27b37981ee">GLM_ALIGNED_TYPEDEF</a> (i32vec4, aligned_i32vec4, 16)</td></tr>
<tr class="memdesc:gad2ecbdea18732163e2636e27b37981ee"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit signed integer aligned vector of 4 components type.  <a href="a00364.html#gad2ecbdea18732163e2636e27b37981ee">More...</a><br /></td></tr>
<tr class="separator:gad2ecbdea18732163e2636e27b37981ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga965b1c9aa1800e93d4abc2eb2b5afcbf"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga965b1c9aa1800e93d4abc2eb2b5afcbf">GLM_ALIGNED_TYPEDEF</a> (i64vec1, aligned_i64vec1, 8)</td></tr>
<tr class="memdesc:ga965b1c9aa1800e93d4abc2eb2b5afcbf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit signed integer aligned scalar type.  <a href="a00364.html#ga965b1c9aa1800e93d4abc2eb2b5afcbf">More...</a><br /></td></tr>
<tr class="separator:ga965b1c9aa1800e93d4abc2eb2b5afcbf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1f9e9c2ea2768675dff9bae5cde2d829"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga1f9e9c2ea2768675dff9bae5cde2d829">GLM_ALIGNED_TYPEDEF</a> (i64vec2, aligned_i64vec2, 16)</td></tr>
<tr class="memdesc:ga1f9e9c2ea2768675dff9bae5cde2d829"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit signed integer aligned vector of 2 components type.  <a href="a00364.html#ga1f9e9c2ea2768675dff9bae5cde2d829">More...</a><br /></td></tr>
<tr class="separator:ga1f9e9c2ea2768675dff9bae5cde2d829"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad77c317b7d942322cd5be4c8127b3187"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gad77c317b7d942322cd5be4c8127b3187">GLM_ALIGNED_TYPEDEF</a> (i64vec3, aligned_i64vec3, 32)</td></tr>
<tr class="memdesc:gad77c317b7d942322cd5be4c8127b3187"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit signed integer aligned vector of 3 components type.  <a href="a00364.html#gad77c317b7d942322cd5be4c8127b3187">More...</a><br /></td></tr>
<tr class="separator:gad77c317b7d942322cd5be4c8127b3187"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga716f8ea809bdb11b5b542d8b71aeb04f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga716f8ea809bdb11b5b542d8b71aeb04f">GLM_ALIGNED_TYPEDEF</a> (i64vec4, aligned_i64vec4, 32)</td></tr>
<tr class="memdesc:ga716f8ea809bdb11b5b542d8b71aeb04f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit signed integer aligned vector of 4 components type.  <a href="a00364.html#ga716f8ea809bdb11b5b542d8b71aeb04f">More...</a><br /></td></tr>
<tr class="separator:ga716f8ea809bdb11b5b542d8b71aeb04f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad46f8e9082d5878b1bc04f9c1471cdaa"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gad46f8e9082d5878b1bc04f9c1471cdaa">GLM_ALIGNED_TYPEDEF</a> (lowp_uint8, aligned_lowp_uint8, 1)</td></tr>
<tr class="memdesc:gad46f8e9082d5878b1bc04f9c1471cdaa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#gad46f8e9082d5878b1bc04f9c1471cdaa">More...</a><br /></td></tr>
<tr class="separator:gad46f8e9082d5878b1bc04f9c1471cdaa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1246094581af624aca6c7499aaabf801"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga1246094581af624aca6c7499aaabf801">GLM_ALIGNED_TYPEDEF</a> (lowp_uint16, aligned_lowp_uint16, 2)</td></tr>
<tr class="memdesc:ga1246094581af624aca6c7499aaabf801"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga1246094581af624aca6c7499aaabf801">More...</a><br /></td></tr>
<tr class="separator:ga1246094581af624aca6c7499aaabf801"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7a5009a1d0196bbf21dd7518f61f0249"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga7a5009a1d0196bbf21dd7518f61f0249">GLM_ALIGNED_TYPEDEF</a> (lowp_uint32, aligned_lowp_uint32, 4)</td></tr>
<tr class="memdesc:ga7a5009a1d0196bbf21dd7518f61f0249"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga7a5009a1d0196bbf21dd7518f61f0249">More...</a><br /></td></tr>
<tr class="separator:ga7a5009a1d0196bbf21dd7518f61f0249"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga45213fd18b3bb1df391671afefe4d1e7"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga45213fd18b3bb1df391671afefe4d1e7">GLM_ALIGNED_TYPEDEF</a> (lowp_uint64, aligned_lowp_uint64, 8)</td></tr>
<tr class="memdesc:ga45213fd18b3bb1df391671afefe4d1e7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga45213fd18b3bb1df391671afefe4d1e7">More...</a><br /></td></tr>
<tr class="separator:ga45213fd18b3bb1df391671afefe4d1e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0ba26b4e3fd9ecbc25358efd68d8a4ca"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga0ba26b4e3fd9ecbc25358efd68d8a4ca">GLM_ALIGNED_TYPEDEF</a> (lowp_uint8_t, aligned_lowp_uint8_t, 1)</td></tr>
<tr class="memdesc:ga0ba26b4e3fd9ecbc25358efd68d8a4ca"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga0ba26b4e3fd9ecbc25358efd68d8a4ca">More...</a><br /></td></tr>
<tr class="separator:ga0ba26b4e3fd9ecbc25358efd68d8a4ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf2b58f5fb6d4ec8ce7b76221d3af43e1"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaf2b58f5fb6d4ec8ce7b76221d3af43e1">GLM_ALIGNED_TYPEDEF</a> (lowp_uint16_t, aligned_lowp_uint16_t, 2)</td></tr>
<tr class="memdesc:gaf2b58f5fb6d4ec8ce7b76221d3af43e1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#gaf2b58f5fb6d4ec8ce7b76221d3af43e1">More...</a><br /></td></tr>
<tr class="separator:gaf2b58f5fb6d4ec8ce7b76221d3af43e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadc246401847dcba155f0699425e49dcd"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gadc246401847dcba155f0699425e49dcd">GLM_ALIGNED_TYPEDEF</a> (lowp_uint32_t, aligned_lowp_uint32_t, 4)</td></tr>
<tr class="memdesc:gadc246401847dcba155f0699425e49dcd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#gadc246401847dcba155f0699425e49dcd">More...</a><br /></td></tr>
<tr class="separator:gadc246401847dcba155f0699425e49dcd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaace64bddf51a9def01498da9a94fb01c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaace64bddf51a9def01498da9a94fb01c">GLM_ALIGNED_TYPEDEF</a> (lowp_uint64_t, aligned_lowp_uint64_t, 8)</td></tr>
<tr class="memdesc:gaace64bddf51a9def01498da9a94fb01c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#gaace64bddf51a9def01498da9a94fb01c">More...</a><br /></td></tr>
<tr class="separator:gaace64bddf51a9def01498da9a94fb01c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad7bb97c29d664bd86ffb1bed4abc5534"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gad7bb97c29d664bd86ffb1bed4abc5534">GLM_ALIGNED_TYPEDEF</a> (lowp_u8, aligned_lowp_u8, 1)</td></tr>
<tr class="memdesc:gad7bb97c29d664bd86ffb1bed4abc5534"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#gad7bb97c29d664bd86ffb1bed4abc5534">More...</a><br /></td></tr>
<tr class="separator:gad7bb97c29d664bd86ffb1bed4abc5534"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga404bba7785130e0b1384d695a9450b28"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga404bba7785130e0b1384d695a9450b28">GLM_ALIGNED_TYPEDEF</a> (lowp_u16, aligned_lowp_u16, 2)</td></tr>
<tr class="memdesc:ga404bba7785130e0b1384d695a9450b28"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga404bba7785130e0b1384d695a9450b28">More...</a><br /></td></tr>
<tr class="separator:ga404bba7785130e0b1384d695a9450b28"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga31ba41fd896257536958ec6080203d2a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga31ba41fd896257536958ec6080203d2a">GLM_ALIGNED_TYPEDEF</a> (lowp_u32, aligned_lowp_u32, 4)</td></tr>
<tr class="memdesc:ga31ba41fd896257536958ec6080203d2a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga31ba41fd896257536958ec6080203d2a">More...</a><br /></td></tr>
<tr class="separator:ga31ba41fd896257536958ec6080203d2a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacca5f13627f57b3505676e40a6e43e5e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gacca5f13627f57b3505676e40a6e43e5e">GLM_ALIGNED_TYPEDEF</a> (lowp_u64, aligned_lowp_u64, 8)</td></tr>
<tr class="memdesc:gacca5f13627f57b3505676e40a6e43e5e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#gacca5f13627f57b3505676e40a6e43e5e">More...</a><br /></td></tr>
<tr class="separator:gacca5f13627f57b3505676e40a6e43e5e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5faf1d3e70bf33174dd7f3d01d5b883b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga5faf1d3e70bf33174dd7f3d01d5b883b">GLM_ALIGNED_TYPEDEF</a> (mediump_uint8, aligned_mediump_uint8, 1)</td></tr>
<tr class="memdesc:ga5faf1d3e70bf33174dd7f3d01d5b883b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga5faf1d3e70bf33174dd7f3d01d5b883b">More...</a><br /></td></tr>
<tr class="separator:ga5faf1d3e70bf33174dd7f3d01d5b883b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga727e2bf2c433bb3b0182605860a48363"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga727e2bf2c433bb3b0182605860a48363">GLM_ALIGNED_TYPEDEF</a> (mediump_uint16, aligned_mediump_uint16, 2)</td></tr>
<tr class="memdesc:ga727e2bf2c433bb3b0182605860a48363"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga727e2bf2c433bb3b0182605860a48363">More...</a><br /></td></tr>
<tr class="separator:ga727e2bf2c433bb3b0182605860a48363"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga12566ca66d5962dadb4a5eb4c74e891e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga12566ca66d5962dadb4a5eb4c74e891e">GLM_ALIGNED_TYPEDEF</a> (mediump_uint32, aligned_mediump_uint32, 4)</td></tr>
<tr class="memdesc:ga12566ca66d5962dadb4a5eb4c74e891e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga12566ca66d5962dadb4a5eb4c74e891e">More...</a><br /></td></tr>
<tr class="separator:ga12566ca66d5962dadb4a5eb4c74e891e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7b66a97a8acaa35c5a377b947318c6bc"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga7b66a97a8acaa35c5a377b947318c6bc">GLM_ALIGNED_TYPEDEF</a> (mediump_uint64, aligned_mediump_uint64, 8)</td></tr>
<tr class="memdesc:ga7b66a97a8acaa35c5a377b947318c6bc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga7b66a97a8acaa35c5a377b947318c6bc">More...</a><br /></td></tr>
<tr class="separator:ga7b66a97a8acaa35c5a377b947318c6bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa9cde002439b74fa66120a16a9f55fcc"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaa9cde002439b74fa66120a16a9f55fcc">GLM_ALIGNED_TYPEDEF</a> (mediump_uint8_t, aligned_mediump_uint8_t, 1)</td></tr>
<tr class="memdesc:gaa9cde002439b74fa66120a16a9f55fcc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#gaa9cde002439b74fa66120a16a9f55fcc">More...</a><br /></td></tr>
<tr class="separator:gaa9cde002439b74fa66120a16a9f55fcc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1ca98c67f7d1e975f7c5202f1da1df1f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga1ca98c67f7d1e975f7c5202f1da1df1f">GLM_ALIGNED_TYPEDEF</a> (mediump_uint16_t, aligned_mediump_uint16_t, 2)</td></tr>
<tr class="memdesc:ga1ca98c67f7d1e975f7c5202f1da1df1f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga1ca98c67f7d1e975f7c5202f1da1df1f">More...</a><br /></td></tr>
<tr class="separator:ga1ca98c67f7d1e975f7c5202f1da1df1f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1dc8bc6199d785f235576948d80a597c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga1dc8bc6199d785f235576948d80a597c">GLM_ALIGNED_TYPEDEF</a> (mediump_uint32_t, aligned_mediump_uint32_t, 4)</td></tr>
<tr class="memdesc:ga1dc8bc6199d785f235576948d80a597c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga1dc8bc6199d785f235576948d80a597c">More...</a><br /></td></tr>
<tr class="separator:ga1dc8bc6199d785f235576948d80a597c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad14a0f2ec93519682b73d70b8e401d81"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gad14a0f2ec93519682b73d70b8e401d81">GLM_ALIGNED_TYPEDEF</a> (mediump_uint64_t, aligned_mediump_uint64_t, 8)</td></tr>
<tr class="memdesc:gad14a0f2ec93519682b73d70b8e401d81"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#gad14a0f2ec93519682b73d70b8e401d81">More...</a><br /></td></tr>
<tr class="separator:gad14a0f2ec93519682b73d70b8e401d81"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gada8b996eb6526dc1ead813bd49539d1b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gada8b996eb6526dc1ead813bd49539d1b">GLM_ALIGNED_TYPEDEF</a> (mediump_u8, aligned_mediump_u8, 1)</td></tr>
<tr class="memdesc:gada8b996eb6526dc1ead813bd49539d1b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#gada8b996eb6526dc1ead813bd49539d1b">More...</a><br /></td></tr>
<tr class="separator:gada8b996eb6526dc1ead813bd49539d1b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga28948f6bfb52b42deb9d73ae1ea8d8b0"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga28948f6bfb52b42deb9d73ae1ea8d8b0">GLM_ALIGNED_TYPEDEF</a> (mediump_u16, aligned_mediump_u16, 2)</td></tr>
<tr class="memdesc:ga28948f6bfb52b42deb9d73ae1ea8d8b0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga28948f6bfb52b42deb9d73ae1ea8d8b0">More...</a><br /></td></tr>
<tr class="separator:ga28948f6bfb52b42deb9d73ae1ea8d8b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad6a7c0b5630f89d3f1c5b4ef2919bb4c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gad6a7c0b5630f89d3f1c5b4ef2919bb4c">GLM_ALIGNED_TYPEDEF</a> (mediump_u32, aligned_mediump_u32, 4)</td></tr>
<tr class="memdesc:gad6a7c0b5630f89d3f1c5b4ef2919bb4c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#gad6a7c0b5630f89d3f1c5b4ef2919bb4c">More...</a><br /></td></tr>
<tr class="separator:gad6a7c0b5630f89d3f1c5b4ef2919bb4c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa0fc531cbaa972ac3a0b86d21ef4a7fa"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaa0fc531cbaa972ac3a0b86d21ef4a7fa">GLM_ALIGNED_TYPEDEF</a> (mediump_u64, aligned_mediump_u64, 8)</td></tr>
<tr class="memdesc:gaa0fc531cbaa972ac3a0b86d21ef4a7fa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#gaa0fc531cbaa972ac3a0b86d21ef4a7fa">More...</a><br /></td></tr>
<tr class="separator:gaa0fc531cbaa972ac3a0b86d21ef4a7fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0ee829f7b754b262bbfe6317c0d678ac"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga0ee829f7b754b262bbfe6317c0d678ac">GLM_ALIGNED_TYPEDEF</a> (highp_uint8, aligned_highp_uint8, 1)</td></tr>
<tr class="memdesc:ga0ee829f7b754b262bbfe6317c0d678ac"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga0ee829f7b754b262bbfe6317c0d678ac">More...</a><br /></td></tr>
<tr class="separator:ga0ee829f7b754b262bbfe6317c0d678ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga447848a817a626cae08cedc9778b331c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga447848a817a626cae08cedc9778b331c">GLM_ALIGNED_TYPEDEF</a> (highp_uint16, aligned_highp_uint16, 2)</td></tr>
<tr class="memdesc:ga447848a817a626cae08cedc9778b331c"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga447848a817a626cae08cedc9778b331c">More...</a><br /></td></tr>
<tr class="separator:ga447848a817a626cae08cedc9778b331c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6027ae13b2734f542a6e7beee11b8820"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga6027ae13b2734f542a6e7beee11b8820">GLM_ALIGNED_TYPEDEF</a> (highp_uint32, aligned_highp_uint32, 4)</td></tr>
<tr class="memdesc:ga6027ae13b2734f542a6e7beee11b8820"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga6027ae13b2734f542a6e7beee11b8820">More...</a><br /></td></tr>
<tr class="separator:ga6027ae13b2734f542a6e7beee11b8820"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2aca46c8608c95ef991ee4c332acde5f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga2aca46c8608c95ef991ee4c332acde5f">GLM_ALIGNED_TYPEDEF</a> (highp_uint64, aligned_highp_uint64, 8)</td></tr>
<tr class="memdesc:ga2aca46c8608c95ef991ee4c332acde5f"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga2aca46c8608c95ef991ee4c332acde5f">More...</a><br /></td></tr>
<tr class="separator:ga2aca46c8608c95ef991ee4c332acde5f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaff50b10dd1c48be324fdaffd18e2c7ea"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaff50b10dd1c48be324fdaffd18e2c7ea">GLM_ALIGNED_TYPEDEF</a> (highp_uint8_t, aligned_highp_uint8_t, 1)</td></tr>
<tr class="memdesc:gaff50b10dd1c48be324fdaffd18e2c7ea"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#gaff50b10dd1c48be324fdaffd18e2c7ea">More...</a><br /></td></tr>
<tr class="separator:gaff50b10dd1c48be324fdaffd18e2c7ea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9fc4421dbb833d5461e6d4e59dcfde55"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga9fc4421dbb833d5461e6d4e59dcfde55">GLM_ALIGNED_TYPEDEF</a> (highp_uint16_t, aligned_highp_uint16_t, 2)</td></tr>
<tr class="memdesc:ga9fc4421dbb833d5461e6d4e59dcfde55"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga9fc4421dbb833d5461e6d4e59dcfde55">More...</a><br /></td></tr>
<tr class="separator:ga9fc4421dbb833d5461e6d4e59dcfde55"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga329f1e2b94b33ba5e3918197030bcf03"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga329f1e2b94b33ba5e3918197030bcf03">GLM_ALIGNED_TYPEDEF</a> (highp_uint32_t, aligned_highp_uint32_t, 4)</td></tr>
<tr class="memdesc:ga329f1e2b94b33ba5e3918197030bcf03"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga329f1e2b94b33ba5e3918197030bcf03">More...</a><br /></td></tr>
<tr class="separator:ga329f1e2b94b33ba5e3918197030bcf03"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga71e646f7e301aa422328194162c9c998"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga71e646f7e301aa422328194162c9c998">GLM_ALIGNED_TYPEDEF</a> (highp_uint64_t, aligned_highp_uint64_t, 8)</td></tr>
<tr class="memdesc:ga71e646f7e301aa422328194162c9c998"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga71e646f7e301aa422328194162c9c998">More...</a><br /></td></tr>
<tr class="separator:ga71e646f7e301aa422328194162c9c998"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8942e09f479489441a7a5004c6d8cb66"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga8942e09f479489441a7a5004c6d8cb66">GLM_ALIGNED_TYPEDEF</a> (highp_u8, aligned_highp_u8, 1)</td></tr>
<tr class="memdesc:ga8942e09f479489441a7a5004c6d8cb66"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga8942e09f479489441a7a5004c6d8cb66">More...</a><br /></td></tr>
<tr class="separator:ga8942e09f479489441a7a5004c6d8cb66"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaab32497d6e4db16ee439dbedd64c5865"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaab32497d6e4db16ee439dbedd64c5865">GLM_ALIGNED_TYPEDEF</a> (highp_u16, aligned_highp_u16, 2)</td></tr>
<tr class="memdesc:gaab32497d6e4db16ee439dbedd64c5865"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#gaab32497d6e4db16ee439dbedd64c5865">More...</a><br /></td></tr>
<tr class="separator:gaab32497d6e4db16ee439dbedd64c5865"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaadbb34952eca8e3d7fe122c3e167742"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaaadbb34952eca8e3d7fe122c3e167742">GLM_ALIGNED_TYPEDEF</a> (highp_u32, aligned_highp_u32, 4)</td></tr>
<tr class="memdesc:gaaadbb34952eca8e3d7fe122c3e167742"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#gaaadbb34952eca8e3d7fe122c3e167742">More...</a><br /></td></tr>
<tr class="separator:gaaadbb34952eca8e3d7fe122c3e167742"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga92024d27c74a3650afb55ec8e024ed25"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga92024d27c74a3650afb55ec8e024ed25">GLM_ALIGNED_TYPEDEF</a> (highp_u64, aligned_highp_u64, 8)</td></tr>
<tr class="memdesc:ga92024d27c74a3650afb55ec8e024ed25"><td class="mdescLeft">&#160;</td><td class="mdescRight">High qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga92024d27c74a3650afb55ec8e024ed25">More...</a><br /></td></tr>
<tr class="separator:ga92024d27c74a3650afb55ec8e024ed25"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabde1d0b4072df35453db76075ab896a6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gabde1d0b4072df35453db76075ab896a6">GLM_ALIGNED_TYPEDEF</a> (uint8, aligned_uint8, 1)</td></tr>
<tr class="memdesc:gabde1d0b4072df35453db76075ab896a6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#gabde1d0b4072df35453db76075ab896a6">More...</a><br /></td></tr>
<tr class="separator:gabde1d0b4072df35453db76075ab896a6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga06c296c9e398b294c8c9dd2a7693dcbb"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga06c296c9e398b294c8c9dd2a7693dcbb">GLM_ALIGNED_TYPEDEF</a> (uint16, aligned_uint16, 2)</td></tr>
<tr class="memdesc:ga06c296c9e398b294c8c9dd2a7693dcbb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga06c296c9e398b294c8c9dd2a7693dcbb">More...</a><br /></td></tr>
<tr class="separator:ga06c296c9e398b294c8c9dd2a7693dcbb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacf1744488c96ebd33c9f36ad33b2010a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gacf1744488c96ebd33c9f36ad33b2010a">GLM_ALIGNED_TYPEDEF</a> (uint32, aligned_uint32, 4)</td></tr>
<tr class="memdesc:gacf1744488c96ebd33c9f36ad33b2010a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#gacf1744488c96ebd33c9f36ad33b2010a">More...</a><br /></td></tr>
<tr class="separator:gacf1744488c96ebd33c9f36ad33b2010a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3328061a64c20ba59d5f9da24c2cd059"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga3328061a64c20ba59d5f9da24c2cd059">GLM_ALIGNED_TYPEDEF</a> (uint64, aligned_uint64, 8)</td></tr>
<tr class="memdesc:ga3328061a64c20ba59d5f9da24c2cd059"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga3328061a64c20ba59d5f9da24c2cd059">More...</a><br /></td></tr>
<tr class="separator:ga3328061a64c20ba59d5f9da24c2cd059"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf6ced36f13bae57f377bafa6f5fcc299"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaf6ced36f13bae57f377bafa6f5fcc299">GLM_ALIGNED_TYPEDEF</a> (uint8_t, aligned_uint8_t, 1)</td></tr>
<tr class="memdesc:gaf6ced36f13bae57f377bafa6f5fcc299"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#gaf6ced36f13bae57f377bafa6f5fcc299">More...</a><br /></td></tr>
<tr class="separator:gaf6ced36f13bae57f377bafa6f5fcc299"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafbc7fb7847bfc78a339d1d371c915c73"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gafbc7fb7847bfc78a339d1d371c915c73">GLM_ALIGNED_TYPEDEF</a> (uint16_t, aligned_uint16_t, 2)</td></tr>
<tr class="memdesc:gafbc7fb7847bfc78a339d1d371c915c73"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#gafbc7fb7847bfc78a339d1d371c915c73">More...</a><br /></td></tr>
<tr class="separator:gafbc7fb7847bfc78a339d1d371c915c73"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa86bc56a73fd8120b1121b5f5e6245ae"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaa86bc56a73fd8120b1121b5f5e6245ae">GLM_ALIGNED_TYPEDEF</a> (uint32_t, aligned_uint32_t, 4)</td></tr>
<tr class="memdesc:gaa86bc56a73fd8120b1121b5f5e6245ae"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#gaa86bc56a73fd8120b1121b5f5e6245ae">More...</a><br /></td></tr>
<tr class="separator:gaa86bc56a73fd8120b1121b5f5e6245ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga68c0b9e669060d0eb5ab8c3ddeb483d8"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga68c0b9e669060d0eb5ab8c3ddeb483d8">GLM_ALIGNED_TYPEDEF</a> (uint64_t, aligned_uint64_t, 8)</td></tr>
<tr class="memdesc:ga68c0b9e669060d0eb5ab8c3ddeb483d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga68c0b9e669060d0eb5ab8c3ddeb483d8">More...</a><br /></td></tr>
<tr class="separator:ga68c0b9e669060d0eb5ab8c3ddeb483d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4f3bab577daf3343e99cc005134bce86"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga4f3bab577daf3343e99cc005134bce86">GLM_ALIGNED_TYPEDEF</a> (u8, aligned_u8, 1)</td></tr>
<tr class="memdesc:ga4f3bab577daf3343e99cc005134bce86"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga4f3bab577daf3343e99cc005134bce86">More...</a><br /></td></tr>
<tr class="separator:ga4f3bab577daf3343e99cc005134bce86"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga13a2391339d0790d43b76d00a7611c4f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga13a2391339d0790d43b76d00a7611c4f">GLM_ALIGNED_TYPEDEF</a> (u16, aligned_u16, 2)</td></tr>
<tr class="memdesc:ga13a2391339d0790d43b76d00a7611c4f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga13a2391339d0790d43b76d00a7611c4f">More...</a><br /></td></tr>
<tr class="separator:ga13a2391339d0790d43b76d00a7611c4f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga197570e03acbc3d18ab698e342971e8f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga197570e03acbc3d18ab698e342971e8f">GLM_ALIGNED_TYPEDEF</a> (u32, aligned_u32, 4)</td></tr>
<tr class="memdesc:ga197570e03acbc3d18ab698e342971e8f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga197570e03acbc3d18ab698e342971e8f">More...</a><br /></td></tr>
<tr class="separator:ga197570e03acbc3d18ab698e342971e8f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0f033b21e145a1faa32c62ede5878993"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga0f033b21e145a1faa32c62ede5878993">GLM_ALIGNED_TYPEDEF</a> (u64, aligned_u64, 8)</td></tr>
<tr class="memdesc:ga0f033b21e145a1faa32c62ede5878993"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga0f033b21e145a1faa32c62ede5878993">More...</a><br /></td></tr>
<tr class="separator:ga0f033b21e145a1faa32c62ede5878993"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga509af83527f5cd512e9a7873590663aa"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga509af83527f5cd512e9a7873590663aa">GLM_ALIGNED_TYPEDEF</a> (uvec1, aligned_uvec1, 4)</td></tr>
<tr class="memdesc:ga509af83527f5cd512e9a7873590663aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga509af83527f5cd512e9a7873590663aa">More...</a><br /></td></tr>
<tr class="separator:ga509af83527f5cd512e9a7873590663aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga94e86186978c502c6dc0c0d9c4a30679"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga94e86186978c502c6dc0c0d9c4a30679">GLM_ALIGNED_TYPEDEF</a> (uvec2, aligned_uvec2, 8)</td></tr>
<tr class="memdesc:ga94e86186978c502c6dc0c0d9c4a30679"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit unsigned integer aligned vector of 2 components type.  <a href="a00364.html#ga94e86186978c502c6dc0c0d9c4a30679">More...</a><br /></td></tr>
<tr class="separator:ga94e86186978c502c6dc0c0d9c4a30679"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5cec574686a7f3c8ed24bb195c5e2d0a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga5cec574686a7f3c8ed24bb195c5e2d0a">GLM_ALIGNED_TYPEDEF</a> (uvec3, aligned_uvec3, 16)</td></tr>
<tr class="memdesc:ga5cec574686a7f3c8ed24bb195c5e2d0a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit unsigned integer aligned vector of 3 components type.  <a href="a00364.html#ga5cec574686a7f3c8ed24bb195c5e2d0a">More...</a><br /></td></tr>
<tr class="separator:ga5cec574686a7f3c8ed24bb195c5e2d0a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga47edfdcee9c89b1ebdaf20450323b1d4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga47edfdcee9c89b1ebdaf20450323b1d4">GLM_ALIGNED_TYPEDEF</a> (uvec4, aligned_uvec4, 16)</td></tr>
<tr class="memdesc:ga47edfdcee9c89b1ebdaf20450323b1d4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit unsigned integer aligned vector of 4 components type.  <a href="a00364.html#ga47edfdcee9c89b1ebdaf20450323b1d4">More...</a><br /></td></tr>
<tr class="separator:ga47edfdcee9c89b1ebdaf20450323b1d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5611d6718e3a00096918a64192e73a45"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga5611d6718e3a00096918a64192e73a45">GLM_ALIGNED_TYPEDEF</a> (u8vec1, aligned_u8vec1, 1)</td></tr>
<tr class="memdesc:ga5611d6718e3a00096918a64192e73a45"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga5611d6718e3a00096918a64192e73a45">More...</a><br /></td></tr>
<tr class="separator:ga5611d6718e3a00096918a64192e73a45"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga19837e6f72b60d994a805ef564c6c326"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga19837e6f72b60d994a805ef564c6c326">GLM_ALIGNED_TYPEDEF</a> (u8vec2, aligned_u8vec2, 2)</td></tr>
<tr class="memdesc:ga19837e6f72b60d994a805ef564c6c326"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit unsigned integer aligned vector of 2 components type.  <a href="a00364.html#ga19837e6f72b60d994a805ef564c6c326">More...</a><br /></td></tr>
<tr class="separator:ga19837e6f72b60d994a805ef564c6c326"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9740cf8e34f068049b42a2753f9601c2"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga9740cf8e34f068049b42a2753f9601c2">GLM_ALIGNED_TYPEDEF</a> (u8vec3, aligned_u8vec3, 4)</td></tr>
<tr class="memdesc:ga9740cf8e34f068049b42a2753f9601c2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit unsigned integer aligned vector of 3 components type.  <a href="a00364.html#ga9740cf8e34f068049b42a2753f9601c2">More...</a><br /></td></tr>
<tr class="separator:ga9740cf8e34f068049b42a2753f9601c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8b8588bb221448f5541a858903822a57"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga8b8588bb221448f5541a858903822a57">GLM_ALIGNED_TYPEDEF</a> (u8vec4, aligned_u8vec4, 4)</td></tr>
<tr class="memdesc:ga8b8588bb221448f5541a858903822a57"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 8 bit unsigned integer aligned vector of 4 components type.  <a href="a00364.html#ga8b8588bb221448f5541a858903822a57">More...</a><br /></td></tr>
<tr class="separator:ga8b8588bb221448f5541a858903822a57"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga991abe990c16de26b2129d6bc2f4c051"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga991abe990c16de26b2129d6bc2f4c051">GLM_ALIGNED_TYPEDEF</a> (u16vec1, aligned_u16vec1, 2)</td></tr>
<tr class="memdesc:ga991abe990c16de26b2129d6bc2f4c051"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit unsigned integer aligned scalar type.  <a href="a00364.html#ga991abe990c16de26b2129d6bc2f4c051">More...</a><br /></td></tr>
<tr class="separator:ga991abe990c16de26b2129d6bc2f4c051"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac01bb9fc32a1cd76c2b80d030f71df4c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gac01bb9fc32a1cd76c2b80d030f71df4c">GLM_ALIGNED_TYPEDEF</a> (u16vec2, aligned_u16vec2, 4)</td></tr>
<tr class="memdesc:gac01bb9fc32a1cd76c2b80d030f71df4c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit unsigned integer aligned vector of 2 components type.  <a href="a00364.html#gac01bb9fc32a1cd76c2b80d030f71df4c">More...</a><br /></td></tr>
<tr class="separator:gac01bb9fc32a1cd76c2b80d030f71df4c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga09540dbca093793a36a8997e0d4bee77"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga09540dbca093793a36a8997e0d4bee77">GLM_ALIGNED_TYPEDEF</a> (u16vec3, aligned_u16vec3, 8)</td></tr>
<tr class="memdesc:ga09540dbca093793a36a8997e0d4bee77"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit unsigned integer aligned vector of 3 components type.  <a href="a00364.html#ga09540dbca093793a36a8997e0d4bee77">More...</a><br /></td></tr>
<tr class="separator:ga09540dbca093793a36a8997e0d4bee77"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaecafb5996f5a44f57e34d29c8670741e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaecafb5996f5a44f57e34d29c8670741e">GLM_ALIGNED_TYPEDEF</a> (u16vec4, aligned_u16vec4, 8)</td></tr>
<tr class="memdesc:gaecafb5996f5a44f57e34d29c8670741e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 16 bit unsigned integer aligned vector of 4 components type.  <a href="a00364.html#gaecafb5996f5a44f57e34d29c8670741e">More...</a><br /></td></tr>
<tr class="separator:gaecafb5996f5a44f57e34d29c8670741e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac6b161a04d2f8408fe1c9d857e8daac0"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gac6b161a04d2f8408fe1c9d857e8daac0">GLM_ALIGNED_TYPEDEF</a> (u32vec1, aligned_u32vec1, 4)</td></tr>
<tr class="memdesc:gac6b161a04d2f8408fe1c9d857e8daac0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit unsigned integer aligned scalar type.  <a href="a00364.html#gac6b161a04d2f8408fe1c9d857e8daac0">More...</a><br /></td></tr>
<tr class="separator:gac6b161a04d2f8408fe1c9d857e8daac0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1fa0dfc8feb0fa17dab2acd43e05342b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga1fa0dfc8feb0fa17dab2acd43e05342b">GLM_ALIGNED_TYPEDEF</a> (u32vec2, aligned_u32vec2, 8)</td></tr>
<tr class="memdesc:ga1fa0dfc8feb0fa17dab2acd43e05342b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit unsigned integer aligned vector of 2 components type.  <a href="a00364.html#ga1fa0dfc8feb0fa17dab2acd43e05342b">More...</a><br /></td></tr>
<tr class="separator:ga1fa0dfc8feb0fa17dab2acd43e05342b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0019500abbfa9c66eff61ca75eaaed94"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga0019500abbfa9c66eff61ca75eaaed94">GLM_ALIGNED_TYPEDEF</a> (u32vec3, aligned_u32vec3, 16)</td></tr>
<tr class="memdesc:ga0019500abbfa9c66eff61ca75eaaed94"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit unsigned integer aligned vector of 3 components type.  <a href="a00364.html#ga0019500abbfa9c66eff61ca75eaaed94">More...</a><br /></td></tr>
<tr class="separator:ga0019500abbfa9c66eff61ca75eaaed94"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga14fd29d01dae7b08a04e9facbcc18824"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga14fd29d01dae7b08a04e9facbcc18824">GLM_ALIGNED_TYPEDEF</a> (u32vec4, aligned_u32vec4, 16)</td></tr>
<tr class="memdesc:ga14fd29d01dae7b08a04e9facbcc18824"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 32 bit unsigned integer aligned vector of 4 components type.  <a href="a00364.html#ga14fd29d01dae7b08a04e9facbcc18824">More...</a><br /></td></tr>
<tr class="separator:ga14fd29d01dae7b08a04e9facbcc18824"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab253845f534a67136f9619843cade903"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gab253845f534a67136f9619843cade903">GLM_ALIGNED_TYPEDEF</a> (u64vec1, aligned_u64vec1, 8)</td></tr>
<tr class="memdesc:gab253845f534a67136f9619843cade903"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit unsigned integer aligned scalar type.  <a href="a00364.html#gab253845f534a67136f9619843cade903">More...</a><br /></td></tr>
<tr class="separator:gab253845f534a67136f9619843cade903"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga929427a7627940cdf3304f9c050b677d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga929427a7627940cdf3304f9c050b677d">GLM_ALIGNED_TYPEDEF</a> (u64vec2, aligned_u64vec2, 16)</td></tr>
<tr class="memdesc:ga929427a7627940cdf3304f9c050b677d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit unsigned integer aligned vector of 2 components type.  <a href="a00364.html#ga929427a7627940cdf3304f9c050b677d">More...</a><br /></td></tr>
<tr class="separator:ga929427a7627940cdf3304f9c050b677d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae373b6c04fdf9879f33d63e6949c037e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gae373b6c04fdf9879f33d63e6949c037e">GLM_ALIGNED_TYPEDEF</a> (u64vec3, aligned_u64vec3, 32)</td></tr>
<tr class="memdesc:gae373b6c04fdf9879f33d63e6949c037e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit unsigned integer aligned vector of 3 components type.  <a href="a00364.html#gae373b6c04fdf9879f33d63e6949c037e">More...</a><br /></td></tr>
<tr class="separator:gae373b6c04fdf9879f33d63e6949c037e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga53a8a03dca2015baec4584f45b8e9cdc"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga53a8a03dca2015baec4584f45b8e9cdc">GLM_ALIGNED_TYPEDEF</a> (u64vec4, aligned_u64vec4, 32)</td></tr>
<tr class="memdesc:ga53a8a03dca2015baec4584f45b8e9cdc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default qualifier 64 bit unsigned integer aligned vector of 4 components type.  <a href="a00364.html#ga53a8a03dca2015baec4584f45b8e9cdc">More...</a><br /></td></tr>
<tr class="separator:ga53a8a03dca2015baec4584f45b8e9cdc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab3301bae94ef5bf59fbdd9a24e7d2a01"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gab3301bae94ef5bf59fbdd9a24e7d2a01">GLM_ALIGNED_TYPEDEF</a> (float32, aligned_float32, 4)</td></tr>
<tr class="memdesc:gab3301bae94ef5bf59fbdd9a24e7d2a01"><td class="mdescLeft">&#160;</td><td class="mdescRight">32 bit single-qualifier floating-point aligned scalar.  <a href="a00364.html#gab3301bae94ef5bf59fbdd9a24e7d2a01">More...</a><br /></td></tr>
<tr class="separator:gab3301bae94ef5bf59fbdd9a24e7d2a01"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gada9b0bea273d3ae0286f891533b9568f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gada9b0bea273d3ae0286f891533b9568f">GLM_ALIGNED_TYPEDEF</a> (float32_t, aligned_float32_t, 4)</td></tr>
<tr class="memdesc:gada9b0bea273d3ae0286f891533b9568f"><td class="mdescLeft">&#160;</td><td class="mdescRight">32 bit single-qualifier floating-point aligned scalar.  <a href="a00364.html#gada9b0bea273d3ae0286f891533b9568f">More...</a><br /></td></tr>
<tr class="separator:gada9b0bea273d3ae0286f891533b9568f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadbce23b9f23d77bb3884e289a574ebd5"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gadbce23b9f23d77bb3884e289a574ebd5">GLM_ALIGNED_TYPEDEF</a> (float32, aligned_f32, 4)</td></tr>
<tr class="memdesc:gadbce23b9f23d77bb3884e289a574ebd5"><td class="mdescLeft">&#160;</td><td class="mdescRight">32 bit single-qualifier floating-point aligned scalar.  <a href="a00364.html#gadbce23b9f23d77bb3884e289a574ebd5">More...</a><br /></td></tr>
<tr class="separator:gadbce23b9f23d77bb3884e289a574ebd5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga75930684ff2233171c573e603f216162"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga75930684ff2233171c573e603f216162">GLM_ALIGNED_TYPEDEF</a> (float64, aligned_float64, 8)</td></tr>
<tr class="memdesc:ga75930684ff2233171c573e603f216162"><td class="mdescLeft">&#160;</td><td class="mdescRight">64 bit double-qualifier floating-point aligned scalar.  <a href="a00364.html#ga75930684ff2233171c573e603f216162">More...</a><br /></td></tr>
<tr class="separator:ga75930684ff2233171c573e603f216162"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6e3a2d83b131336219a0f4c7cbba2a48"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga6e3a2d83b131336219a0f4c7cbba2a48">GLM_ALIGNED_TYPEDEF</a> (float64_t, aligned_float64_t, 8)</td></tr>
<tr class="memdesc:ga6e3a2d83b131336219a0f4c7cbba2a48"><td class="mdescLeft">&#160;</td><td class="mdescRight">64 bit double-qualifier floating-point aligned scalar.  <a href="a00364.html#ga6e3a2d83b131336219a0f4c7cbba2a48">More...</a><br /></td></tr>
<tr class="separator:ga6e3a2d83b131336219a0f4c7cbba2a48"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa4deaa0dea930c393d55e7a4352b0a20"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaa4deaa0dea930c393d55e7a4352b0a20">GLM_ALIGNED_TYPEDEF</a> (float64, aligned_f64, 8)</td></tr>
<tr class="memdesc:gaa4deaa0dea930c393d55e7a4352b0a20"><td class="mdescLeft">&#160;</td><td class="mdescRight">64 bit double-qualifier floating-point aligned scalar.  <a href="a00364.html#gaa4deaa0dea930c393d55e7a4352b0a20">More...</a><br /></td></tr>
<tr class="separator:gaa4deaa0dea930c393d55e7a4352b0a20"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga81bc497b2bfc6f80bab690c6ee28f0f9"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga81bc497b2bfc6f80bab690c6ee28f0f9">GLM_ALIGNED_TYPEDEF</a> (vec1, aligned_vec1, 4)</td></tr>
<tr class="memdesc:ga81bc497b2bfc6f80bab690c6ee28f0f9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 1 component.  <a href="a00364.html#ga81bc497b2bfc6f80bab690c6ee28f0f9">More...</a><br /></td></tr>
<tr class="separator:ga81bc497b2bfc6f80bab690c6ee28f0f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gada3e8f783e9d4b90006695a16c39d4d4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gada3e8f783e9d4b90006695a16c39d4d4">GLM_ALIGNED_TYPEDEF</a> (vec2, aligned_vec2, 8)</td></tr>
<tr class="memdesc:gada3e8f783e9d4b90006695a16c39d4d4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 2 components.  <a href="a00364.html#gada3e8f783e9d4b90006695a16c39d4d4">More...</a><br /></td></tr>
<tr class="separator:gada3e8f783e9d4b90006695a16c39d4d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab8d081fac3a38d6f55fa552f32168d32"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gab8d081fac3a38d6f55fa552f32168d32">GLM_ALIGNED_TYPEDEF</a> (vec3, aligned_vec3, 16)</td></tr>
<tr class="memdesc:gab8d081fac3a38d6f55fa552f32168d32"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 3 components.  <a href="a00364.html#gab8d081fac3a38d6f55fa552f32168d32">More...</a><br /></td></tr>
<tr class="separator:gab8d081fac3a38d6f55fa552f32168d32"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga12fe7b9769c964c5b48dcfd8b7f40198"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga12fe7b9769c964c5b48dcfd8b7f40198">GLM_ALIGNED_TYPEDEF</a> (vec4, aligned_vec4, 16)</td></tr>
<tr class="memdesc:ga12fe7b9769c964c5b48dcfd8b7f40198"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 4 components.  <a href="a00364.html#ga12fe7b9769c964c5b48dcfd8b7f40198">More...</a><br /></td></tr>
<tr class="separator:ga12fe7b9769c964c5b48dcfd8b7f40198"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaefab04611c7f8fe1fd9be3071efea6cc"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaefab04611c7f8fe1fd9be3071efea6cc">GLM_ALIGNED_TYPEDEF</a> (fvec1, aligned_fvec1, 4)</td></tr>
<tr class="memdesc:gaefab04611c7f8fe1fd9be3071efea6cc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 1 component.  <a href="a00364.html#gaefab04611c7f8fe1fd9be3071efea6cc">More...</a><br /></td></tr>
<tr class="separator:gaefab04611c7f8fe1fd9be3071efea6cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2543c05ba19b3bd19d45b1227390c5b4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga2543c05ba19b3bd19d45b1227390c5b4">GLM_ALIGNED_TYPEDEF</a> (fvec2, aligned_fvec2, 8)</td></tr>
<tr class="memdesc:ga2543c05ba19b3bd19d45b1227390c5b4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 2 components.  <a href="a00364.html#ga2543c05ba19b3bd19d45b1227390c5b4">More...</a><br /></td></tr>
<tr class="separator:ga2543c05ba19b3bd19d45b1227390c5b4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga009afd727fd657ef33a18754d6d28f60"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga009afd727fd657ef33a18754d6d28f60">GLM_ALIGNED_TYPEDEF</a> (fvec3, aligned_fvec3, 16)</td></tr>
<tr class="memdesc:ga009afd727fd657ef33a18754d6d28f60"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 3 components.  <a href="a00364.html#ga009afd727fd657ef33a18754d6d28f60">More...</a><br /></td></tr>
<tr class="separator:ga009afd727fd657ef33a18754d6d28f60"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2f26177e74bfb301a3d0e02ec3c3ef53"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga2f26177e74bfb301a3d0e02ec3c3ef53">GLM_ALIGNED_TYPEDEF</a> (fvec4, aligned_fvec4, 16)</td></tr>
<tr class="memdesc:ga2f26177e74bfb301a3d0e02ec3c3ef53"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 4 components.  <a href="a00364.html#ga2f26177e74bfb301a3d0e02ec3c3ef53">More...</a><br /></td></tr>
<tr class="separator:ga2f26177e74bfb301a3d0e02ec3c3ef53"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga309f495a1d6b75ddf195b674b65cb1e4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga309f495a1d6b75ddf195b674b65cb1e4">GLM_ALIGNED_TYPEDEF</a> (f32vec1, aligned_f32vec1, 4)</td></tr>
<tr class="memdesc:ga309f495a1d6b75ddf195b674b65cb1e4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 1 component.  <a href="a00364.html#ga309f495a1d6b75ddf195b674b65cb1e4">More...</a><br /></td></tr>
<tr class="separator:ga309f495a1d6b75ddf195b674b65cb1e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5e185865a2217d0cd47187644683a8c3"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga5e185865a2217d0cd47187644683a8c3">GLM_ALIGNED_TYPEDEF</a> (f32vec2, aligned_f32vec2, 8)</td></tr>
<tr class="memdesc:ga5e185865a2217d0cd47187644683a8c3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 2 components.  <a href="a00364.html#ga5e185865a2217d0cd47187644683a8c3">More...</a><br /></td></tr>
<tr class="separator:ga5e185865a2217d0cd47187644683a8c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gade4458b27b039b9ca34f8ec049f3115a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gade4458b27b039b9ca34f8ec049f3115a">GLM_ALIGNED_TYPEDEF</a> (f32vec3, aligned_f32vec3, 16)</td></tr>
<tr class="memdesc:gade4458b27b039b9ca34f8ec049f3115a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 3 components.  <a href="a00364.html#gade4458b27b039b9ca34f8ec049f3115a">More...</a><br /></td></tr>
<tr class="separator:gade4458b27b039b9ca34f8ec049f3115a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2e8a12c5e6a9c4ae4ddaeda1d1cffe3b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga2e8a12c5e6a9c4ae4ddaeda1d1cffe3b">GLM_ALIGNED_TYPEDEF</a> (f32vec4, aligned_f32vec4, 16)</td></tr>
<tr class="memdesc:ga2e8a12c5e6a9c4ae4ddaeda1d1cffe3b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned vector of 4 components.  <a href="a00364.html#ga2e8a12c5e6a9c4ae4ddaeda1d1cffe3b">More...</a><br /></td></tr>
<tr class="separator:ga2e8a12c5e6a9c4ae4ddaeda1d1cffe3b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3e0f35fa0c626285a8bad41707e7316c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga3e0f35fa0c626285a8bad41707e7316c">GLM_ALIGNED_TYPEDEF</a> (dvec1, aligned_dvec1, 8)</td></tr>
<tr class="memdesc:ga3e0f35fa0c626285a8bad41707e7316c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned vector of 1 component.  <a href="a00364.html#ga3e0f35fa0c626285a8bad41707e7316c">More...</a><br /></td></tr>
<tr class="separator:ga3e0f35fa0c626285a8bad41707e7316c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga78bfec2f185d1d365ea0a9ef1e3d45b8"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga78bfec2f185d1d365ea0a9ef1e3d45b8">GLM_ALIGNED_TYPEDEF</a> (dvec2, aligned_dvec2, 16)</td></tr>
<tr class="memdesc:ga78bfec2f185d1d365ea0a9ef1e3d45b8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned vector of 2 components.  <a href="a00364.html#ga78bfec2f185d1d365ea0a9ef1e3d45b8">More...</a><br /></td></tr>
<tr class="separator:ga78bfec2f185d1d365ea0a9ef1e3d45b8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga01fe6fee6db5df580b6724a7e681f069"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga01fe6fee6db5df580b6724a7e681f069">GLM_ALIGNED_TYPEDEF</a> (dvec3, aligned_dvec3, 32)</td></tr>
<tr class="memdesc:ga01fe6fee6db5df580b6724a7e681f069"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned vector of 3 components.  <a href="a00364.html#ga01fe6fee6db5df580b6724a7e681f069">More...</a><br /></td></tr>
<tr class="separator:ga01fe6fee6db5df580b6724a7e681f069"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga687d5b8f551d5af32425c0b2fba15e99"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga687d5b8f551d5af32425c0b2fba15e99">GLM_ALIGNED_TYPEDEF</a> (dvec4, aligned_dvec4, 32)</td></tr>
<tr class="memdesc:ga687d5b8f551d5af32425c0b2fba15e99"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned vector of 4 components.  <a href="a00364.html#ga687d5b8f551d5af32425c0b2fba15e99">More...</a><br /></td></tr>
<tr class="separator:ga687d5b8f551d5af32425c0b2fba15e99"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8e842371d46842ff8f1813419ba49d0f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga8e842371d46842ff8f1813419ba49d0f">GLM_ALIGNED_TYPEDEF</a> (f64vec1, aligned_f64vec1, 8)</td></tr>
<tr class="memdesc:ga8e842371d46842ff8f1813419ba49d0f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned vector of 1 component.  <a href="a00364.html#ga8e842371d46842ff8f1813419ba49d0f">More...</a><br /></td></tr>
<tr class="separator:ga8e842371d46842ff8f1813419ba49d0f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga32814aa0f19316b43134fc25f2aad2b9"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga32814aa0f19316b43134fc25f2aad2b9">GLM_ALIGNED_TYPEDEF</a> (f64vec2, aligned_f64vec2, 16)</td></tr>
<tr class="memdesc:ga32814aa0f19316b43134fc25f2aad2b9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned vector of 2 components.  <a href="a00364.html#ga32814aa0f19316b43134fc25f2aad2b9">More...</a><br /></td></tr>
<tr class="separator:ga32814aa0f19316b43134fc25f2aad2b9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf3d3bbc1e93909b689123b085e177a14"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaf3d3bbc1e93909b689123b085e177a14">GLM_ALIGNED_TYPEDEF</a> (f64vec3, aligned_f64vec3, 32)</td></tr>
<tr class="memdesc:gaf3d3bbc1e93909b689123b085e177a14"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned vector of 3 components.  <a href="a00364.html#gaf3d3bbc1e93909b689123b085e177a14">More...</a><br /></td></tr>
<tr class="separator:gaf3d3bbc1e93909b689123b085e177a14"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga804c654cead1139bd250f90f9bb01fad"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga804c654cead1139bd250f90f9bb01fad">GLM_ALIGNED_TYPEDEF</a> (f64vec4, aligned_f64vec4, 32)</td></tr>
<tr class="memdesc:ga804c654cead1139bd250f90f9bb01fad"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned vector of 4 components.  <a href="a00364.html#ga804c654cead1139bd250f90f9bb01fad">More...</a><br /></td></tr>
<tr class="separator:ga804c654cead1139bd250f90f9bb01fad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacce4ac532880b8c7469d3c31974420a1"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gacce4ac532880b8c7469d3c31974420a1">GLM_ALIGNED_TYPEDEF</a> (mat2, aligned_mat2, 16)</td></tr>
<tr class="memdesc:gacce4ac532880b8c7469d3c31974420a1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 1x1 matrix.  <a href="a00364.html#gacce4ac532880b8c7469d3c31974420a1">More...</a><br /></td></tr>
<tr class="separator:gacce4ac532880b8c7469d3c31974420a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0498e0e249a6faddaf96aa55d7f81c3b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga0498e0e249a6faddaf96aa55d7f81c3b">GLM_ALIGNED_TYPEDEF</a> (mat3, aligned_mat3, 16)</td></tr>
<tr class="memdesc:ga0498e0e249a6faddaf96aa55d7f81c3b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 3x3 matrix.  <a href="a00364.html#ga0498e0e249a6faddaf96aa55d7f81c3b">More...</a><br /></td></tr>
<tr class="separator:ga0498e0e249a6faddaf96aa55d7f81c3b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7435d87de82a0d652b35dc5b9cc718d5"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga7435d87de82a0d652b35dc5b9cc718d5">GLM_ALIGNED_TYPEDEF</a> (mat4, aligned_mat4, 16)</td></tr>
<tr class="memdesc:ga7435d87de82a0d652b35dc5b9cc718d5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 4x4 matrix.  <a href="a00364.html#ga7435d87de82a0d652b35dc5b9cc718d5">More...</a><br /></td></tr>
<tr class="separator:ga7435d87de82a0d652b35dc5b9cc718d5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga719da577361541a4c43a2dd1d0e361e1"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga719da577361541a4c43a2dd1d0e361e1">GLM_ALIGNED_TYPEDEF</a> (fmat2x2, aligned_fmat2, 16)</td></tr>
<tr class="memdesc:ga719da577361541a4c43a2dd1d0e361e1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 1x1 matrix.  <a href="a00364.html#ga719da577361541a4c43a2dd1d0e361e1">More...</a><br /></td></tr>
<tr class="separator:ga719da577361541a4c43a2dd1d0e361e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6e7ee4f541e1d7db66cd1a224caacafb"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga6e7ee4f541e1d7db66cd1a224caacafb">GLM_ALIGNED_TYPEDEF</a> (fmat3x3, aligned_fmat3, 16)</td></tr>
<tr class="memdesc:ga6e7ee4f541e1d7db66cd1a224caacafb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 3x3 matrix.  <a href="a00364.html#ga6e7ee4f541e1d7db66cd1a224caacafb">More...</a><br /></td></tr>
<tr class="separator:ga6e7ee4f541e1d7db66cd1a224caacafb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae5d672d359f2a39f63f98c7975057486"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gae5d672d359f2a39f63f98c7975057486">GLM_ALIGNED_TYPEDEF</a> (fmat4x4, aligned_fmat4, 16)</td></tr>
<tr class="memdesc:gae5d672d359f2a39f63f98c7975057486"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 4x4 matrix.  <a href="a00364.html#gae5d672d359f2a39f63f98c7975057486">More...</a><br /></td></tr>
<tr class="separator:gae5d672d359f2a39f63f98c7975057486"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6fa2df037dbfc5fe8c8e0b4db8a34953"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga6fa2df037dbfc5fe8c8e0b4db8a34953">GLM_ALIGNED_TYPEDEF</a> (fmat2x2, aligned_fmat2x2, 16)</td></tr>
<tr class="memdesc:ga6fa2df037dbfc5fe8c8e0b4db8a34953"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 1x1 matrix.  <a href="a00364.html#ga6fa2df037dbfc5fe8c8e0b4db8a34953">More...</a><br /></td></tr>
<tr class="separator:ga6fa2df037dbfc5fe8c8e0b4db8a34953"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0743b4f4f69a3227b82ff58f6abbad62"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga0743b4f4f69a3227b82ff58f6abbad62">GLM_ALIGNED_TYPEDEF</a> (fmat2x3, aligned_fmat2x3, 16)</td></tr>
<tr class="memdesc:ga0743b4f4f69a3227b82ff58f6abbad62"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 2x3 matrix.  <a href="a00364.html#ga0743b4f4f69a3227b82ff58f6abbad62">More...</a><br /></td></tr>
<tr class="separator:ga0743b4f4f69a3227b82ff58f6abbad62"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1a76b325fdf70f961d835edd182c63dd"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga1a76b325fdf70f961d835edd182c63dd">GLM_ALIGNED_TYPEDEF</a> (fmat2x4, aligned_fmat2x4, 16)</td></tr>
<tr class="memdesc:ga1a76b325fdf70f961d835edd182c63dd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 2x4 matrix.  <a href="a00364.html#ga1a76b325fdf70f961d835edd182c63dd">More...</a><br /></td></tr>
<tr class="separator:ga1a76b325fdf70f961d835edd182c63dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4b4e181cd041ba28c3163e7b8074aef0"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga4b4e181cd041ba28c3163e7b8074aef0">GLM_ALIGNED_TYPEDEF</a> (fmat3x2, aligned_fmat3x2, 16)</td></tr>
<tr class="memdesc:ga4b4e181cd041ba28c3163e7b8074aef0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 3x2 matrix.  <a href="a00364.html#ga4b4e181cd041ba28c3163e7b8074aef0">More...</a><br /></td></tr>
<tr class="separator:ga4b4e181cd041ba28c3163e7b8074aef0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga27b13f465abc8a40705698145e222c3f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga27b13f465abc8a40705698145e222c3f">GLM_ALIGNED_TYPEDEF</a> (fmat3x3, aligned_fmat3x3, 16)</td></tr>
<tr class="memdesc:ga27b13f465abc8a40705698145e222c3f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 3x3 matrix.  <a href="a00364.html#ga27b13f465abc8a40705698145e222c3f">More...</a><br /></td></tr>
<tr class="separator:ga27b13f465abc8a40705698145e222c3f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2608d19cc275830a6f8c0b6405625a4f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga2608d19cc275830a6f8c0b6405625a4f">GLM_ALIGNED_TYPEDEF</a> (fmat3x4, aligned_fmat3x4, 16)</td></tr>
<tr class="memdesc:ga2608d19cc275830a6f8c0b6405625a4f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 3x4 matrix.  <a href="a00364.html#ga2608d19cc275830a6f8c0b6405625a4f">More...</a><br /></td></tr>
<tr class="separator:ga2608d19cc275830a6f8c0b6405625a4f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga93f09768241358a287c4cca538f1f7e7"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga93f09768241358a287c4cca538f1f7e7">GLM_ALIGNED_TYPEDEF</a> (fmat4x2, aligned_fmat4x2, 16)</td></tr>
<tr class="memdesc:ga93f09768241358a287c4cca538f1f7e7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 4x2 matrix.  <a href="a00364.html#ga93f09768241358a287c4cca538f1f7e7">More...</a><br /></td></tr>
<tr class="separator:ga93f09768241358a287c4cca538f1f7e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7c117e3ecca089e10247b1d41d88aff9"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga7c117e3ecca089e10247b1d41d88aff9">GLM_ALIGNED_TYPEDEF</a> (fmat4x3, aligned_fmat4x3, 16)</td></tr>
<tr class="memdesc:ga7c117e3ecca089e10247b1d41d88aff9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 4x3 matrix.  <a href="a00364.html#ga7c117e3ecca089e10247b1d41d88aff9">More...</a><br /></td></tr>
<tr class="separator:ga7c117e3ecca089e10247b1d41d88aff9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga07c75cd04ba42dc37fa3e105f89455c5"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga07c75cd04ba42dc37fa3e105f89455c5">GLM_ALIGNED_TYPEDEF</a> (fmat4x4, aligned_fmat4x4, 16)</td></tr>
<tr class="memdesc:ga07c75cd04ba42dc37fa3e105f89455c5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 4x4 matrix.  <a href="a00364.html#ga07c75cd04ba42dc37fa3e105f89455c5">More...</a><br /></td></tr>
<tr class="separator:ga07c75cd04ba42dc37fa3e105f89455c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga65ff0d690a34a4d7f46f9b2eb51525ee"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga65ff0d690a34a4d7f46f9b2eb51525ee">GLM_ALIGNED_TYPEDEF</a> (f32mat2x2, aligned_f32mat2, 16)</td></tr>
<tr class="memdesc:ga65ff0d690a34a4d7f46f9b2eb51525ee"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 1x1 matrix.  <a href="a00364.html#ga65ff0d690a34a4d7f46f9b2eb51525ee">More...</a><br /></td></tr>
<tr class="separator:ga65ff0d690a34a4d7f46f9b2eb51525ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadd8ddbe2bf65ccede865ba2f510176dc"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gadd8ddbe2bf65ccede865ba2f510176dc">GLM_ALIGNED_TYPEDEF</a> (f32mat3x3, aligned_f32mat3, 16)</td></tr>
<tr class="memdesc:gadd8ddbe2bf65ccede865ba2f510176dc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 3x3 matrix.  <a href="a00364.html#gadd8ddbe2bf65ccede865ba2f510176dc">More...</a><br /></td></tr>
<tr class="separator:gadd8ddbe2bf65ccede865ba2f510176dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf18dbff14bf13d3ff540c517659ec045"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaf18dbff14bf13d3ff540c517659ec045">GLM_ALIGNED_TYPEDEF</a> (f32mat4x4, aligned_f32mat4, 16)</td></tr>
<tr class="memdesc:gaf18dbff14bf13d3ff540c517659ec045"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 4x4 matrix.  <a href="a00364.html#gaf18dbff14bf13d3ff540c517659ec045">More...</a><br /></td></tr>
<tr class="separator:gaf18dbff14bf13d3ff540c517659ec045"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga66339f6139bf7ff19e245beb33f61cc8"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga66339f6139bf7ff19e245beb33f61cc8">GLM_ALIGNED_TYPEDEF</a> (f32mat2x2, aligned_f32mat2x2, 16)</td></tr>
<tr class="memdesc:ga66339f6139bf7ff19e245beb33f61cc8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 1x1 matrix.  <a href="a00364.html#ga66339f6139bf7ff19e245beb33f61cc8">More...</a><br /></td></tr>
<tr class="separator:ga66339f6139bf7ff19e245beb33f61cc8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1558a48b3934011b52612809f443e46d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga1558a48b3934011b52612809f443e46d">GLM_ALIGNED_TYPEDEF</a> (f32mat2x3, aligned_f32mat2x3, 16)</td></tr>
<tr class="memdesc:ga1558a48b3934011b52612809f443e46d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 2x3 matrix.  <a href="a00364.html#ga1558a48b3934011b52612809f443e46d">More...</a><br /></td></tr>
<tr class="separator:ga1558a48b3934011b52612809f443e46d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa52e5732daa62851627021ad551c7680"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaa52e5732daa62851627021ad551c7680">GLM_ALIGNED_TYPEDEF</a> (f32mat2x4, aligned_f32mat2x4, 16)</td></tr>
<tr class="memdesc:gaa52e5732daa62851627021ad551c7680"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 2x4 matrix.  <a href="a00364.html#gaa52e5732daa62851627021ad551c7680">More...</a><br /></td></tr>
<tr class="separator:gaa52e5732daa62851627021ad551c7680"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac09663c42566bcb58d23c6781ac4e85a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gac09663c42566bcb58d23c6781ac4e85a">GLM_ALIGNED_TYPEDEF</a> (f32mat3x2, aligned_f32mat3x2, 16)</td></tr>
<tr class="memdesc:gac09663c42566bcb58d23c6781ac4e85a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 3x2 matrix.  <a href="a00364.html#gac09663c42566bcb58d23c6781ac4e85a">More...</a><br /></td></tr>
<tr class="separator:gac09663c42566bcb58d23c6781ac4e85a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3f510999e59e1b309113e1d561162b29"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga3f510999e59e1b309113e1d561162b29">GLM_ALIGNED_TYPEDEF</a> (f32mat3x3, aligned_f32mat3x3, 16)</td></tr>
<tr class="memdesc:ga3f510999e59e1b309113e1d561162b29"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 3x3 matrix.  <a href="a00364.html#ga3f510999e59e1b309113e1d561162b29">More...</a><br /></td></tr>
<tr class="separator:ga3f510999e59e1b309113e1d561162b29"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2c9c94f0c89cd71ce56551db6cf4aaec"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga2c9c94f0c89cd71ce56551db6cf4aaec">GLM_ALIGNED_TYPEDEF</a> (f32mat3x4, aligned_f32mat3x4, 16)</td></tr>
<tr class="memdesc:ga2c9c94f0c89cd71ce56551db6cf4aaec"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 3x4 matrix.  <a href="a00364.html#ga2c9c94f0c89cd71ce56551db6cf4aaec">More...</a><br /></td></tr>
<tr class="separator:ga2c9c94f0c89cd71ce56551db6cf4aaec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga99ce8274c750fbfdf0e70c95946a2875"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga99ce8274c750fbfdf0e70c95946a2875">GLM_ALIGNED_TYPEDEF</a> (f32mat4x2, aligned_f32mat4x2, 16)</td></tr>
<tr class="memdesc:ga99ce8274c750fbfdf0e70c95946a2875"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 4x2 matrix.  <a href="a00364.html#ga99ce8274c750fbfdf0e70c95946a2875">More...</a><br /></td></tr>
<tr class="separator:ga99ce8274c750fbfdf0e70c95946a2875"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9476ef66790239df53dbe66f3989c3b5"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga9476ef66790239df53dbe66f3989c3b5">GLM_ALIGNED_TYPEDEF</a> (f32mat4x3, aligned_f32mat4x3, 16)</td></tr>
<tr class="memdesc:ga9476ef66790239df53dbe66f3989c3b5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 4x3 matrix.  <a href="a00364.html#ga9476ef66790239df53dbe66f3989c3b5">More...</a><br /></td></tr>
<tr class="separator:ga9476ef66790239df53dbe66f3989c3b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacc429b3b0b49921e12713b6d31e14e1d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gacc429b3b0b49921e12713b6d31e14e1d">GLM_ALIGNED_TYPEDEF</a> (f32mat4x4, aligned_f32mat4x4, 16)</td></tr>
<tr class="memdesc:gacc429b3b0b49921e12713b6d31e14e1d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned 4x4 matrix.  <a href="a00364.html#gacc429b3b0b49921e12713b6d31e14e1d">More...</a><br /></td></tr>
<tr class="separator:gacc429b3b0b49921e12713b6d31e14e1d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga88f6c6fa06e6e64479763e69444669cf"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga88f6c6fa06e6e64479763e69444669cf">GLM_ALIGNED_TYPEDEF</a> (f64mat2x2, aligned_f64mat2, 32)</td></tr>
<tr class="memdesc:ga88f6c6fa06e6e64479763e69444669cf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 1x1 matrix.  <a href="a00364.html#ga88f6c6fa06e6e64479763e69444669cf">More...</a><br /></td></tr>
<tr class="separator:ga88f6c6fa06e6e64479763e69444669cf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaae8e4639c991e64754145ab8e4c32083"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaae8e4639c991e64754145ab8e4c32083">GLM_ALIGNED_TYPEDEF</a> (f64mat3x3, aligned_f64mat3, 32)</td></tr>
<tr class="memdesc:gaae8e4639c991e64754145ab8e4c32083"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 3x3 matrix.  <a href="a00364.html#gaae8e4639c991e64754145ab8e4c32083">More...</a><br /></td></tr>
<tr class="separator:gaae8e4639c991e64754145ab8e4c32083"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6e9094f3feb3b5b49d0f83683a101fde"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga6e9094f3feb3b5b49d0f83683a101fde">GLM_ALIGNED_TYPEDEF</a> (f64mat4x4, aligned_f64mat4, 32)</td></tr>
<tr class="memdesc:ga6e9094f3feb3b5b49d0f83683a101fde"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 4x4 matrix.  <a href="a00364.html#ga6e9094f3feb3b5b49d0f83683a101fde">More...</a><br /></td></tr>
<tr class="separator:ga6e9094f3feb3b5b49d0f83683a101fde"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadbd2c639c03de1c3e9591b5a39f65559"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gadbd2c639c03de1c3e9591b5a39f65559">GLM_ALIGNED_TYPEDEF</a> (f64mat2x2, aligned_f64mat2x2, 32)</td></tr>
<tr class="memdesc:gadbd2c639c03de1c3e9591b5a39f65559"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 1x1 matrix.  <a href="a00364.html#gadbd2c639c03de1c3e9591b5a39f65559">More...</a><br /></td></tr>
<tr class="separator:gadbd2c639c03de1c3e9591b5a39f65559"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab059d7b9fe2094acc563b7223987499f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gab059d7b9fe2094acc563b7223987499f">GLM_ALIGNED_TYPEDEF</a> (f64mat2x3, aligned_f64mat2x3, 32)</td></tr>
<tr class="memdesc:gab059d7b9fe2094acc563b7223987499f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 2x3 matrix.  <a href="a00364.html#gab059d7b9fe2094acc563b7223987499f">More...</a><br /></td></tr>
<tr class="separator:gab059d7b9fe2094acc563b7223987499f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabbc811d1c52ed2b8cfcaff1378f75c69"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gabbc811d1c52ed2b8cfcaff1378f75c69">GLM_ALIGNED_TYPEDEF</a> (f64mat2x4, aligned_f64mat2x4, 32)</td></tr>
<tr class="memdesc:gabbc811d1c52ed2b8cfcaff1378f75c69"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 2x4 matrix.  <a href="a00364.html#gabbc811d1c52ed2b8cfcaff1378f75c69">More...</a><br /></td></tr>
<tr class="separator:gabbc811d1c52ed2b8cfcaff1378f75c69"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9ddf5212777734d2fd841a84439f3bdf"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga9ddf5212777734d2fd841a84439f3bdf">GLM_ALIGNED_TYPEDEF</a> (f64mat3x2, aligned_f64mat3x2, 32)</td></tr>
<tr class="memdesc:ga9ddf5212777734d2fd841a84439f3bdf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 3x2 matrix.  <a href="a00364.html#ga9ddf5212777734d2fd841a84439f3bdf">More...</a><br /></td></tr>
<tr class="separator:ga9ddf5212777734d2fd841a84439f3bdf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad1dda32ed09f94bfcf0a7d8edfb6cf13"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gad1dda32ed09f94bfcf0a7d8edfb6cf13">GLM_ALIGNED_TYPEDEF</a> (f64mat3x3, aligned_f64mat3x3, 32)</td></tr>
<tr class="memdesc:gad1dda32ed09f94bfcf0a7d8edfb6cf13"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 3x3 matrix.  <a href="a00364.html#gad1dda32ed09f94bfcf0a7d8edfb6cf13">More...</a><br /></td></tr>
<tr class="separator:gad1dda32ed09f94bfcf0a7d8edfb6cf13"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5875e0fa72f07e271e7931811cbbf31a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga5875e0fa72f07e271e7931811cbbf31a">GLM_ALIGNED_TYPEDEF</a> (f64mat3x4, aligned_f64mat3x4, 32)</td></tr>
<tr class="memdesc:ga5875e0fa72f07e271e7931811cbbf31a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 3x4 matrix.  <a href="a00364.html#ga5875e0fa72f07e271e7931811cbbf31a">More...</a><br /></td></tr>
<tr class="separator:ga5875e0fa72f07e271e7931811cbbf31a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga41e82cd6ac07f912ba2a2d45799dcf0d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga41e82cd6ac07f912ba2a2d45799dcf0d">GLM_ALIGNED_TYPEDEF</a> (f64mat4x2, aligned_f64mat4x2, 32)</td></tr>
<tr class="memdesc:ga41e82cd6ac07f912ba2a2d45799dcf0d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 4x2 matrix.  <a href="a00364.html#ga41e82cd6ac07f912ba2a2d45799dcf0d">More...</a><br /></td></tr>
<tr class="separator:ga41e82cd6ac07f912ba2a2d45799dcf0d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0892638d6ba773043b3d63d1d092622e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga0892638d6ba773043b3d63d1d092622e">GLM_ALIGNED_TYPEDEF</a> (f64mat4x3, aligned_f64mat4x3, 32)</td></tr>
<tr class="memdesc:ga0892638d6ba773043b3d63d1d092622e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 4x3 matrix.  <a href="a00364.html#ga0892638d6ba773043b3d63d1d092622e">More...</a><br /></td></tr>
<tr class="separator:ga0892638d6ba773043b3d63d1d092622e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga912a16432608b822f1e13607529934c1"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga912a16432608b822f1e13607529934c1">GLM_ALIGNED_TYPEDEF</a> (f64mat4x4, aligned_f64mat4x4, 32)</td></tr>
<tr class="memdesc:ga912a16432608b822f1e13607529934c1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned 4x4 matrix.  <a href="a00364.html#ga912a16432608b822f1e13607529934c1">More...</a><br /></td></tr>
<tr class="separator:ga912a16432608b822f1e13607529934c1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafd945a8ea86b042aba410e0560df9a3d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gafd945a8ea86b042aba410e0560df9a3d">GLM_ALIGNED_TYPEDEF</a> (quat, aligned_quat, 16)</td></tr>
<tr class="memdesc:gafd945a8ea86b042aba410e0560df9a3d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned quaternion.  <a href="a00364.html#gafd945a8ea86b042aba410e0560df9a3d">More...</a><br /></td></tr>
<tr class="separator:gafd945a8ea86b042aba410e0560df9a3d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga19c2ba545d1f2f36bcb7b60c9a228622"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga19c2ba545d1f2f36bcb7b60c9a228622">GLM_ALIGNED_TYPEDEF</a> (quat, aligned_fquat, 16)</td></tr>
<tr class="memdesc:ga19c2ba545d1f2f36bcb7b60c9a228622"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned quaternion.  <a href="a00364.html#ga19c2ba545d1f2f36bcb7b60c9a228622">More...</a><br /></td></tr>
<tr class="separator:ga19c2ba545d1f2f36bcb7b60c9a228622"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaabc28c84a3288b697605d4688686f9a9"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#gaabc28c84a3288b697605d4688686f9a9">GLM_ALIGNED_TYPEDEF</a> (dquat, aligned_dquat, 32)</td></tr>
<tr class="memdesc:gaabc28c84a3288b697605d4688686f9a9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned quaternion.  <a href="a00364.html#gaabc28c84a3288b697605d4688686f9a9">More...</a><br /></td></tr>
<tr class="separator:gaabc28c84a3288b697605d4688686f9a9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1ed8aeb5ca67fade269a46105f1bf273"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga1ed8aeb5ca67fade269a46105f1bf273">GLM_ALIGNED_TYPEDEF</a> (f32quat, aligned_f32quat, 16)</td></tr>
<tr class="memdesc:ga1ed8aeb5ca67fade269a46105f1bf273"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-qualifier floating-point aligned quaternion.  <a href="a00364.html#ga1ed8aeb5ca67fade269a46105f1bf273">More...</a><br /></td></tr>
<tr class="separator:ga1ed8aeb5ca67fade269a46105f1bf273"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga95cc03b8b475993fa50e05e38e203303"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a> (f64quat, aligned_f64quat, 32)</td></tr>
<tr class="memdesc:ga95cc03b8b475993fa50e05e38e203303"><td class="mdescLeft">&#160;</td><td class="mdescRight">Double-qualifier floating-point aligned quaternion.  <a href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">More...</a><br /></td></tr>
<tr class="separator:ga95cc03b8b475993fa50e05e38e203303"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00162.html" title="GLM_GTX_type_aligned ">glm/gtx/type_aligned.hpp</a>&gt; to use the features of this extension. </p>
<p>Defines aligned types. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gab5cd5c5fad228b25c782084f1cc30114"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga760bcf26fdb23a2c3ecad3c928a19ae6">lowp_int8</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_int8&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 8 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5bb5dd895ef625c1b113f2cf400186b0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga698e36b01167fc0f037889334dce8def">lowp_int16</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_int16&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 16 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gac6efa54cf7c6c86f7158922abdb1a430"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga864aabca5f3296e176e0c3ed9cc16b02">lowp_int32</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_int32&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 32 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga6612eb77c8607048e7552279a11eeb5f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaf645b1a60203b39c0207baff5e3d8c3c">lowp_int64</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_int64&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 64 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga7ddc1848ff2223026db8968ce0c97497"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga119c41d73fe9977358174eb3ac1035a3">lowp_int8_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_int8_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 8 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga22240dd9458b0f8c11fbcc4f48714f68"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga8b2cd8d31eb345b2d641d9261c38db1a">lowp_int16_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_int16_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 16 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8130ea381d76a2cc34a93ccbb6cf487d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga0350631d35ff800e6133ac6243b13cbc">lowp_int32_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_int32_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 32 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga7ccb60f3215d293fd62b33b31ed0e7be"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaebf341fc4a5be233f7dde962c2e33847">lowp_int64_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_int64_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 64 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gac20d508d2ef5cc95ad3daf083c57ec2a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga552a6bde5e75984efb0f863278da2e54">lowp_i8</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_i8&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 8 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga50257b48069a31d0c8d9c1f644d267de"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga392b673fd10847bfb78fb808c6cf8ff7">lowp_i16</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_i16&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 16 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaa07e98e67b7a3435c0746018c7a2a839"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga7ff73a45cea9613ebf1a9fad0b9f82ac">lowp_i32</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_i32&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 32 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga62601fc6f8ca298b77285bedf03faffd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga354736e0c645099cd44c42fb2f87c2b8">lowp_i64</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_i64&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 64 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gac8cff825951aeb54dd846037113c72db"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga6fbd69cbdaa44345bff923a2cf63de7e">mediump_int8</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_int8&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 8 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga78f443d88f438575a62b5df497cdf66b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gadff3608baa4b5bd3ed28f95c1c2c345d">mediump_int16</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_int16&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 16 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga0680cd3b5d4e8006985fb41a4f9b57af"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga5244cef85d6e870e240c76428a262ae8">mediump_int32</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_int32&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 32 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gad9e5babb1dd3e3531b42c37bf25dd951"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga7b968f2b86a0442a89c7359171e1d866">mediump_int64</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_int64&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 64 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga353fd9fa8a9ad952fcabd0d53ad9a6dd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga6d7b3789ecb932c26430009478cac7ae">mediump_int8_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_int8_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 8 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2196442c0e5c5e8c77842de388c42521"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga80e72fe94c88498537e8158ba7591c54">mediump_int16_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_int16_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 16 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga1284488189daf897cf095c5eefad9744"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga26fc7ced1ad7ca5024f1c973c8dc9180">mediump_int32_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_int32_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 32 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga73fdc86a539808af58808b7c60a1c4d8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gac3bc41bcac61d1ba8f02a6f68ce23f64">mediump_int64_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_int64_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 64 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gafafeea923e1983262c972e2b83922d3b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gacf1ded173e1e2d049c511d095b259e21">mediump_i8</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_i8&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 8 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga4b35ca5fe8f55c9d2fe54fdb8d8896f4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga62a17cddeb4dffb4e18fe3aea23f051a">mediump_i16</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_i16&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 16 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga63b882e29170d428463d99c3d630acc6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaf5e94bf2a20af7601787c154751dc2e1">mediump_i32</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_i32&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 32 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8b20507bb048c1edea2d441cc953e6f0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga3ebcb1f6d8d8387253de8bccb058d77f">mediump_i64</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_i64&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 64 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga56c5ca60813027b603c7b61425a0479d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gad0549c902a96a7164e4ac858d5f39dbf">highp_int8</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_int8&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 8 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga7a751b3aff24c0259f4a7357c2969089"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga5fde0fa4a3852a9dd5d637a92ee74718">highp_int16</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_int16&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 16 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga70cd2144351c556469ee6119e59971fc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga84ed04b4e0de18c977e932d617e7c223">highp_int32</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_int32&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 32 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga46bbf08dc004d8c433041e0b5018a5d3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga226a8d52b4e3f77aaa6231135e886aac">highp_int64</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_int64&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 64 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gab3e10c77a20d1abad2de1c561c7a5c18"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga1085c50dd8fbeb5e7e609b1c127492a5">highp_int8_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_int8_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 8 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga968f30319ebeaca9ebcd3a25a8e139fb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gacaea06d0a79ef3172e887a7a6ba434ff">highp_int16_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_int16_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 16 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaae773c28e6390c6aa76f5b678b7098a3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga2c71c8bd9e2fe7d2e93ca250d8b6157f">highp_int32_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_int32_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 32 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga790cfff1ca39d0ed696ffed980809311"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga73c6abb280a45feeff60f9accaee91f3">highp_int64_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_int64_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 64 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8265b91eb23c120a9b0c3e381bc37b96"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gacb88796f2d08ef253d0345aff20c3aee">highp_i8</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_i8&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 8 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae6d384de17588d8edb894fbe06e0d410"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga0336abc2604dd2c20c30e036454b64f8">highp_i16</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_i16&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 16 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga9c8172b745ee03fc5b2b91c350c2922f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga727675ac6b5d2fc699520e0059735e25">highp_i32</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_i32&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 32 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga77e0dff12aa4020ddc3f8cabbea7b2e6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gac25db6d2b1e2a0f351b77ba3409ac4cd">highp_i64</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_i64&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 64 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gabd82b9faa9d4d618dbbe0fc8a1efee63"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00260.html#ga1b956fe1df85f3c132b21edb4e116458">int8</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_int8&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 8 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga285649744560be21000cfd81bbb5d507"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00260.html#ga259fa4834387bd68627ddf37bb3ebdb9">int16</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_int16&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 16 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga07732da630b2deda428ce95c0ecaf3ff"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00260.html#ga43d43196463bde49cb067f5c20ab8481">int32</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_int32&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 32 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga1a8da2a8c51f69c07a2e7f473aa420f4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">int64</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_int64&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 64 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga848aedf13e2d9738acf0bb482c590174"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga4bf09d8838a86866b39ee6e109341645">int8_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_int8_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 8 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gafd2803d39049dd45a37a63931e25d943"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gae8f5e3e964ca2ae240adc2c0d74adede">int16_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_int16_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 16 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae553b33349d6da832cf0724f1e024094"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga042ef09ff2f0cb24a36f541bcb3a3710">int32_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_int32_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 32 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga16d223a2b3409e812e1d3bd87f0e9e5c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga322a7d7d2c2c68994dc872a33de63c61">int64_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_int64_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 64 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2de065d2ddfdb366bcd0febca79ae2ad"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga302ec977b0c0c3ea245b6c9275495355">i8</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_i8&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 8 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gabd786bdc20a11c8cb05c92c8212e28d3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga3ab5fe184343d394fb6c2723c3ee3699">i16</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_i16&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 16 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gad4aefe56691cdb640c72f0d46d3fb532"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga96faea43ac5f875d2d3ffbf8d213e3eb">i32</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_i32&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 32 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8fe9745f7de24a8394518152ff9fccdc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gadb997e409103d4da18abd837e636a496">i64</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_i64&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 64 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaaad735483450099f7f882d4e3a3569bd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00272.html#gaedd0562c2e77714929d7723a7e2e0dba">ivec1</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00303.html#ga76298aed82a439063c3d55980c84aa0b">aligned_ivec1</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 32 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gac7b6f823802edbd6edbaf70ea25bf068"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#ga6f9269106d91b2d2b91bcf27cd5f5560">ivec2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00303.html#gae4f38fd2c86cee6940986197777b3ca4">aligned_ivec2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 32 bit signed integer aligned vector of 2 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga3e235bcd2b8029613f25b8d40a2d3ef7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#gad0d784d8eee201aca362484d2daee46c">ivec3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00303.html#ga32794322d294e5ace7fed4a61896f270">aligned_ivec3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 32 bit signed integer aligned vector of 3 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga50d8a9523968c77f8325b4c9bfbff41e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#ga5abb4603dae0ce58c595e66d9123d812">ivec4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00303.html#ga7f79eae5927c9033d84617e49f6f34e4">aligned_ivec4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 32 bit signed integer aligned vector of 4 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga9ec20fdfb729c702032da9378c79679f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga7e80d927ff0a3861ced68dfff8a4020b">i8vec1</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_i8vec1&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 8 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga25b3fe1d9e8d0a5e86c1949c1acd8131"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gad06935764d78f43f9d542c784c2212ec">i8vec2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_i8vec2&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 8 bit signed integer aligned vector of 2 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2958f907719d94d8109b562540c910e2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga5a08d36cf7917cd19d081a603d0eae3e">i8vec3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_i8vec3&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 8 bit signed integer aligned vector of 3 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga1fe6fc032a978f1c845fac9aa0668714"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga4177a44206121dabc8c4ff1c0f544574">i8vec4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_i8vec4&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 8 bit signed integer aligned vector of 4 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaa4161e7a496dc96972254143fe873e55"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gafe730798732aa7b0647096a004db1b1c">i16vec1</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_i16vec1&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 16 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga9d7cb211ccda69b1c22ddeeb0f3e7aba"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga2996630ba7b10535af8e065cf326f761">i16vec2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_i16vec2&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 16 bit signed integer aligned vector of 2 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaaee91dd2ab34423bcc11072ef6bd0f02"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gae9c90a867a6026b1f6eab00456f3fb8b">i16vec3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_i16vec3&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 16 bit signed integer aligned vector of 3 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga49f047ccaa8b31fad9f26c67bf9b3510"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga550831bfc26d1e0101c1cb3d79938c06">i16vec4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_i16vec4&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 16 bit signed integer aligned vector of 4 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga904e9c2436bb099397c0823506a0771f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga54b8a4e0f5a7203a821bf8e9c1265bcf">i32vec1</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_i32vec1&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 32 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaf90651cf2f5e7ee2b11cfdc5a6749534"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga8b44026374982dcd1e52d22bac99247e">i32vec2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_i32vec2&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 32 bit signed integer aligned vector of 2 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga7354a4ead8cb17868aec36b9c30d6010"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga7f526b5cccef126a2ebcf9bdd890394e">i32vec3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_i32vec3&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 32 bit signed integer aligned vector of 3 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gad2ecbdea18732163e2636e27b37981ee"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga866a05905c49912309ed1fa5f5980e61">i32vec4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_i32vec4&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 32 bit signed integer aligned vector of 4 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga965b1c9aa1800e93d4abc2eb2b5afcbf"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga2b65767f8b5aed1bd1cf86c541662b50">i64vec1</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_i64vec1&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 64 bit signed integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga1f9e9c2ea2768675dff9bae5cde2d829"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga48310188e1d0c616bf8d78c92447523b">i64vec2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_i64vec2&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 64 bit signed integer aligned vector of 2 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gad77c317b7d942322cd5be4c8127b3187"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga667948cfe6fb3d6606c750729ec49f77">i64vec3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_i64vec3&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">32&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 64 bit signed integer aligned vector of 3 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga716f8ea809bdb11b5b542d8b71aeb04f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaa4e31c3d9de067029efeb161a44b0232">i64vec4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_i64vec4&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">32&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 64 bit signed integer aligned vector of 4 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gad46f8e9082d5878b1bc04f9c1471cdaa"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaf49470869e9be2c059629b250619804e">lowp_uint8</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_uint8&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 8 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga1246094581af624aca6c7499aaabf801"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gad68bfd9f881856fc863a6ebca0b67f78">lowp_uint16</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_uint16&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 16 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga7a5009a1d0196bbf21dd7518f61f0249"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaa6a5b461bbf5fe20982472aa51896d4b">lowp_uint32</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_uint32&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 32 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga45213fd18b3bb1df391671afefe4d1e7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaa212b805736a759998e312cbdd550fae">lowp_uint64</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_uint64&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 64 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga0ba26b4e3fd9ecbc25358efd68d8a4ca"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga667b2ece2b258be898812dc2177995d1">lowp_uint8_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_uint8_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 8 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaf2b58f5fb6d4ec8ce7b76221d3af43e1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga91c4815f93177eb423362fd296a87e9f">lowp_uint16_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_uint16_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 16 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gadc246401847dcba155f0699425e49dcd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaf1b735b4b1145174f4e4167d13778f9b">lowp_uint32_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_uint32_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 32 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaace64bddf51a9def01498da9a94fb01c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga8dd3a3281ae5c970ffe0c41d538aa153">lowp_uint64_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_uint64_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 64 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gad7bb97c29d664bd86ffb1bed4abc5534"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga1b09f03da7ac43055c68a349d5445083">lowp_u8</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_u8&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 8 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga404bba7785130e0b1384d695a9450b28"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga504ce1631cb2ac02fcf1d44d8c2aa126">lowp_u16</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_u16&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 16 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga31ba41fd896257536958ec6080203d2a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga4f072ada9552e1e480bbb3b1acde5250">lowp_u32</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_u32&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 32 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gacca5f13627f57b3505676e40a6e43e5e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga30069d1f02b19599cbfadf98c23ac6ed">lowp_u64</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_lowp_u64&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low qualifier 64 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5faf1d3e70bf33174dd7f3d01d5b883b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga1fa92a233b9110861cdbc8c2ccf0b5a3">mediump_uint8</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_uint8&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 8 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga727e2bf2c433bb3b0182605860a48363"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga2885a6c89916911e418c06bb76b9bdbb">mediump_uint16</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_uint16&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 16 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga12566ca66d5962dadb4a5eb4c74e891e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga34dd5ec1988c443bae80f1b20a8ade5f">mediump_uint32</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_uint32&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 32 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga7b66a97a8acaa35c5a377b947318c6bc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga30652709815ad9404272a31957daa59e">mediump_uint64</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_uint64&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 64 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaa9cde002439b74fa66120a16a9f55fcc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gadfe65c78231039e90507770db50c98c7">mediump_uint8_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_uint8_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 8 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga1ca98c67f7d1e975f7c5202f1da1df1f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga3963b1050fc65a383ee28e3f827b6e3e">mediump_uint16_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_uint16_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 16 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga1dc8bc6199d785f235576948d80a597c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaf4dae276fd29623950de14a6ca2586b5">mediump_uint32_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_uint32_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 32 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gad14a0f2ec93519682b73d70b8e401d81"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga9b170dd4a8f38448a2dc93987c7875e9">mediump_uint64_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_uint64_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 64 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gada8b996eb6526dc1ead813bd49539d1b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gad1213a22bbb9e4107f07eaa4956f8281">mediump_u8</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_u8&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 8 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga28948f6bfb52b42deb9d73ae1ea8d8b0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga9df98857be695d5a30cb30f5bfa38a80">mediump_u16</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_u16&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 16 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gad6a7c0b5630f89d3f1c5b4ef2919bb4c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga1bd0e914158bf03135f8a317de6debe9">mediump_u32</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_u32&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 32 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaa0fc531cbaa972ac3a0b86d21ef4a7fa"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga2af9490085ae3bdf36a544e9dd073610">mediump_u64</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_mediump_u64&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium qualifier 64 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga0ee829f7b754b262bbfe6317c0d678ac"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga97432f9979e73e66567361fd01e4cffb">highp_uint8</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_uint8&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 8 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga447848a817a626cae08cedc9778b331c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga746dc6da204f5622e395f492997dbf57">highp_uint16</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_uint16&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 16 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga6027ae13b2734f542a6e7beee11b8820"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga256b12b650c3f2fb86878fd1c5db8bc3">highp_uint32</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_uint32&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 32 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2aca46c8608c95ef991ee4c332acde5f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaa38d732f5d4a7bc42a1b43b9d3c141ce">highp_uint64</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_uint64&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 64 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaff50b10dd1c48be324fdaffd18e2c7ea"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gac4e00a26a2adb5f2c0a7096810df29e5">highp_uint8_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_uint8_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 8 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga9fc4421dbb833d5461e6d4e59dcfde55"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gacf54c3330ef60aa3d16cb676c7bcb8c7">highp_uint16_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_uint16_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 16 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga329f1e2b94b33ba5e3918197030bcf03"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gae978599c9711ac263ba732d4ac225b0e">highp_uint32_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_uint32_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 32 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga71e646f7e301aa422328194162c9c998"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaa46172d7dc1c7ffe3e78107ff88adf08">highp_uint64_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_uint64_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 64 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8942e09f479489441a7a5004c6d8cb66"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gacd1259f3a9e8d2a9df5be2d74322ef9c">highp_u8</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_u8&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 8 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaab32497d6e4db16ee439dbedd64c5865"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga8e62c883d13f47015f3b70ed88751369">highp_u16</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_u16&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 16 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaaadbb34952eca8e3d7fe122c3e167742"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga7a6f1929464dcc680b16381a4ee5f2cf">highp_u32</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_u32&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 32 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga92024d27c74a3650afb55ec8e024ed25"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga0c181fdf06a309691999926b6690c969">highp_u64</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_highp_u64&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High qualifier 64 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gabde1d0b4072df35453db76075ab896a6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#gadde6aaee8457bee49c2a92621fe22b79">uint8</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_uint8&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 8 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga06c296c9e398b294c8c9dd2a7693dcbb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">uint16</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_uint16&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 16 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gacf1744488c96ebd33c9f36ad33b2010a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga1134b580f8da4de94ca6b1de4d37975e">uint32</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_uint32&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 32 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga3328061a64c20ba59d5f9da24c2cd059"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_uint64&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 64 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaf6ced36f13bae57f377bafa6f5fcc299"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga28d97808322d3c92186e4a0c067d7e8e">uint8_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_uint8_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 8 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gafbc7fb7847bfc78a339d1d371c915c73"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga91f91f411080c37730856ff5887f5bcf">uint16_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_uint16_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 16 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaa86bc56a73fd8120b1121b5f5e6245ae"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga2171d9dc1fefb1c82e2817f45b622eac">uint32_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_uint32_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 32 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga68c0b9e669060d0eb5ab8c3ddeb483d8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga3999d3e7ff22025c16ddb601e14dfdee">uint64_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_uint64_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 64 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga4f3bab577daf3343e99cc005134bce86"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaecc7082561fc9028b844b6cf3d305d36">u8</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_u8&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 8 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga13a2391339d0790d43b76d00a7611c4f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaa2d7acc0adb536fab71fe261232a40ff">u16</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_u16&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 16 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga197570e03acbc3d18ab698e342971e8f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga8165913e068444f7842302d40ba897b9">u32</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_u32&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 32 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga0f033b21e145a1faa32c62ede5878993"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaf3f312156984c365e9f65620354da70b">u64</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_u64&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 64 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga509af83527f5cd512e9a7873590663aa"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00276.html#gac3bdd96183d23876c58a1424585fefe7">uvec1</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00303.html#ga1ff8ed402c93d280ff0597c1c5e7c548">aligned_uvec1</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 32 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga94e86186978c502c6dc0c0d9c4a30679"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#ga2f6d9ec3ae14813ade37d6aee3715fdb">uvec2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00303.html#ga074137e3be58528d67041c223d49f398">aligned_uvec2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 32 bit unsigned integer aligned vector of 2 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5cec574686a7f3c8ed24bb195c5e2d0a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#ga3d3e55874babd4bf93baa7bbc83ae418">uvec3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00303.html#ga2a8d9c3046f89d854eb758adfa0811c0">aligned_uvec3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 32 bit unsigned integer aligned vector of 3 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga47edfdcee9c89b1ebdaf20450323b1d4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#gaa57e96bb337867329d5f43bcc27c1095">uvec4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00303.html#gabf842c45eea186170c267a328e3f3b7d">aligned_uvec4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 32 bit unsigned integer aligned vector of 4 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5611d6718e3a00096918a64192e73a45"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga29b349e037f0b24320b4548a143daee2">u8vec1</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_u8vec1&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">1&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 8 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga19837e6f72b60d994a805ef564c6c326"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga518b8d948a6b4ddb72f84d5c3b7b6611">u8vec2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_u8vec2&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 8 bit unsigned integer aligned vector of 2 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga9740cf8e34f068049b42a2753f9601c2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga7c5706f6bbe5282e5598acf7e7b377e2">u8vec3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_u8vec3&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 8 bit unsigned integer aligned vector of 3 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8b8588bb221448f5541a858903822a57"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga20779a61de2fd526a17f12fe53ec46b1">u8vec4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_u8vec4&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 8 bit unsigned integer aligned vector of 4 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga991abe990c16de26b2129d6bc2f4c051"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga08c05ba8ffb19f5d14ab584e1e9e9ee5">u16vec1</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_u16vec1&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">2&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 16 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gac01bb9fc32a1cd76c2b80d030f71df4c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga2a78447eb9d66a114b193f4a25899c16">u16vec2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_u16vec2&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 16 bit unsigned integer aligned vector of 2 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga09540dbca093793a36a8997e0d4bee77"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga1c522ca821c27b862fe51cf4024b064b">u16vec3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_u16vec3&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 16 bit unsigned integer aligned vector of 3 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaecafb5996f5a44f57e34d29c8670741e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga529496d75775fb656a07993ea9af2450">u16vec4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_u16vec4&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 16 bit unsigned integer aligned vector of 4 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gac6b161a04d2f8408fe1c9d857e8daac0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gae627372cfd5f20dd87db490387b71195">u32vec1</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_u32vec1&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 32 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga1fa0dfc8feb0fa17dab2acd43e05342b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga2a266e46ee218d0c680f12b35c500cc0">u32vec2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_u32vec2&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 32 bit unsigned integer aligned vector of 2 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga0019500abbfa9c66eff61ca75eaaed94"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gae267358ff2a41d156d97f5762630235a">u32vec3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_u32vec3&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 32 bit unsigned integer aligned vector of 3 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga14fd29d01dae7b08a04e9facbcc18824"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga31cef34e4cd04840c54741ff2f7005f0">u32vec4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_u32vec4&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 32 bit unsigned integer aligned vector of 4 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gab253845f534a67136f9619843cade903"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaf09f3ca4b671a4a4f84505eb4cc865fd">u64vec1</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_u64vec1&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 64 bit unsigned integer aligned scalar type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga929427a7627940cdf3304f9c050b677d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaef3824ed4fe435a019c5b9dddf53fec5">u64vec2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_u64vec2&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 64 bit unsigned integer aligned vector of 2 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae373b6c04fdf9879f33d63e6949c037e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga489b89ba93d4f7b3934df78debc52276">u64vec3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_u64vec3&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">32&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 64 bit unsigned integer aligned vector of 3 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga53a8a03dca2015baec4584f45b8e9cdc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga3945dd6515d4498cb603e65ff867ab03">u64vec4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_u64vec4&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">32&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default qualifier 64 bit unsigned integer aligned vector of 4 components type. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gab3301bae94ef5bf59fbdd9a24e7d2a01"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaacdc525d6f7bddb3ae95d5c311bd06a1">float32</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_float32&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>32 bit single-qualifier floating-point aligned scalar. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gada9b0bea273d3ae0286f891533b9568f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaa4947bc8b47c72fceea9bda730ecf603">float32_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_float32_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>32 bit single-qualifier floating-point aligned scalar. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gadbce23b9f23d77bb3884e289a574ebd5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaacdc525d6f7bddb3ae95d5c311bd06a1">float32</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f32&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>32 bit single-qualifier floating-point aligned scalar. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga75930684ff2233171c573e603f216162"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga232fad1b0d6dcc7c16aabde98b2e2a80">float64</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_float64&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>64 bit double-qualifier floating-point aligned scalar. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga6e3a2d83b131336219a0f4c7cbba2a48"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga728366fef72cd96f0a5fa6429f05469e">float64_t</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_float64_t&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>64 bit double-qualifier floating-point aligned scalar. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaa4deaa0dea930c393d55e7a4352b0a20"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga232fad1b0d6dcc7c16aabde98b2e2a80">float64</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f64&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>64 bit double-qualifier floating-point aligned scalar. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga81bc497b2bfc6f80bab690c6ee28f0f9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00270.html#gadfc071d934d8dae7955a1d530a3cf656">vec1</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00303.html#ga05e6d4c908965d04191c2070a8d0a65e">aligned_vec1</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned vector of 1 component. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gada3e8f783e9d4b90006695a16c39d4d4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">vec2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00303.html#ga0682462f8096a226773e20fac993cde5">aligned_vec2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned vector of 2 components. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gab8d081fac3a38d6f55fa552f32168d32"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#ga9c3019b13faf179e4ad3626ea66df334">vec3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00303.html#ga7cf643b66664e0cd3c48759ae66c2bd0">aligned_vec3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned vector of 3 components. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga12fe7b9769c964c5b48dcfd8b7f40198"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00303.html#ga85d89e83cb8137e1be1446de8c3b643a">aligned_vec4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned vector of 4 components. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaefab04611c7f8fe1fd9be3071efea6cc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga98b9ed43cf8c5cf1d354b23c7df9119f">fvec1</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_fvec1&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned vector of 1 component. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2543c05ba19b3bd19d45b1227390c5b4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga24273aa02abaecaab7f160bac437a339">fvec2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_fvec2&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned vector of 2 components. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga009afd727fd657ef33a18754d6d28f60"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga89930533646b30d021759298aa6bf04a">fvec3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_fvec3&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned vector of 3 components. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2f26177e74bfb301a3d0e02ec3c3ef53"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga713c796c54875cf4092d42ff9d9096b0">fvec4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_fvec4&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned vector of 4 components. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga309f495a1d6b75ddf195b674b65cb1e4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga701f32ab5b3fb06996b41f5c0d643805">f32vec1</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f32vec1&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">4&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned vector of 1 component. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5e185865a2217d0cd47187644683a8c3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga5d6c70e080409a76a257dc55bd8ea2c8">f32vec2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f32vec2&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned vector of 2 components. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gade4458b27b039b9ca34f8ec049f3115a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaea5c4518e175162e306d2c2b5ef5ac79">f32vec3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f32vec3&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned vector of 3 components. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2e8a12c5e6a9c4ae4ddaeda1d1cffe3b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga31c6ca0e074a44007f49a9a3720b18c8">f32vec4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f32vec4&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned vector of 4 components. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga3e0f35fa0c626285a8bad41707e7316c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00268.html#ga6221af17edc2d4477a4583d2cd53e569">dvec1</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00303.html#ga4974f46ae5a19415d91316960a53617a">aligned_dvec1</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Double-qualifier floating-point aligned vector of 1 component. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga78bfec2f185d1d365ea0a9ef1e3d45b8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#ga8b09c71aaac7da7867ae58377fe219a8">dvec2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00303.html#ga18d859f87122b2b3b2992ffe86dbebc0">aligned_dvec2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Double-qualifier floating-point aligned vector of 2 components. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga01fe6fee6db5df580b6724a7e681f069"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#ga5b83ae3d0fdec519c038e4d2cf967cf0">dvec3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00303.html#gaa37869eea77d28419b2fb0ff70b69bf0">aligned_dvec3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">32&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Double-qualifier floating-point aligned vector of 3 components. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga687d5b8f551d5af32425c0b2fba15e99"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#ga57debab5d98ce618f7b2a97fe26eb3ac">dvec4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00303.html#ga8a9f0a4795ccc442fa9901845026f9f5">aligned_dvec4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">32&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Double-qualifier floating-point aligned vector of 4 components. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8e842371d46842ff8f1813419ba49d0f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gade502df1ce14f837fae7f60a03ddb9b0">f64vec1</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f64vec1&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">8&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Double-qualifier floating-point aligned vector of 1 component. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga32814aa0f19316b43134fc25f2aad2b9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gadc4e1594f9555d919131ee02b17822a2">f64vec2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f64vec2&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Double-qualifier floating-point aligned vector of 2 components. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaf3d3bbc1e93909b689123b085e177a14"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaa7a1ddca75c5f629173bf4772db7a635">f64vec3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f64vec3&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">32&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Double-qualifier floating-point aligned vector of 3 components. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga804c654cead1139bd250f90f9bb01fad"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga66e92e57260bdb910609b9a56bf83e97">f64vec4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f64vec4&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">32&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Double-qualifier floating-point aligned vector of 4 components. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gacce4ac532880b8c7469d3c31974420a1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00283.html#ga8dd59e7fc6913ac5d61b86553e9148ba">mat2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00303.html#ga5a8a5f8c47cd7d5502dd9932f83472b9">aligned_mat2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 1x1 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> Single-qualifier floating-point aligned 2x2 matrix. </dd>
<dd>
<a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga0498e0e249a6faddaf96aa55d7f81c3b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00283.html#gaefb0fc7a4960b782c18708bb6b655262">mat3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00303.html#ga43a92a24ca863e0e0f3b65834b3cf714">aligned_mat3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 3x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga7435d87de82a0d652b35dc5b9cc718d5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00283.html#ga0db98d836c5549d31cf64ecd043b7af7">mat4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00303.html#gab0fddcf95dd51cbcbf624ea7c40dfeb8">aligned_mat4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 4x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga719da577361541a4c43a2dd1d0e361e1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga3350c93c3275298f940a42875388e4b4">fmat2x2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_fmat2&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 1x1 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> Single-qualifier floating-point aligned 2x2 matrix. </dd>
<dd>
<a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga6e7ee4f541e1d7db66cd1a224caacafb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaa07c86650253672a19dbfb898f3265b8">fmat3x3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_fmat3&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 3x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae5d672d359f2a39f63f98c7975057486"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gad01cc6479bde1fd1870f13d3ed9530b3">fmat4x4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_fmat4&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 4x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga6fa2df037dbfc5fe8c8e0b4db8a34953"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga3350c93c3275298f940a42875388e4b4">fmat2x2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_fmat2x2&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 1x1 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> Single-qualifier floating-point aligned 2x2 matrix. </dd>
<dd>
<a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga0743b4f4f69a3227b82ff58f6abbad62"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga55a2d2a8eb09b5633668257eb3cad453">fmat2x3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_fmat2x3&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 2x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga1a76b325fdf70f961d835edd182c63dd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga681381f19f11c9e5ee45cda2c56937ff">fmat2x4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_fmat2x4&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 2x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga4b4e181cd041ba28c3163e7b8074aef0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga6af54d70d9beb0a7ef992a879e86b04f">fmat3x2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_fmat3x2&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 3x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga27b13f465abc8a40705698145e222c3f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaa07c86650253672a19dbfb898f3265b8">fmat3x3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_fmat3x3&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 3x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2608d19cc275830a6f8c0b6405625a4f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga44e158af77a670ee1b58c03cda9e1619">fmat3x4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_fmat3x4&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 3x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga93f09768241358a287c4cca538f1f7e7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga8c8aa45aafcc23238edb1d5aeb801774">fmat4x2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_fmat4x2&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 4x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga7c117e3ecca089e10247b1d41d88aff9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga4295048a78bdf46b8a7de77ec665b497">fmat4x3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_fmat4x3&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 4x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga07c75cd04ba42dc37fa3e105f89455c5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gad01cc6479bde1fd1870f13d3ed9530b3">fmat4x4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_fmat4x4&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 4x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga65ff0d690a34a4d7f46f9b2eb51525ee"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga04100c76f7d55a0dd0983ccf05142bff">f32mat2x2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f32mat2&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 1x1 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> Single-qualifier floating-point aligned 2x2 matrix. </dd>
<dd>
<a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gadd8ddbe2bf65ccede865ba2f510176dc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga65261fa8a21045c8646ddff114a56174">f32mat3x3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f32mat3&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 3x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaf18dbff14bf13d3ff540c517659ec045"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaee4da0e9fbd8cfa2f89cb80889719dc3">f32mat4x4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f32mat4&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 4x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga66339f6139bf7ff19e245beb33f61cc8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga04100c76f7d55a0dd0983ccf05142bff">f32mat2x2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f32mat2x2&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 1x1 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> Single-qualifier floating-point aligned 2x2 matrix. </dd>
<dd>
<a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga1558a48b3934011b52612809f443e46d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gab256cdab5eb582e426d749ae77b5b566">f32mat2x3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f32mat2x3&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 2x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaa52e5732daa62851627021ad551c7680"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaf512b74c4400b68f9fdf9388b3d6aac8">f32mat2x4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f32mat2x4&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 2x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gac09663c42566bcb58d23c6781ac4e85a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga1320a08e14fdff3821241eefab6947e9">f32mat3x2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f32mat3x2&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 3x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga3f510999e59e1b309113e1d561162b29"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga65261fa8a21045c8646ddff114a56174">f32mat3x3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f32mat3x3&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 3x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2c9c94f0c89cd71ce56551db6cf4aaec"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gab90ade28222f8b861d5ceaf81a3a7f5d">f32mat3x4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f32mat3x4&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 3x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga99ce8274c750fbfdf0e70c95946a2875"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga3b32ca1e57a4ef91babbc3d35a34ea20">f32mat4x2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f32mat4x2&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 4x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga9476ef66790239df53dbe66f3989c3b5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga239b96198771b7add8eea7e6b59840c0">f32mat4x3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f32mat4x3&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 4x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gacc429b3b0b49921e12713b6d31e14e1d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaee4da0e9fbd8cfa2f89cb80889719dc3">f32mat4x4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f32mat4x4&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned 4x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga88f6c6fa06e6e64479763e69444669cf"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga9ec7c4c79e303c053e30729a95fb2c37">f64mat2x2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f64mat2&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">32&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Double-qualifier floating-point aligned 1x1 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> Double-qualifier floating-point aligned 2x2 matrix. </dd>
<dd>
<a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaae8e4639c991e64754145ab8e4c32083"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga916c1aed91cf91f7b41399ebe7c6e185">f64mat3x3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f64mat3&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">32&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Double-qualifier floating-point aligned 3x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga6e9094f3feb3b5b49d0f83683a101fde"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gae52e2b7077a9ff928a06ab5ce600b81e">f64mat4x4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f64mat4&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">32&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Double-qualifier floating-point aligned 4x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gadbd2c639c03de1c3e9591b5a39f65559"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga9ec7c4c79e303c053e30729a95fb2c37">f64mat2x2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f64mat2x2&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">32&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Double-qualifier floating-point aligned 1x1 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> Double-qualifier floating-point aligned 2x2 matrix. </dd>
<dd>
<a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gab059d7b9fe2094acc563b7223987499f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gae3ab5719fc4c1e966631dbbcba8d412a">f64mat2x3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f64mat2x3&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">32&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Double-qualifier floating-point aligned 2x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gabbc811d1c52ed2b8cfcaff1378f75c69"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gac87278e0c702ba8afff76316d4eeb769">f64mat2x4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f64mat2x4&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">32&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Double-qualifier floating-point aligned 2x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga9ddf5212777734d2fd841a84439f3bdf"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga2473d8bf3f4abf967c4d0e18175be6f7">f64mat3x2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f64mat3x2&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">32&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Double-qualifier floating-point aligned 3x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gad1dda32ed09f94bfcf0a7d8edfb6cf13"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga916c1aed91cf91f7b41399ebe7c6e185">f64mat3x3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f64mat3x3&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">32&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Double-qualifier floating-point aligned 3x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5875e0fa72f07e271e7931811cbbf31a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gaab239fa9e35b65a67cbaa6ac082f3675">f64mat3x4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f64mat3x4&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">32&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Double-qualifier floating-point aligned 3x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga41e82cd6ac07f912ba2a2d45799dcf0d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gab7daf79d6bc06a68bea1c6f5e11b5512">f64mat4x2</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f64mat4x2&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">32&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Double-qualifier floating-point aligned 4x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga0892638d6ba773043b3d63d1d092622e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga3e2e66ffbe341a80bc005ba2b9552110">f64mat4x3</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f64mat4x3&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">32&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Double-qualifier floating-point aligned 4x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga912a16432608b822f1e13607529934c1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gae52e2b7077a9ff928a06ab5ce600b81e">f64mat4x4</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f64mat4x4&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">32&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Double-qualifier floating-point aligned 4x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gafd945a8ea86b042aba410e0560df9a3d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00252.html#gab0b441adb4509bc58d2946c2239a8942">quat</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_quat&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned quaternion. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga19c2ba545d1f2f36bcb7b60c9a228622"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00252.html#gab0b441adb4509bc58d2946c2239a8942">quat</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_fquat&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned quaternion. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaabc28c84a3288b697605d4688686f9a9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00249.html#ga1181459aa5d640a3ea43861b118f3f0b">dquat</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_dquat&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">32&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Double-qualifier floating-point aligned quaternion. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga1ed8aeb5ca67fade269a46105f1bf273"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga38e674196ba411d642be40c47bf33939">f32quat</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f32quat&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">16&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-qualifier floating-point aligned quaternion. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga95cc03b8b475993fa50e05e38e203303"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">glm::GLM_ALIGNED_TYPEDEF </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga2b114a2f2af0fe1dfeb569c767822940">f64quat</a>&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">aligned_f64quat&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">32&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Double-qualifier floating-point aligned quaternion. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00364.html" title="Include <glm/gtx/type_aligned.hpp> to use the features of this extension. ">GLM_GTX_type_aligned</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
