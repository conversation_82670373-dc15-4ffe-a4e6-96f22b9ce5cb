<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_EXT_quaternion_float_precision</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a>  </div>
  <div class="headertitle">
<div class="title">GLM_EXT_quaternion_float_precision<div class="ingroups"><a class="el" href="a00285.html">Stable extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Exposes single-precision floating point quaternion type with various precision in term of ULPs.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:gaa2fd8085774376310aeb80588e0eab6e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaa2fd8085774376310aeb80588e0eab6e"></a>
typedef qua&lt; float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00253.html#gaa2fd8085774376310aeb80588e0eab6e">highp_quat</a></td></tr>
<tr class="memdesc:gaa2fd8085774376310aeb80588e0eab6e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Quaternion of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gaa2fd8085774376310aeb80588e0eab6e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gade62c5316c1c11a79c34c00c189558eb"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gade62c5316c1c11a79c34c00c189558eb"></a>
typedef qua&lt; float, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00253.html#gade62c5316c1c11a79c34c00c189558eb">lowp_quat</a></td></tr>
<tr class="memdesc:gade62c5316c1c11a79c34c00c189558eb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Quaternion of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gade62c5316c1c11a79c34c00c189558eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad2a59409de1bb12ccb6eb692ee7e9d8d"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gad2a59409de1bb12ccb6eb692ee7e9d8d"></a>
typedef qua&lt; float, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00253.html#gad2a59409de1bb12ccb6eb692ee7e9d8d">mediump_quat</a></td></tr>
<tr class="memdesc:gad2a59409de1bb12ccb6eb692ee7e9d8d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Quaternion of single-precision floating-point numbers using high precision arithmetic in term of ULPs. <br /></td></tr>
<tr class="separator:gad2a59409de1bb12ccb6eb692ee7e9d8d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Exposes single-precision floating point quaternion type with various precision in term of ULPs. </p>
<p>Include &lt;<a class="el" href="a00132.html" title="GLM_EXT_quaternion_float_precision ">glm/ext/quaternion_float_precision.hpp</a>&gt; to use the features of this extension. </p>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
