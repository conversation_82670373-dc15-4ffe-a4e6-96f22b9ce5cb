#!/usr/bin/env python3
"""
Test script for the GaVS preprocessing pipeline.

This script performs basic validation and testing of the preprocessing functionality
without requiring actual video files.
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
import unittest
from unittest.mock import patch, MagicMock

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

from preprocess_video_for_gavs import GaVSPreprocessor


class TestGaVSPreprocessor(unittest.TestCase):
    """Test cases for GaVS preprocessor."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = Path(tempfile.mkdtemp())
        self.test_video = self.test_dir / "test_video.mp4"
        self.output_dir = self.test_dir / "output"
        
        # Create a dummy video file
        self.test_video.touch()
    
    def tearDown(self):
        """Clean up test environment."""
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
    
    def test_initialization(self):
        """Test preprocessor initialization."""
        preprocessor = GaVSPreprocessor(
            input_video=str(self.test_video),
            output_dir=str(self.output_dir),
            fps=30.0
        )
        
        self.assertEqual(preprocessor.input_video, self.test_video)
        self.assertEqual(preprocessor.output_dir, self.output_dir)
        self.assertEqual(preprocessor.fps, 30.0)
        self.assertTrue(self.output_dir.exists())
    
    def test_initialization_missing_video(self):
        """Test initialization with missing video file."""
        missing_video = self.test_dir / "missing.mp4"
        
        with self.assertRaises(FileNotFoundError):
            GaVSPreprocessor(
                input_video=str(missing_video),
                output_dir=str(self.output_dir)
            )
    
    def test_directory_structure_setup(self):
        """Test directory structure creation."""
        preprocessor = GaVSPreprocessor(
            input_video=str(self.test_video),
            output_dir=str(self.output_dir)
        )
        
        preprocessor.setup_directory_structure()
        
        # Check that all required directories exist
        expected_dirs = [
            preprocessor.images_dir,
            preprocessor.fg_mask_dir,
            preprocessor.sparse_dir,
            preprocessor.inpainted_dir / "images",
            preprocessor.inpainted_dir / "fg_mask",
            preprocessor.colmap_mask_dir,
            preprocessor.rs_mask_dir
        ]
        
        for directory in expected_dirs:
            self.assertTrue(directory.exists(), f"Directory not created: {directory}")
    
    @patch('subprocess.run')
    def test_dependency_check_success(self, mock_run):
        """Test successful dependency check."""
        # Mock successful command execution
        mock_run.return_value.returncode = 0
        
        preprocessor = GaVSPreprocessor(
            input_video=str(self.test_video),
            output_dir=str(self.output_dir)
        )
        
        result = preprocessor.check_dependencies()
        self.assertTrue(result)
    
    @patch('subprocess.run')
    def test_dependency_check_failure(self, mock_run):
        """Test failed dependency check."""
        # Mock failed command execution
        mock_run.side_effect = FileNotFoundError()
        
        preprocessor = GaVSPreprocessor(
            input_video=str(self.test_video),
            output_dir=str(self.output_dir)
        )
        
        result = preprocessor.check_dependencies()
        self.assertFalse(result)
    
    @patch('subprocess.run')
    def test_gpu_detection(self, mock_run):
        """Test GPU detection."""
        preprocessor = GaVSPreprocessor(
            input_video=str(self.test_video),
            output_dir=str(self.output_dir)
        )
        
        # Test GPU available
        mock_run.return_value.returncode = 0
        self.assertTrue(preprocessor._has_gpu())
        
        # Test GPU not available
        mock_run.side_effect = FileNotFoundError()
        self.assertFalse(preprocessor._has_gpu())
    
    def test_blender_json_structure(self):
        """Test blender.json structure validation."""
        preprocessor = GaVSPreprocessor(
            input_video=str(self.test_video),
            output_dir=str(self.output_dir)
        )
        
        # Create mock camera and image data
        mock_camera = MagicMock()
        mock_camera.width = 640
        mock_camera.height = 480
        mock_camera.params = [500.0, 500.0, 320.0, 240.0]  # fx, fy, cx, cy
        
        mock_image = MagicMock()
        mock_image.name = "00001.png"
        mock_image.qvec = [1.0, 0.0, 0.0, 0.0]  # Identity quaternion
        mock_image.tvec = [0.0, 0.0, 0.0]  # Zero translation
        
        cameras = {1: mock_camera}
        images = {1: mock_image}
        
        # Mock the pose conversion method
        with patch.object(preprocessor, '_colmap_pose_to_transform_matrix') as mock_pose:
            import numpy as np
            mock_pose.return_value = np.eye(4)
            
            result = preprocessor.generate_blender_json(cameras, images)
            self.assertTrue(result)
            
            # Check that blender.json was created
            blender_json_path = self.output_dir / "blender.json"
            self.assertTrue(blender_json_path.exists())
            
            # Validate JSON structure
            import json
            with open(blender_json_path, 'r') as f:
                data = json.load(f)
            
            required_keys = ['camera_model', 'w', 'h', 'fl_x', 'fl_y', 'cx', 'cy', 'raw_frames']
            for key in required_keys:
                self.assertIn(key, data)
            
            self.assertEqual(data['w'], 640)
            self.assertEqual(data['h'], 480)
            self.assertEqual(len(data['raw_frames']), 1)


def test_command_line_interface():
    """Test the command line interface."""
    print("Testing command line interface...")
    
    # Test help message
    import subprocess
    result = subprocess.run([
        sys.executable, 'preprocess_video_for_gavs.py', '--help'
    ], capture_output=True, text=True)
    
    assert result.returncode == 0
    assert 'GaVS Video Preprocessing Pipeline' in result.stdout
    print("✅ Help message works correctly")


def test_example_script():
    """Test the example script."""
    print("Testing example script...")
    
    try:
        import example_preprocessing
        print("✅ Example script imports successfully")
        
        # Test that main function runs without errors
        example_preprocessing.main()
        print("✅ Example script main function runs")
        
    except Exception as e:
        print(f"❌ Example script test failed: {e}")


def run_integration_tests():
    """Run integration tests that don't require external dependencies."""
    print("Running integration tests...")
    
    # Test 1: Command line interface
    test_command_line_interface()
    
    # Test 2: Example script
    test_example_script()
    
    # Test 3: Import validation
    try:
        from preprocess_video_for_gavs import GaVSPreprocessor, create_gavs_config
        print("✅ Main modules import successfully")
    except ImportError as e:
        print(f"❌ Import test failed: {e}")
        return False
    
    # Test 4: COLMAP utilities import
    try:
        sys.path.append('datasets')
        from colmap_utils import read_model, qvec2rotmat
        print("✅ COLMAP utilities import successfully")
    except ImportError as e:
        print(f"❌ COLMAP utilities import failed: {e}")
        print("Make sure you're running this from the GaVS project root directory")
        return False
    
    print("✅ All integration tests passed!")
    return True


def main():
    """Run all tests."""
    print("🧪 GaVS Preprocessing Pipeline Tests")
    print("=" * 50)
    
    # Run unit tests
    print("\n📋 Running unit tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run integration tests
    print("\n🔗 Running integration tests...")
    success = run_integration_tests()
    
    if success:
        print("\n🎉 All tests completed successfully!")
        print("\nNext steps:")
        print("1. Install dependencies (ffmpeg, COLMAP)")
        print("2. Test with a real video file")
        print("3. Run: python preprocess_video_for_gavs.py --input video.mp4 --output ./dataset/test")
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")


if __name__ == "__main__":
    main()
