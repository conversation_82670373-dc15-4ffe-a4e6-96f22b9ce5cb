<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTC_reciprocal</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTC_reciprocal<div class="ingroups"><a class="el" href="a00286.html">Recommended extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00140.html" title="GLM_GTC_reciprocal ">glm/gtc/reciprocal.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gaeadfb9c9d71093f7865b2ba2ca8d104d"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gaeadfb9c9d71093f7865b2ba2ca8d104d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00301.html#gaeadfb9c9d71093f7865b2ba2ca8d104d">acot</a> (genType x)</td></tr>
<tr class="memdesc:gaeadfb9c9d71093f7865b2ba2ca8d104d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Inverse cotangent function.  <a href="a00301.html#gaeadfb9c9d71093f7865b2ba2ca8d104d">More...</a><br /></td></tr>
<tr class="separator:gaeadfb9c9d71093f7865b2ba2ca8d104d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafaca98a7100170db8841f446282debfa"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gafaca98a7100170db8841f446282debfa"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00301.html#gafaca98a7100170db8841f446282debfa">acoth</a> (genType x)</td></tr>
<tr class="memdesc:gafaca98a7100170db8841f446282debfa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Inverse cotangent hyperbolic function.  <a href="a00301.html#gafaca98a7100170db8841f446282debfa">More...</a><br /></td></tr>
<tr class="separator:gafaca98a7100170db8841f446282debfa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1b4bed91476b9b915e76b4a30236d330"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga1b4bed91476b9b915e76b4a30236d330"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00301.html#ga1b4bed91476b9b915e76b4a30236d330">acsc</a> (genType x)</td></tr>
<tr class="memdesc:ga1b4bed91476b9b915e76b4a30236d330"><td class="mdescLeft">&#160;</td><td class="mdescRight">Inverse cosecant function.  <a href="a00301.html#ga1b4bed91476b9b915e76b4a30236d330">More...</a><br /></td></tr>
<tr class="separator:ga1b4bed91476b9b915e76b4a30236d330"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4b50aa5e5afc7e19ec113ab91596c576"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga4b50aa5e5afc7e19ec113ab91596c576"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00301.html#ga4b50aa5e5afc7e19ec113ab91596c576">acsch</a> (genType x)</td></tr>
<tr class="memdesc:ga4b50aa5e5afc7e19ec113ab91596c576"><td class="mdescLeft">&#160;</td><td class="mdescRight">Inverse cosecant hyperbolic function.  <a href="a00301.html#ga4b50aa5e5afc7e19ec113ab91596c576">More...</a><br /></td></tr>
<tr class="separator:ga4b50aa5e5afc7e19ec113ab91596c576"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2c5b7f962c2c9ff684e6d2de48db1f10"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga2c5b7f962c2c9ff684e6d2de48db1f10"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00301.html#ga2c5b7f962c2c9ff684e6d2de48db1f10">asec</a> (genType x)</td></tr>
<tr class="memdesc:ga2c5b7f962c2c9ff684e6d2de48db1f10"><td class="mdescLeft">&#160;</td><td class="mdescRight">Inverse secant function.  <a href="a00301.html#ga2c5b7f962c2c9ff684e6d2de48db1f10">More...</a><br /></td></tr>
<tr class="separator:ga2c5b7f962c2c9ff684e6d2de48db1f10"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaec7586dccfe431f850d006f3824b8ca6"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gaec7586dccfe431f850d006f3824b8ca6"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00301.html#gaec7586dccfe431f850d006f3824b8ca6">asech</a> (genType x)</td></tr>
<tr class="memdesc:gaec7586dccfe431f850d006f3824b8ca6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Inverse secant hyperbolic function.  <a href="a00301.html#gaec7586dccfe431f850d006f3824b8ca6">More...</a><br /></td></tr>
<tr class="separator:gaec7586dccfe431f850d006f3824b8ca6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3a7b517a95bbd3ad74da3aea87a66314"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga3a7b517a95bbd3ad74da3aea87a66314"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00301.html#ga3a7b517a95bbd3ad74da3aea87a66314">cot</a> (genType angle)</td></tr>
<tr class="memdesc:ga3a7b517a95bbd3ad74da3aea87a66314"><td class="mdescLeft">&#160;</td><td class="mdescRight">Cotangent function.  <a href="a00301.html#ga3a7b517a95bbd3ad74da3aea87a66314">More...</a><br /></td></tr>
<tr class="separator:ga3a7b517a95bbd3ad74da3aea87a66314"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6b8b770eb7198e4dea59d52e6db81442"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga6b8b770eb7198e4dea59d52e6db81442"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00301.html#ga6b8b770eb7198e4dea59d52e6db81442">coth</a> (genType angle)</td></tr>
<tr class="memdesc:ga6b8b770eb7198e4dea59d52e6db81442"><td class="mdescLeft">&#160;</td><td class="mdescRight">Cotangent hyperbolic function.  <a href="a00301.html#ga6b8b770eb7198e4dea59d52e6db81442">More...</a><br /></td></tr>
<tr class="separator:ga6b8b770eb7198e4dea59d52e6db81442"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga59dd0005b6474eea48af743b4f14ebbb"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga59dd0005b6474eea48af743b4f14ebbb"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00301.html#ga59dd0005b6474eea48af743b4f14ebbb">csc</a> (genType angle)</td></tr>
<tr class="memdesc:ga59dd0005b6474eea48af743b4f14ebbb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Cosecant function.  <a href="a00301.html#ga59dd0005b6474eea48af743b4f14ebbb">More...</a><br /></td></tr>
<tr class="separator:ga59dd0005b6474eea48af743b4f14ebbb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6d95843ff3ca6472ab399ba171d290a0"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga6d95843ff3ca6472ab399ba171d290a0"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00301.html#ga6d95843ff3ca6472ab399ba171d290a0">csch</a> (genType angle)</td></tr>
<tr class="memdesc:ga6d95843ff3ca6472ab399ba171d290a0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Cosecant hyperbolic function.  <a href="a00301.html#ga6d95843ff3ca6472ab399ba171d290a0">More...</a><br /></td></tr>
<tr class="separator:ga6d95843ff3ca6472ab399ba171d290a0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae4bcbebee670c5ea155f0777b3acbd84"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gae4bcbebee670c5ea155f0777b3acbd84"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00301.html#gae4bcbebee670c5ea155f0777b3acbd84">sec</a> (genType angle)</td></tr>
<tr class="memdesc:gae4bcbebee670c5ea155f0777b3acbd84"><td class="mdescLeft">&#160;</td><td class="mdescRight">Secant function.  <a href="a00301.html#gae4bcbebee670c5ea155f0777b3acbd84">More...</a><br /></td></tr>
<tr class="separator:gae4bcbebee670c5ea155f0777b3acbd84"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9a5cfd1e7170104a7b33863b1b75e5ae"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga9a5cfd1e7170104a7b33863b1b75e5ae"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00301.html#ga9a5cfd1e7170104a7b33863b1b75e5ae">sech</a> (genType angle)</td></tr>
<tr class="memdesc:ga9a5cfd1e7170104a7b33863b1b75e5ae"><td class="mdescLeft">&#160;</td><td class="mdescRight">Secant hyperbolic function.  <a href="a00301.html#ga9a5cfd1e7170104a7b33863b1b75e5ae">More...</a><br /></td></tr>
<tr class="separator:ga9a5cfd1e7170104a7b33863b1b75e5ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00140.html" title="GLM_GTC_reciprocal ">glm/gtc/reciprocal.hpp</a>&gt; to use the features of this extension. </p>
<p>Define secant, cosecant and cotangent functions. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gaeadfb9c9d71093f7865b2ba2ca8d104d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::acot </td>
          <td>(</td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Inverse cotangent function. </p>
<dl class="section return"><dt>Returns</dt><dd>Return an angle expressed in radians. </dd></dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00301.html" title="Include <glm/gtc/reciprocal.hpp> to use the features of this extension. ">GLM_GTC_reciprocal</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gafaca98a7100170db8841f446282debfa"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::acoth </td>
          <td>(</td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Inverse cotangent hyperbolic function. </p>
<dl class="section return"><dt>Returns</dt><dd>Return an angle expressed in radians. </dd></dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00301.html" title="Include <glm/gtc/reciprocal.hpp> to use the features of this extension. ">GLM_GTC_reciprocal</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga1b4bed91476b9b915e76b4a30236d330"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::acsc </td>
          <td>(</td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Inverse cosecant function. </p>
<dl class="section return"><dt>Returns</dt><dd>Return an angle expressed in radians. </dd></dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00301.html" title="Include <glm/gtc/reciprocal.hpp> to use the features of this extension. ">GLM_GTC_reciprocal</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga4b50aa5e5afc7e19ec113ab91596c576"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::acsch </td>
          <td>(</td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Inverse cosecant hyperbolic function. </p>
<dl class="section return"><dt>Returns</dt><dd>Return an angle expressed in radians. </dd></dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00301.html" title="Include <glm/gtc/reciprocal.hpp> to use the features of this extension. ">GLM_GTC_reciprocal</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2c5b7f962c2c9ff684e6d2de48db1f10"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::asec </td>
          <td>(</td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Inverse secant function. </p>
<dl class="section return"><dt>Returns</dt><dd>Return an angle expressed in radians. </dd></dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00301.html" title="Include <glm/gtc/reciprocal.hpp> to use the features of this extension. ">GLM_GTC_reciprocal</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaec7586dccfe431f850d006f3824b8ca6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::asech </td>
          <td>(</td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Inverse secant hyperbolic function. </p>
<dl class="section return"><dt>Returns</dt><dd>Return an angle expressed in radians. </dd></dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00301.html" title="Include <glm/gtc/reciprocal.hpp> to use the features of this extension. ">GLM_GTC_reciprocal</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga3a7b517a95bbd3ad74da3aea87a66314"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::cot </td>
          <td>(</td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>angle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Cotangent function. </p>
<p>adjacent / opposite or 1 / tan(x)</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00301.html" title="Include <glm/gtc/reciprocal.hpp> to use the features of this extension. ">GLM_GTC_reciprocal</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga6b8b770eb7198e4dea59d52e6db81442"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::coth </td>
          <td>(</td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>angle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Cotangent hyperbolic function. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00301.html" title="Include <glm/gtc/reciprocal.hpp> to use the features of this extension. ">GLM_GTC_reciprocal</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga59dd0005b6474eea48af743b4f14ebbb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::csc </td>
          <td>(</td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>angle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Cosecant function. </p>
<p>hypotenuse / opposite or 1 / sin(x)</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00301.html" title="Include <glm/gtc/reciprocal.hpp> to use the features of this extension. ">GLM_GTC_reciprocal</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga6d95843ff3ca6472ab399ba171d290a0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::csch </td>
          <td>(</td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>angle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Cosecant hyperbolic function. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00301.html" title="Include <glm/gtc/reciprocal.hpp> to use the features of this extension. ">GLM_GTC_reciprocal</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae4bcbebee670c5ea155f0777b3acbd84"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::sec </td>
          <td>(</td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>angle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Secant function. </p>
<p>hypotenuse / adjacent or 1 / cos(x)</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00301.html" title="Include <glm/gtc/reciprocal.hpp> to use the features of this extension. ">GLM_GTC_reciprocal</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga9a5cfd1e7170104a7b33863b1b75e5ae"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::sech </td>
          <td>(</td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>angle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Secant hyperbolic function. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00301.html" title="Include <glm/gtc/reciprocal.hpp> to use the features of this extension. ">GLM_GTC_reciprocal</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
