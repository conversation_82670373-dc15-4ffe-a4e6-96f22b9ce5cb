<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: Integer functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Integer functions<div class="ingroups"><a class="el" href="a00280.html">Core features</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Provides GLSL functions on integer types.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga44abfe3379e11cbd29425a843420d0d6"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga44abfe3379e11cbd29425a843420d0d6"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL int&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00370.html#ga44abfe3379e11cbd29425a843420d0d6">bitCount</a> (genType v)</td></tr>
<tr class="memdesc:ga44abfe3379e11cbd29425a843420d0d6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the number of bits set to 1 in the binary representation of value.  <a href="a00370.html#ga44abfe3379e11cbd29425a843420d0d6">More...</a><br /></td></tr>
<tr class="separator:ga44abfe3379e11cbd29425a843420d0d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaac7b15e40bdea8d9aa4c4cb34049f7b5"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaac7b15e40bdea8d9aa4c4cb34049f7b5"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, int, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00370.html#gaac7b15e40bdea8d9aa4c4cb34049f7b5">bitCount</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gaac7b15e40bdea8d9aa4c4cb34049f7b5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the number of bits set to 1 in the binary representation of value.  <a href="a00370.html#gaac7b15e40bdea8d9aa4c4cb34049f7b5">More...</a><br /></td></tr>
<tr class="separator:gaac7b15e40bdea8d9aa4c4cb34049f7b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga346b25ab11e793e91a4a69c8aa6819f2"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga346b25ab11e793e91a4a69c8aa6819f2"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00370.html#ga346b25ab11e793e91a4a69c8aa6819f2">bitfieldExtract</a> (vec&lt; L, T, Q &gt; const &amp;Value, int Offset, int Bits)</td></tr>
<tr class="memdesc:ga346b25ab11e793e91a4a69c8aa6819f2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extracts bits [offset, offset + bits - 1] from value, returning them in the least significant bits of the result.  <a href="a00370.html#ga346b25ab11e793e91a4a69c8aa6819f2">More...</a><br /></td></tr>
<tr class="separator:ga346b25ab11e793e91a4a69c8aa6819f2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2e82992340d421fadb61a473df699b20"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga2e82992340d421fadb61a473df699b20"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00370.html#ga2e82992340d421fadb61a473df699b20">bitfieldInsert</a> (vec&lt; L, T, Q &gt; const &amp;Base, vec&lt; L, T, Q &gt; const &amp;Insert, int Offset, int Bits)</td></tr>
<tr class="memdesc:ga2e82992340d421fadb61a473df699b20"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the insertion the bits least-significant bits of insert into base.  <a href="a00370.html#ga2e82992340d421fadb61a473df699b20">More...</a><br /></td></tr>
<tr class="separator:ga2e82992340d421fadb61a473df699b20"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga750a1d92464489b7711dee67aa3441b6"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga750a1d92464489b7711dee67aa3441b6"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00370.html#ga750a1d92464489b7711dee67aa3441b6">bitfieldReverse</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga750a1d92464489b7711dee67aa3441b6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the reversal of the bits of value.  <a href="a00370.html#ga750a1d92464489b7711dee67aa3441b6">More...</a><br /></td></tr>
<tr class="separator:ga750a1d92464489b7711dee67aa3441b6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf74c4d969fa34ab8acb9d390f5ca5274"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:gaf74c4d969fa34ab8acb9d390f5ca5274"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL int&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00370.html#gaf74c4d969fa34ab8acb9d390f5ca5274">findLSB</a> (genIUType x)</td></tr>
<tr class="memdesc:gaf74c4d969fa34ab8acb9d390f5ca5274"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the bit number of the least significant bit set to 1 in the binary representation of value.  <a href="a00370.html#gaf74c4d969fa34ab8acb9d390f5ca5274">More...</a><br /></td></tr>
<tr class="separator:gaf74c4d969fa34ab8acb9d390f5ca5274"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4454c0331d6369888c28ab677f4810c7"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga4454c0331d6369888c28ab677f4810c7"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, int, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00370.html#ga4454c0331d6369888c28ab677f4810c7">findLSB</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga4454c0331d6369888c28ab677f4810c7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the bit number of the least significant bit set to 1 in the binary representation of value.  <a href="a00370.html#ga4454c0331d6369888c28ab677f4810c7">More...</a><br /></td></tr>
<tr class="separator:ga4454c0331d6369888c28ab677f4810c7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7e4a794d766861c70bc961630f8ef621"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:ga7e4a794d766861c70bc961630f8ef621"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL int&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00370.html#ga7e4a794d766861c70bc961630f8ef621">findMSB</a> (genIUType x)</td></tr>
<tr class="memdesc:ga7e4a794d766861c70bc961630f8ef621"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the bit number of the most significant bit in the binary representation of value.  <a href="a00370.html#ga7e4a794d766861c70bc961630f8ef621">More...</a><br /></td></tr>
<tr class="separator:ga7e4a794d766861c70bc961630f8ef621"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga39ac4d52028bb6ab08db5ad6562c2872"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga39ac4d52028bb6ab08db5ad6562c2872"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, int, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00370.html#ga39ac4d52028bb6ab08db5ad6562c2872">findMSB</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga39ac4d52028bb6ab08db5ad6562c2872"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the bit number of the most significant bit in the binary representation of value.  <a href="a00370.html#ga39ac4d52028bb6ab08db5ad6562c2872">More...</a><br /></td></tr>
<tr class="separator:ga39ac4d52028bb6ab08db5ad6562c2872"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac0c510a70e852f57594a9141848642e3"><td class="memTemplParams" colspan="2">template&lt;length_t L, qualifier Q&gt; </td></tr>
<tr class="memitem:gac0c510a70e852f57594a9141848642e3"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00370.html#gac0c510a70e852f57594a9141848642e3">imulExtended</a> (vec&lt; L, int, Q &gt; const &amp;x, vec&lt; L, int, Q &gt; const &amp;y, vec&lt; L, int, Q &gt; &amp;msb, vec&lt; L, int, Q &gt; &amp;lsb)</td></tr>
<tr class="memdesc:gac0c510a70e852f57594a9141848642e3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Multiplies 32-bit integers x and y, producing a 64-bit result.  <a href="a00370.html#gac0c510a70e852f57594a9141848642e3">More...</a><br /></td></tr>
<tr class="separator:gac0c510a70e852f57594a9141848642e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaedcec48743632dff6786bcc492074b1b"><td class="memTemplParams" colspan="2">template&lt;length_t L, qualifier Q&gt; </td></tr>
<tr class="memitem:gaedcec48743632dff6786bcc492074b1b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, uint, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00370.html#gaedcec48743632dff6786bcc492074b1b">uaddCarry</a> (vec&lt; L, uint, Q &gt; const &amp;x, vec&lt; L, uint, Q &gt; const &amp;y, vec&lt; L, uint, Q &gt; &amp;carry)</td></tr>
<tr class="memdesc:gaedcec48743632dff6786bcc492074b1b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds 32-bit unsigned integer x and y, returning the sum modulo pow(2, 32).  <a href="a00370.html#gaedcec48743632dff6786bcc492074b1b">More...</a><br /></td></tr>
<tr class="separator:gaedcec48743632dff6786bcc492074b1b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga732e2fb56db57ea541c7e5c92b7121be"><td class="memTemplParams" colspan="2">template&lt;length_t L, qualifier Q&gt; </td></tr>
<tr class="memitem:ga732e2fb56db57ea541c7e5c92b7121be"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00370.html#ga732e2fb56db57ea541c7e5c92b7121be">umulExtended</a> (vec&lt; L, uint, Q &gt; const &amp;x, vec&lt; L, uint, Q &gt; const &amp;y, vec&lt; L, uint, Q &gt; &amp;msb, vec&lt; L, uint, Q &gt; &amp;lsb)</td></tr>
<tr class="memdesc:ga732e2fb56db57ea541c7e5c92b7121be"><td class="mdescLeft">&#160;</td><td class="mdescRight">Multiplies 32-bit integers x and y, producing a 64-bit result.  <a href="a00370.html#ga732e2fb56db57ea541c7e5c92b7121be">More...</a><br /></td></tr>
<tr class="separator:ga732e2fb56db57ea541c7e5c92b7121be"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae3316ba1229ad9b9f09480833321b053"><td class="memTemplParams" colspan="2">template&lt;length_t L, qualifier Q&gt; </td></tr>
<tr class="memitem:gae3316ba1229ad9b9f09480833321b053"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, uint, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00370.html#gae3316ba1229ad9b9f09480833321b053">usubBorrow</a> (vec&lt; L, uint, Q &gt; const &amp;x, vec&lt; L, uint, Q &gt; const &amp;y, vec&lt; L, uint, Q &gt; &amp;borrow)</td></tr>
<tr class="memdesc:gae3316ba1229ad9b9f09480833321b053"><td class="mdescLeft">&#160;</td><td class="mdescRight">Subtracts the 32-bit unsigned integer y from x, returning the difference if non-negative, or pow(2, 32) plus the difference otherwise.  <a href="a00370.html#gae3316ba1229ad9b9f09480833321b053">More...</a><br /></td></tr>
<tr class="separator:gae3316ba1229ad9b9f09480833321b053"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Provides GLSL functions on integer types. </p>
<p>These all operate component-wise. The description is per component. The notation [a, b] means the set of bits from bit-number a through bit-number b, inclusive. The lowest-order bit is bit 0.</p>
<p>Include &lt;<a class="el" href="a00043.html" title="Core features ">glm/integer.hpp</a>&gt; to use these core features. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga44abfe3379e11cbd29425a843420d0d6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL int glm::bitCount </td>
          <td>(</td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the number of bits set to 1 in the binary representation of value. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Signed or unsigned integer scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/bitCount.xml">GLSL bitCount man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.8 Integer Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaac7b15e40bdea8d9aa4c4cb34049f7b5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, int, Q&gt; glm::bitCount </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the number of bits set to 1 in the binary representation of value. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>An integer between 1 and 4 included that qualify the dimension of the vector. </td></tr>
    <tr><td class="paramname">T</td><td>Signed or unsigned integer scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/bitCount.xml">GLSL bitCount man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.8 Integer Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga346b25ab11e793e91a4a69c8aa6819f2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::bitfieldExtract </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Value</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>Offset</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>Bits</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Extracts bits [offset, offset + bits - 1] from value, returning them in the least significant bits of the result. </p>
<p>For unsigned data types, the most significant bits of the result will be set to zero. For signed data types, the most significant bits will be set to the value of bit offset + base - 1.</p>
<p>If bits is zero, the result will be zero. The result will be undefined if offset or bits is negative, or if the sum of offset and bits is greater than the number of bits used to store the operand.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>An integer between 1 and 4 included that qualify the dimension of the vector. </td></tr>
    <tr><td class="paramname">T</td><td>Signed or unsigned integer scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/bitfieldExtract.xml">GLSL bitfieldExtract man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.8 Integer Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2e82992340d421fadb61a473df699b20"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::bitfieldInsert </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Base</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Insert</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>Offset</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>Bits</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the insertion the bits least-significant bits of insert into base. </p>
<p>The result will have bits [offset, offset + bits - 1] taken from bits [0, bits - 1] of insert, and all other bits taken directly from the corresponding bits of base. If bits is zero, the result will simply be base. The result will be undefined if offset or bits is negative, or if the sum of offset and bits is greater than the number of bits used to store the operand.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>An integer between 1 and 4 included that qualify the dimension of the vector. </td></tr>
    <tr><td class="paramname">T</td><td>Signed or unsigned integer scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/bitfieldInsert.xml">GLSL bitfieldInsert man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.8 Integer Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga750a1d92464489b7711dee67aa3441b6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::bitfieldReverse </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the reversal of the bits of value. </p>
<p>The bit numbered n of the result will be taken from bit (bits - 1) - n of value, where bits is the total number of bits used to represent value.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>An integer between 1 and 4 included that qualify the dimension of the vector. </td></tr>
    <tr><td class="paramname">T</td><td>Signed or unsigned integer scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/bitfieldReverse.xml">GLSL bitfieldReverse man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.8 Integer Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaf74c4d969fa34ab8acb9d390f5ca5274"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL int glm::findLSB </td>
          <td>(</td>
          <td class="paramtype">genIUType&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the bit number of the least significant bit set to 1 in the binary representation of value. </p>
<p>If value is zero, -1 will be returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genIUType</td><td>Signed or unsigned integer scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/findLSB.xml">GLSL findLSB man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.8 Integer Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga4454c0331d6369888c28ab677f4810c7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, int, Q&gt; glm::findLSB </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the bit number of the least significant bit set to 1 in the binary representation of value. </p>
<p>If value is zero, -1 will be returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>An integer between 1 and 4 included that qualify the dimension of the vector. </td></tr>
    <tr><td class="paramname">T</td><td>Signed or unsigned integer scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/findLSB.xml">GLSL findLSB man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.8 Integer Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga7e4a794d766861c70bc961630f8ef621"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL int glm::findMSB </td>
          <td>(</td>
          <td class="paramtype">genIUType&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the bit number of the most significant bit in the binary representation of value. </p>
<p>For positive integers, the result will be the bit number of the most significant bit set to 1. For negative integers, the result will be the bit number of the most significant bit set to 0. For a value of zero or negative one, -1 will be returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genIUType</td><td>Signed or unsigned integer scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/findMSB.xml">GLSL findMSB man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.8 Integer Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga39ac4d52028bb6ab08db5ad6562c2872"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, int, Q&gt; glm::findMSB </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the bit number of the most significant bit in the binary representation of value. </p>
<p>For positive integers, the result will be the bit number of the most significant bit set to 1. For negative integers, the result will be the bit number of the most significant bit set to 0. For a value of zero or negative one, -1 will be returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>An integer between 1 and 4 included that qualify the dimension of the vector. </td></tr>
    <tr><td class="paramname">T</td><td>Signed or unsigned integer scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/findMSB.xml">GLSL findMSB man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.8 Integer Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gac0c510a70e852f57594a9141848642e3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL void glm::imulExtended </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, int, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, int, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, int, Q &gt; &amp;&#160;</td>
          <td class="paramname"><em>msb</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, int, Q &gt; &amp;&#160;</td>
          <td class="paramname"><em>lsb</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Multiplies 32-bit integers x and y, producing a 64-bit result. </p>
<p>The 32 least-significant bits are returned in lsb. The 32 most-significant bits are returned in msb.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>An integer between 1 and 4 included that qualify the dimension of the vector.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/imulExtended.xml">GLSL imulExtended man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.8 Integer Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaedcec48743632dff6786bcc492074b1b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, uint, Q&gt; glm::uaddCarry </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, uint, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, uint, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, uint, Q &gt; &amp;&#160;</td>
          <td class="paramname"><em>carry</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Adds 32-bit unsigned integer x and y, returning the sum modulo pow(2, 32). </p>
<p>The value carry is set to 0 if the sum was less than pow(2, 32), or to 1 otherwise.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>An integer between 1 and 4 included that qualify the dimension of the vector.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/uaddCarry.xml">GLSL uaddCarry man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.8 Integer Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga732e2fb56db57ea541c7e5c92b7121be"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL void glm::umulExtended </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, uint, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, uint, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, uint, Q &gt; &amp;&#160;</td>
          <td class="paramname"><em>msb</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, uint, Q &gt; &amp;&#160;</td>
          <td class="paramname"><em>lsb</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Multiplies 32-bit integers x and y, producing a 64-bit result. </p>
<p>The 32 least-significant bits are returned in lsb. The 32 most-significant bits are returned in msb.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>An integer between 1 and 4 included that qualify the dimension of the vector.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/umulExtended.xml">GLSL umulExtended man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.8 Integer Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae3316ba1229ad9b9f09480833321b053"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, uint, Q&gt; glm::usubBorrow </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, uint, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, uint, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, uint, Q &gt; &amp;&#160;</td>
          <td class="paramname"><em>borrow</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Subtracts the 32-bit unsigned integer y from x, returning the difference if non-negative, or pow(2, 32) plus the difference otherwise. </p>
<p>The value borrow is set to 0 if x &gt;= y, or to 1 otherwise.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>An integer between 1 and 4 included that qualify the dimension of the vector.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/usubBorrow.xml">GLSL usubBorrow man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.8 Integer Functions</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
