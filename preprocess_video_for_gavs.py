#!/usr/bin/env python3
"""
GaVS Video Preprocessing Pipeline
=================================

This script automates the complete preprocessing pipeline for preparing custom videos
to work with GaVS (3D-Grounded Video Stabilization).

Features:
- Automatic frame extraction from video files
- COLMAP pipeline integration for camera pose estimation
- Dynamic object mask generation setup
- Automatic blender.json configuration file generation
- Complete directory structure setup
- Error handling and validation
- Optional inpainting and rolling shutter mask support

Dependencies:
- ffmpeg (for video processing)
- COLMAP (for camera pose estimation)
- OpenCV (cv2)
- NumPy
- PIL/Pillow
- Optional: Grounded SAM 2 for mask generation
- Optional: ProPainter for video inpainting

Usage:
    python preprocess_video_for_gavs.py --input video.mp4 --output ./dataset/my_video

Author: Generated for GaVS preprocessing
"""

import os
import sys
import json
import argparse
import subprocess
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import tempfile

try:
    import cv2
    import numpy as np
    from PIL import Image
except ImportError as e:
    print(f"Missing required Python packages: {e}")
    print("Please install: pip install opencv-python numpy pillow")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gavs_preprocessing.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class GaVSPreprocessor:
    """Main class for GaVS video preprocessing pipeline."""
    
    def __init__(self, input_video: str, output_dir: str, fps: float = 30.0):
        """
        Initialize the preprocessor.
        
        Args:
            input_video: Path to input video file
            output_dir: Output directory for processed dataset
            fps: Frame rate for extraction (default: 30.0)
        """
        self.input_video = Path(input_video)
        self.output_dir = Path(output_dir)
        self.fps = fps
        
        # Validate input
        if not self.input_video.exists():
            raise FileNotFoundError(f"Input video not found: {input_video}")
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Define subdirectories
        self.images_dir = self.output_dir / "images"
        self.fg_mask_dir = self.output_dir / "fg_mask"
        self.sparse_dir = self.output_dir / "sparse" / "0"
        self.inpainted_dir = self.output_dir / "inpainted"
        self.colmap_mask_dir = self.output_dir / "colmap_mask"
        self.rs_mask_dir = self.output_dir / "rs_mask"
        
        # COLMAP database path
        self.colmap_db = self.output_dir / "database.db"
        
        logger.info(f"Initialized GaVS preprocessor")
        logger.info(f"Input video: {self.input_video}")
        logger.info(f"Output directory: {self.output_dir}")
    
    def check_dependencies(self) -> bool:
        """Check if required external dependencies are available."""
        logger.info("Checking dependencies...")
        
        dependencies = {
            'ffmpeg': 'ffmpeg -version',
            'colmap': 'colmap -h'
        }
        
        missing_deps = []
        
        for dep_name, cmd in dependencies.items():
            try:
                result = subprocess.run(
                    cmd.split(), 
                    capture_output=True, 
                    text=True, 
                    timeout=10
                )
                if result.returncode == 0:
                    logger.info(f"✓ {dep_name} found")
                else:
                    missing_deps.append(dep_name)
            except (subprocess.TimeoutExpired, FileNotFoundError):
                missing_deps.append(dep_name)
        
        if missing_deps:
            logger.error(f"Missing dependencies: {', '.join(missing_deps)}")
            logger.error("Please install:")
            for dep in missing_deps:
                if dep == 'ffmpeg':
                    logger.error("  - FFmpeg: https://ffmpeg.org/download.html")
                elif dep == 'colmap':
                    logger.error("  - COLMAP: https://colmap.github.io/install.html")
            return False
        
        logger.info("All dependencies found!")
        return True
    
    def setup_directory_structure(self):
        """Create the required directory structure for GaVS dataset."""
        logger.info("Setting up directory structure...")
        
        directories = [
            self.images_dir,
            self.fg_mask_dir,
            self.sparse_dir,
            self.inpainted_dir / "images",
            self.inpainted_dir / "fg_mask",
            self.colmap_mask_dir,
            self.rs_mask_dir
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created directory: {directory}")
    
    def extract_frames(self) -> bool:
        """Extract frames from input video using ffmpeg."""
        logger.info(f"Extracting frames at {self.fps} FPS...")
        
        # FFmpeg command to extract frames
        cmd = [
            'ffmpeg',
            '-i', str(self.input_video),
            '-vf', f'fps={self.fps}',
            '-q:v', '2',  # High quality
            '-y',  # Overwrite existing files
            str(self.images_dir / '%05d.png')
        ]
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            if result.returncode != 0:
                logger.error(f"FFmpeg failed: {result.stderr}")
                return False
            
            # Count extracted frames
            frame_files = list(self.images_dir.glob('*.png'))
            logger.info(f"Successfully extracted {len(frame_files)} frames")
            
            if len(frame_files) == 0:
                logger.error("No frames were extracted!")
                return False
            
            return True
            
        except subprocess.TimeoutExpired:
            logger.error("Frame extraction timed out")
            return False
        except Exception as e:
            logger.error(f"Frame extraction failed: {e}")
            return False
    
    def get_video_info(self) -> Dict[str, Any]:
        """Get video information using ffprobe."""
        logger.info("Getting video information...")
        
        cmd = [
            'ffprobe',
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_streams',
            str(self.input_video)
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                logger.warning("Could not get video info with ffprobe")
                return {}
            
            data = json.loads(result.stdout)
            video_stream = None
            
            for stream in data.get('streams', []):
                if stream.get('codec_type') == 'video':
                    video_stream = stream
                    break
            
            if video_stream:
                info = {
                    'width': int(video_stream.get('width', 0)),
                    'height': int(video_stream.get('height', 0)),
                    'fps': eval(video_stream.get('r_frame_rate', '30/1')),
                    'duration': float(video_stream.get('duration', 0))
                }
                logger.info(f"Video info: {info}")
                return info
            
        except Exception as e:
            logger.warning(f"Could not parse video info: {e}")
        
        return {}

    def run_colmap_pipeline(self) -> bool:
        """Run the complete COLMAP pipeline for camera pose estimation."""
        logger.info("Running COLMAP pipeline...")

        # Step 1: Feature extraction
        logger.info("Step 1: COLMAP feature extraction...")
        cmd_extract = [
            'colmap', 'feature_extractor',
            '--database_path', str(self.colmap_db),
            '--image_path', str(self.images_dir),
            '--ImageReader.single_camera', '1',
            '--ImageReader.camera_model', 'PINHOLE',
            '--SiftExtraction.use_gpu', '1' if self._has_gpu() else '0'
        ]

        if not self._run_colmap_command(cmd_extract, "Feature extraction"):
            return False

        # Step 2: Feature matching
        logger.info("Step 2: COLMAP feature matching...")
        cmd_match = [
            'colmap', 'exhaustive_matcher',
            '--database_path', str(self.colmap_db),
            '--SiftMatching.use_gpu', '1' if self._has_gpu() else '0'
        ]

        if not self._run_colmap_command(cmd_match, "Feature matching"):
            return False

        # Step 3: Sparse reconstruction
        logger.info("Step 3: COLMAP sparse reconstruction...")
        cmd_mapper = [
            'colmap', 'mapper',
            '--database_path', str(self.colmap_db),
            '--image_path', str(self.images_dir),
            '--output_path', str(self.sparse_dir.parent)
        ]

        if not self._run_colmap_command(cmd_mapper, "Sparse reconstruction"):
            return False

        # Step 4: Convert to binary format (if not already)
        logger.info("Step 4: Converting COLMAP model to binary format...")
        if (self.sparse_dir / "cameras.txt").exists():
            cmd_convert = [
                'colmap', 'model_converter',
                '--input_path', str(self.sparse_dir),
                '--output_path', str(self.sparse_dir),
                '--output_type', 'BIN'
            ]
            self._run_colmap_command(cmd_convert, "Model conversion")

        # Validate COLMAP output
        required_files = ['cameras.bin', 'images.bin', 'points3D.bin']
        for file in required_files:
            if not (self.sparse_dir / file).exists():
                logger.error(f"COLMAP output missing: {file}")
                return False

        logger.info("COLMAP pipeline completed successfully!")
        return True

    def _has_gpu(self) -> bool:
        """Check if GPU is available for COLMAP."""
        try:
            result = subprocess.run(
                ['nvidia-smi'],
                capture_output=True,
                text=True,
                timeout=5
            )
            return result.returncode == 0
        except Exception:
            return False

    def _run_colmap_command(self, cmd: List[str], step_name: str) -> bool:
        """Run a COLMAP command with error handling."""
        try:
            logger.info(f"Running: {' '.join(cmd)}")
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=1800  # 30 minute timeout
            )

            if result.returncode != 0:
                logger.error(f"{step_name} failed:")
                logger.error(f"STDOUT: {result.stdout}")
                logger.error(f"STDERR: {result.stderr}")
                return False

            logger.info(f"{step_name} completed successfully")
            return True

        except subprocess.TimeoutExpired:
            logger.error(f"{step_name} timed out")
            return False
        except Exception as e:
            logger.error(f"{step_name} failed with exception: {e}")
            return False

    def parse_colmap_results(self) -> Tuple[Dict, Dict, Dict]:
        """Parse COLMAP binary results to extract camera parameters and poses."""
        logger.info("Parsing COLMAP results...")

        try:
            # Import COLMAP reading utilities from the project
            sys.path.append(str(Path(__file__).parent / "datasets"))
            from colmap_utils import read_model

            cameras, images, points3d = read_model(str(self.sparse_dir), ext='.bin')

            logger.info("Parsed COLMAP results:")
            logger.info(f"  - {len(cameras)} cameras")
            logger.info(f"  - {len(images)} images")
            logger.info(f"  - {len(points3d)} 3D points")

            return cameras, images, points3d

        except ImportError:
            logger.error("Could not import COLMAP utilities from datasets/colmap_utils.py")
            logger.error("Make sure you're running this script from the GaVS project root")
            return {}, {}, {}
        except Exception as e:
            logger.error(f"Failed to parse COLMAP results: {e}")
            return {}, {}, {}

    def generate_blender_json(self, cameras: Dict, images: Dict) -> bool:
        """Generate blender.json configuration file from COLMAP results."""
        logger.info("Generating blender.json configuration...")

        if not cameras or not images:
            logger.error("No camera or image data available for blender.json generation")
            return False

        try:
            # Get the first camera (assuming single camera setup)
            camera_id = list(cameras.keys())[0]
            camera = cameras[camera_id]

            # Extract camera parameters
            width = camera.width
            height = camera.height
            params = camera.params

            # For PINHOLE model: params = [fx, fy, cx, cy]
            if len(params) >= 4:
                fx, fy, cx, cy = params[:4]
            else:
                logger.error("Invalid camera parameters")
                return False

            # Create blender.json structure
            blender_data = {
                "camera_model": "PINHOLE",
                "pose_type": "global",
                "w": int(width),
                "h": int(height),
                "fl_x": float(fx),
                "fl_y": float(fy),
                "cx": float(cx),
                "cy": float(cy),
                "raw_frames": []
            }

            # Add frame data
            for image_id in sorted(images.keys()):
                image = images[image_id]

                # Convert COLMAP pose to transform matrix
                transform_matrix = self._colmap_pose_to_transform_matrix(image)

                frame_data = {
                    "file_path": f"images/{image.name}",
                    "colmap_im_id": int(image_id),
                    "transform_matrix": transform_matrix.tolist()
                }

                blender_data["raw_frames"].append(frame_data)

            # Save blender.json
            blender_json_path = self.output_dir / "blender.json"
            with open(blender_json_path, 'w') as f:
                json.dump(blender_data, f, indent=4)

            logger.info(f"Generated blender.json with {len(blender_data['raw_frames'])} frames")
            return True

        except Exception as e:
            logger.error(f"Failed to generate blender.json: {e}")
            return False

    def _colmap_pose_to_transform_matrix(self, image) -> np.ndarray:
        """Convert COLMAP image pose to 4x4 transformation matrix."""
        try:
            # Import quaternion to rotation matrix conversion
            sys.path.append(str(Path(__file__).parent / "datasets"))
            from colmap_utils import qvec2rotmat

            # Extract rotation and translation
            R = qvec2rotmat(image.qvec).astype(np.float32)
            t = image.tvec.astype(np.float32)

            # Create world-to-camera transformation matrix
            T_w2c = np.eye(4, dtype=np.float32)
            T_w2c[:3, :3] = R
            T_w2c[:3, 3] = t

            # Convert to camera-to-world (what blender.json expects)
            T_c2w = np.linalg.inv(T_w2c)

            # Convert from COLMAP to OpenGL/Blender coordinate system
            # COLMAP: +X right, +Y down, +Z forward
            # OpenGL: +X right, +Y up, +Z backward
            coord_transform = np.array([
                [1, 0, 0, 0],
                [0, -1, 0, 0],
                [0, 0, -1, 0],
                [0, 0, 0, 1]
            ], dtype=np.float32)

            T_c2w = T_c2w @ coord_transform

            return T_c2w

        except Exception as e:
            logger.error(f"Failed to convert COLMAP pose: {e}")
            # Return identity matrix as fallback
            return np.eye(4, dtype=np.float32)

    def generate_placeholder_masks(self) -> bool:
        """Generate placeholder masks for dynamic objects."""
        logger.info("Generating placeholder foreground masks...")

        try:
            # Get list of image files
            image_files = sorted(self.images_dir.glob('*.png'))

            if not image_files:
                logger.error("No images found for mask generation")
                return False

            # Get image dimensions from first image
            first_image = Image.open(image_files[0])
            width, height = first_image.size

            # Create white masks (assuming all foreground for now)
            # In practice, you would use Grounded SAM 2 or similar
            mask_array = np.ones((height, width), dtype=np.uint8) * 255

            for image_file in image_files:
                mask_path = self.fg_mask_dir / image_file.name
                mask_image = Image.fromarray(mask_array, mode='L')
                mask_image.save(mask_path)

            logger.info(f"Generated {len(image_files)} placeholder masks")
            logger.warning("Generated placeholder masks (all white). For better results:")
            logger.warning("1. Install Grounded SAM 2: https://github.com/IDEA-Research/Grounded-SAM-2")
            logger.warning("2. Use text prompts to generate proper dynamic object masks")
            logger.warning("3. Replace the placeholder masks in fg_mask/ directory")

            return True

        except Exception as e:
            logger.error(f"Failed to generate placeholder masks: {e}")
            return False

    def setup_mask_generation_instructions(self):
        """Create instructions for proper mask generation."""
        instructions_file = self.output_dir / "MASK_GENERATION_INSTRUCTIONS.md"

        instructions = """# Dynamic Object Mask Generation Instructions

The placeholder masks generated are all white (assuming everything is foreground).
For better video stabilization results, you should generate proper dynamic object masks.

## Recommended Approach: Grounded SAM 2

1. **Install Grounded SAM 2:**
   ```bash
   git clone https://github.com/IDEA-Research/Grounded-SAM-2.git
   cd Grounded-SAM-2
   pip install -e .
   ```

2. **Generate masks with text prompts:**
   ```python
   # Example script for mask generation
   from grounded_sam2 import GroundedSAM2

   model = GroundedSAM2()

   # Define text prompts for dynamic objects
   prompts = ["person", "car", "bicycle", "dog"]  # Adjust based on your video

   for image_path in image_paths:
       masks = model.generate_masks(image_path, prompts)
       # Save masks to fg_mask/ directory
   ```

3. **Alternative tools:**
   - **Segment Anything Model (SAM):** For interactive segmentation
   - **YOLO + tracking:** For object detection and tracking
   - **Manual annotation:** Using tools like CVAT or LabelMe

## Mask Requirements

- **Format:** PNG images with same names as input frames
- **Size:** Same dimensions as input images
- **Values:**
  - White (255) for dynamic/foreground objects
  - Black (0) for static/background regions
- **Location:** Save masks in the `fg_mask/` directory

## Quality Tips

1. **Consistent segmentation:** Ensure objects are consistently segmented across frames
2. **Clean boundaries:** Avoid noisy or jagged mask edges
3. **Complete objects:** Include entire objects, not just parts
4. **Temporal consistency:** Maintain object identity across frames

After generating proper masks, you can proceed with GaVS training.
"""

        with open(instructions_file, 'w') as f:
            f.write(instructions)

        logger.info(f"Created mask generation instructions: {instructions_file}")

    def validate_dataset(self) -> bool:
        """Validate the generated dataset structure and files."""
        logger.info("Validating dataset structure...")

        required_files = [
            self.output_dir / "blender.json",
            self.sparse_dir / "cameras.bin",
            self.sparse_dir / "images.bin",
            self.sparse_dir / "points3D.bin"
        ]

        required_dirs = [
            self.images_dir,
            self.fg_mask_dir,
            self.sparse_dir
        ]

        # Check required files
        for file_path in required_files:
            if not file_path.exists():
                logger.error(f"Missing required file: {file_path}")
                return False

        # Check required directories
        for dir_path in required_dirs:
            if not dir_path.exists():
                logger.error(f"Missing required directory: {dir_path}")
                return False

        # Check if we have images and masks
        image_files = list(self.images_dir.glob('*.png'))
        mask_files = list(self.fg_mask_dir.glob('*.png'))

        if len(image_files) == 0:
            logger.error("No image files found")
            return False

        if len(mask_files) == 0:
            logger.error("No mask files found")
            return False

        if len(image_files) != len(mask_files):
            logger.warning(f"Mismatch: {len(image_files)} images vs {len(mask_files)} masks")

        # Validate blender.json
        try:
            with open(self.output_dir / "blender.json", 'r') as f:
                blender_data = json.load(f)

            required_keys = ['camera_model', 'w', 'h', 'fl_x', 'fl_y', 'cx', 'cy', 'raw_frames']
            for key in required_keys:
                if key not in blender_data:
                    logger.error(f"Missing key in blender.json: {key}")
                    return False

            if len(blender_data['raw_frames']) != len(image_files):
                logger.error("Frame count mismatch between blender.json and images")
                return False

        except Exception as e:
            logger.error(f"Invalid blender.json: {e}")
            return False

        logger.info("Dataset validation passed!")
        logger.info(f"Dataset ready with {len(image_files)} frames")
        return True

    def process(self) -> bool:
        """Run the complete preprocessing pipeline."""
        logger.info("Starting GaVS preprocessing pipeline...")

        try:
            # Step 1: Check dependencies
            if not self.check_dependencies():
                return False

            # Step 2: Setup directory structure
            self.setup_directory_structure()

            # Step 3: Extract frames from video
            if not self.extract_frames():
                logger.error("Frame extraction failed")
                return False

            # Step 4: Run COLMAP pipeline
            if not self.run_colmap_pipeline():
                logger.error("COLMAP pipeline failed")
                return False

            # Step 5: Parse COLMAP results
            cameras, images, points3d = self.parse_colmap_results()
            if not cameras or not images:
                logger.error("Failed to parse COLMAP results")
                return False

            # Step 6: Generate blender.json
            if not self.generate_blender_json(cameras, images):
                logger.error("Failed to generate blender.json")
                return False

            # Step 7: Generate placeholder masks
            if not self.generate_placeholder_masks():
                logger.error("Failed to generate masks")
                return False

            # Step 8: Create mask generation instructions
            self.setup_mask_generation_instructions()

            # Step 9: Validate dataset
            if not self.validate_dataset():
                logger.error("Dataset validation failed")
                return False

            logger.info("✓ GaVS preprocessing completed successfully!")
            logger.info(f"Dataset ready at: {self.output_dir}")
            logger.info("\nNext steps:")
            logger.info("1. Review and improve the generated masks in fg_mask/ directory")
            logger.info("2. Run GaVS training:")
            logger.info(f"   python train.py dataset.data_path={self.output_dir} hydra.run.dir=./exp/{self.output_dir.name}")

            return True

        except Exception as e:
            logger.error(f"Preprocessing failed: {e}")
            return False


def create_gavs_config(dataset_path: str, output_path: str):
    """Create a GaVS dataset configuration file."""
    config_content = f"""name: {Path(dataset_path).name}
data_path: {dataset_path}

height: 320
width: 576
stability: 4
stability_window: 31

znear: 0.01
zfar: 100.0

pad_border_aug: 64
subset: -1  # use subset frames for small set overfitting

frame_sampling_method: random
scale_pose_by_depth: true

predefined_depth_scale:
use_inpainted_images: false  # Set to true if you have inpainted images
"""

    with open(output_path, 'w') as f:
        f.write(config_content)

    logger.info(f"Created GaVS config file: {output_path}")


def main():
    """Main entry point for the preprocessing script."""
    parser = argparse.ArgumentParser(
        description="GaVS Video Preprocessing Pipeline",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic usage
  python preprocess_video_for_gavs.py --input video.mp4 --output ./dataset/my_video

  # Custom frame rate
  python preprocess_video_for_gavs.py --input video.mp4 --output ./dataset/my_video --fps 25

  # Generate config file
  python preprocess_video_for_gavs.py --input video.mp4 --output ./dataset/my_video --create-config

Dependencies:
  - ffmpeg: https://ffmpeg.org/download.html
  - COLMAP: https://colmap.github.io/install.html
  - Python packages: opencv-python, numpy, pillow
        """
    )

    parser.add_argument(
        '--input', '-i',
        required=True,
        help='Path to input video file'
    )

    parser.add_argument(
        '--output', '-o',
        required=True,
        help='Output directory for processed dataset'
    )

    parser.add_argument(
        '--fps',
        type=float,
        default=30.0,
        help='Frame rate for extraction (default: 30.0)'
    )

    parser.add_argument(
        '--create-config',
        action='store_true',
        help='Create a GaVS dataset configuration file'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )

    args = parser.parse_args()

    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Create preprocessor and run pipeline
    try:
        preprocessor = GaVSPreprocessor(
            input_video=args.input,
            output_dir=args.output,
            fps=args.fps
        )

        success = preprocessor.process()

        if success and args.create_config:
            config_path = Path(args.output).parent / f"{Path(args.output).name}.yaml"
            create_gavs_config(args.output, str(config_path))

        if success:
            logger.info("Preprocessing completed successfully!")
            sys.exit(0)
        else:
            logger.error("Preprocessing failed!")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.info("Preprocessing interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
