<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: Recommended extensions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#groups">Modules</a>  </div>
  <div class="headertitle">
<div class="title">Recommended extensions</div>  </div>
</div><!--header-->
<div class="contents">

<p>Additional features not specified by GLSL specification.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="groups"></a>
Modules</h2></td></tr>
<tr class="memitem:a00288"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00288.html">GLM_GTC_bitfield</a></td></tr>
<tr class="memdesc:a00288"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00009.html" title="GLM_GTC_bitfield ">glm/gtc/bitfield.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00289"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00289.html">GLM_GTC_color_space</a></td></tr>
<tr class="memdesc:a00289"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00012.html" title="GLM_GTC_color_space ">glm/gtc/color_space.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00290"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00290.html">GLM_GTC_constants</a></td></tr>
<tr class="memdesc:a00290"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00021.html" title="GLM_GTC_constants ">glm/gtc/constants.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00291"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00291.html">GLM_GTC_epsilon</a></td></tr>
<tr class="memdesc:a00291"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00024.html" title="GLM_GTC_epsilon ">glm/gtc/epsilon.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00292"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00292.html">GLM_GTC_integer</a></td></tr>
<tr class="memdesc:a00292"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00041.html" title="GLM_GTC_integer ">glm/gtc/integer.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00293"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00293.html">GLM_GTC_matrix_access</a></td></tr>
<tr class="memdesc:a00293"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00058.html" title="GLM_GTC_matrix_access ">glm/gtc/matrix_access.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00294"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html">GLM_GTC_matrix_integer</a></td></tr>
<tr class="memdesc:a00294"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00100.html" title="GLM_GTC_matrix_integer ">glm/gtc/matrix_integer.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00295"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00295.html">GLM_GTC_matrix_inverse</a></td></tr>
<tr class="memdesc:a00295"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00100.html" title="GLM_GTC_matrix_integer ">glm/gtc/matrix_integer.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00296"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00296.html">GLM_GTC_matrix_transform</a></td></tr>
<tr class="memdesc:a00296"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00109.html" title="GLM_GTC_matrix_transform ">glm/gtc/matrix_transform.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00297"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00297.html">GLM_GTC_noise</a></td></tr>
<tr class="memdesc:a00297"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00112.html" title="GLM_GTC_noise ">glm/gtc/noise.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00298"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html">GLM_GTC_packing</a></td></tr>
<tr class="memdesc:a00298"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00119.html" title="GLM_GTC_packing ">glm/gtc/packing.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00299"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00299.html">GLM_GTC_quaternion</a></td></tr>
<tr class="memdesc:a00299"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00125.html" title="GLM_GTC_quaternion ">glm/gtc/quaternion.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00300"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00300.html">GLM_GTC_random</a></td></tr>
<tr class="memdesc:a00300"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00137.html" title="GLM_GTC_random ">glm/gtc/random.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00301"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00301.html">GLM_GTC_reciprocal</a></td></tr>
<tr class="memdesc:a00301"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00140.html" title="GLM_GTC_reciprocal ">glm/gtc/reciprocal.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00302"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00302.html">GLM_GTC_round</a></td></tr>
<tr class="memdesc:a00302"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00143.html" title="GLM_GTC_round ">glm/gtc/round.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00303"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00303.html">GLM_GTC_type_aligned</a></td></tr>
<tr class="memdesc:a00303"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00161.html" title="GLM_GTC_type_aligned ">glm/gtc/type_aligned.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00304"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00304.html">GLM_GTC_type_precision</a></td></tr>
<tr class="memdesc:a00304"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00174.html" title="GLM_GTC_type_precision ">glm/gtc/type_precision.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00305"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00305.html">GLM_GTC_type_ptr</a></td></tr>
<tr class="memdesc:a00305"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00175.html" title="GLM_GTC_type_ptr ">glm/gtc/type_ptr.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00306"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00306.html">GLM_GTC_ulp</a></td></tr>
<tr class="memdesc:a00306"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00182.html" title="GLM_GTC_ulp ">glm/gtc/ulp.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00307"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00307.html">GLM_GTC_vec1</a></td></tr>
<tr class="memdesc:a00307"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00183.html" title="GLM_GTC_vec1 ">glm/gtc/vec1.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Additional features not specified by GLSL specification. </p>
<p>GTC extensions aim to be stable with tests and documentation.</p>
<p>Even if it's highly unrecommended, it's possible to include all the extensions at once by including &lt;<a class="el" href="a00027.html" title="Core features (Dependence) ">glm/ext.hpp</a>&gt;. Otherwise, each extension needs to be included a specific file. </p>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
