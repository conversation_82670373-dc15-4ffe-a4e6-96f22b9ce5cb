<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_transform2</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_transform2<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00159.html" title="GLM_GTX_transform2 ">glm/gtx/transform2.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga5b992a0cdc8298054edb68e228f0d93e"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga5b992a0cdc8298054edb68e228f0d93e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 3, 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00363.html#ga5b992a0cdc8298054edb68e228f0d93e">proj2D</a> (mat&lt; 3, 3, T, Q &gt; const &amp;m, vec&lt; 3, T, Q &gt; const &amp;normal)</td></tr>
<tr class="memdesc:ga5b992a0cdc8298054edb68e228f0d93e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build planar projection matrix along normal axis.  <a href="a00363.html#ga5b992a0cdc8298054edb68e228f0d93e">More...</a><br /></td></tr>
<tr class="separator:ga5b992a0cdc8298054edb68e228f0d93e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa2b7f4f15b98f697caede11bef50509e"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaa2b7f4f15b98f697caede11bef50509e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00363.html#gaa2b7f4f15b98f697caede11bef50509e">proj3D</a> (mat&lt; 4, 4, T, Q &gt; const &amp;m, vec&lt; 3, T, Q &gt; const &amp;normal)</td></tr>
<tr class="memdesc:gaa2b7f4f15b98f697caede11bef50509e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build planar projection matrix along normal axis.  <a href="a00363.html#gaa2b7f4f15b98f697caede11bef50509e">More...</a><br /></td></tr>
<tr class="separator:gaa2b7f4f15b98f697caede11bef50509e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabf249498b236e62c983d90d30d63c99c"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gabf249498b236e62c983d90d30d63c99c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00363.html#gabf249498b236e62c983d90d30d63c99c">scaleBias</a> (T scale, T bias)</td></tr>
<tr class="memdesc:gabf249498b236e62c983d90d30d63c99c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a scale bias matrix.  <a href="a00363.html#gabf249498b236e62c983d90d30d63c99c">More...</a><br /></td></tr>
<tr class="separator:gabf249498b236e62c983d90d30d63c99c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae2bdd91a76759fecfbaef97e3020aa8e"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gae2bdd91a76759fecfbaef97e3020aa8e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00363.html#gae2bdd91a76759fecfbaef97e3020aa8e">scaleBias</a> (mat&lt; 4, 4, T, Q &gt; const &amp;m, T scale, T bias)</td></tr>
<tr class="memdesc:gae2bdd91a76759fecfbaef97e3020aa8e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a scale bias matrix.  <a href="a00363.html#gae2bdd91a76759fecfbaef97e3020aa8e">More...</a><br /></td></tr>
<tr class="separator:gae2bdd91a76759fecfbaef97e3020aa8e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabf714b8a358181572b32a45555f71948"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gabf714b8a358181572b32a45555f71948"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 3, 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00363.html#gabf714b8a358181572b32a45555f71948">shearX2D</a> (mat&lt; 3, 3, T, Q &gt; const &amp;m, T y)</td></tr>
<tr class="memdesc:gabf714b8a358181572b32a45555f71948"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms a matrix with a shearing on X axis.  <a href="a00363.html#gabf714b8a358181572b32a45555f71948">More...</a><br /></td></tr>
<tr class="separator:gabf714b8a358181572b32a45555f71948"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga73e867c6cd4d700fe2054437e56106c4"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga73e867c6cd4d700fe2054437e56106c4"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00363.html#ga73e867c6cd4d700fe2054437e56106c4">shearX3D</a> (mat&lt; 4, 4, T, Q &gt; const &amp;m, T y, T z)</td></tr>
<tr class="memdesc:ga73e867c6cd4d700fe2054437e56106c4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms a matrix with a shearing on X axis From GLM_GTX_transform2 extension.  <a href="a00363.html#ga73e867c6cd4d700fe2054437e56106c4">More...</a><br /></td></tr>
<tr class="separator:ga73e867c6cd4d700fe2054437e56106c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac7998d0763d9181550c77e8af09a182c"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gac7998d0763d9181550c77e8af09a182c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 3, 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00363.html#gac7998d0763d9181550c77e8af09a182c">shearY2D</a> (mat&lt; 3, 3, T, Q &gt; const &amp;m, T x)</td></tr>
<tr class="memdesc:gac7998d0763d9181550c77e8af09a182c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms a matrix with a shearing on Y axis.  <a href="a00363.html#gac7998d0763d9181550c77e8af09a182c">More...</a><br /></td></tr>
<tr class="separator:gac7998d0763d9181550c77e8af09a182c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gade5bb65ffcb513973db1a1314fb5cfac"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gade5bb65ffcb513973db1a1314fb5cfac"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00363.html#gade5bb65ffcb513973db1a1314fb5cfac">shearY3D</a> (mat&lt; 4, 4, T, Q &gt; const &amp;m, T x, T z)</td></tr>
<tr class="memdesc:gade5bb65ffcb513973db1a1314fb5cfac"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms a matrix with a shearing on Y axis.  <a href="a00363.html#gade5bb65ffcb513973db1a1314fb5cfac">More...</a><br /></td></tr>
<tr class="separator:gade5bb65ffcb513973db1a1314fb5cfac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6591e0a3a9d2c9c0b6577bb4dace0255"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga6591e0a3a9d2c9c0b6577bb4dace0255"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00363.html#ga6591e0a3a9d2c9c0b6577bb4dace0255">shearZ3D</a> (mat&lt; 4, 4, T, Q &gt; const &amp;m, T x, T y)</td></tr>
<tr class="memdesc:ga6591e0a3a9d2c9c0b6577bb4dace0255"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms a matrix with a shearing on Z axis.  <a href="a00363.html#ga6591e0a3a9d2c9c0b6577bb4dace0255">More...</a><br /></td></tr>
<tr class="separator:ga6591e0a3a9d2c9c0b6577bb4dace0255"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00159.html" title="GLM_GTX_transform2 ">glm/gtx/transform2.hpp</a>&gt; to use the features of this extension. </p>
<p>Add extra transformation matrices </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga5b992a0cdc8298054edb68e228f0d93e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;3, 3, T, Q&gt; glm::proj2D </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 3, 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>normal</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build planar projection matrix along normal axis. </p>
<p>From GLM_GTX_transform2 extension. </p>

</div>
</div>
<a class="anchor" id="gaa2b7f4f15b98f697caede11bef50509e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::proj3D </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>normal</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build planar projection matrix along normal axis. </p>
<p>From GLM_GTX_transform2 extension. </p>

</div>
</div>
<a class="anchor" id="gabf249498b236e62c983d90d30d63c99c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::scaleBias </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>scale</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>bias</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a scale bias matrix. </p>
<p>From GLM_GTX_transform2 extension. </p>

</div>
</div>
<a class="anchor" id="gae2bdd91a76759fecfbaef97e3020aa8e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::scaleBias </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>scale</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>bias</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a scale bias matrix. </p>
<p>From GLM_GTX_transform2 extension. </p>

</div>
</div>
<a class="anchor" id="gabf714b8a358181572b32a45555f71948"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;3, 3, T, Q&gt; glm::shearX2D </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 3, 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Transforms a matrix with a shearing on X axis. </p>
<p>From GLM_GTX_transform2 extension. </p>

</div>
</div>
<a class="anchor" id="ga73e867c6cd4d700fe2054437e56106c4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::shearX3D </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>z</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Transforms a matrix with a shearing on X axis From GLM_GTX_transform2 extension. </p>

</div>
</div>
<a class="anchor" id="gac7998d0763d9181550c77e8af09a182c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;3, 3, T, Q&gt; glm::shearY2D </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 3, 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>x</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Transforms a matrix with a shearing on Y axis. </p>
<p>From GLM_GTX_transform2 extension. </p>

</div>
</div>
<a class="anchor" id="gade5bb65ffcb513973db1a1314fb5cfac"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::shearY3D </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>z</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Transforms a matrix with a shearing on Y axis. </p>
<p>From GLM_GTX_transform2 extension. </p>

</div>
</div>
<a class="anchor" id="ga6591e0a3a9d2c9c0b6577bb4dace0255"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::shearZ3D </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Transforms a matrix with a shearing on Z axis. </p>
<p>From GLM_GTX_transform2 extension. </p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
