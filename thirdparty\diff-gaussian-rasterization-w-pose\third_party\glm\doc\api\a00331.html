<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_intersect</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_intersect<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00044.html" title="GLM_GTX_intersect ">glm/gtx/intersect.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga9c68139f3d8a4f3d7fe45f9dbc0de5b7"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga9c68139f3d8a4f3d7fe45f9dbc0de5b7"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00331.html#ga9c68139f3d8a4f3d7fe45f9dbc0de5b7">intersectLineSphere</a> (genType const &amp;point0, genType const &amp;point1, genType const &amp;sphereCenter, typename genType::value_type sphereRadius, genType &amp;intersectionPosition1, genType &amp;intersectionNormal1, genType &amp;intersectionPosition2=genType(), genType &amp;intersectionNormal2=genType())</td></tr>
<tr class="memdesc:ga9c68139f3d8a4f3d7fe45f9dbc0de5b7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute the intersection of a line and a sphere.  <a href="a00331.html#ga9c68139f3d8a4f3d7fe45f9dbc0de5b7">More...</a><br /></td></tr>
<tr class="separator:ga9c68139f3d8a4f3d7fe45f9dbc0de5b7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9d29b9b3acb504d43986502f42740df4"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga9d29b9b3acb504d43986502f42740df4"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00331.html#ga9d29b9b3acb504d43986502f42740df4">intersectLineTriangle</a> (genType const &amp;orig, genType const &amp;dir, genType const &amp;vert0, genType const &amp;vert1, genType const &amp;vert2, genType &amp;position)</td></tr>
<tr class="memdesc:ga9d29b9b3acb504d43986502f42740df4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute the intersection of a line and a triangle.  <a href="a00331.html#ga9d29b9b3acb504d43986502f42740df4">More...</a><br /></td></tr>
<tr class="separator:ga9d29b9b3acb504d43986502f42740df4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad3697a9700ea379739a667ea02573488"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gad3697a9700ea379739a667ea02573488"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00331.html#gad3697a9700ea379739a667ea02573488">intersectRayPlane</a> (genType const &amp;orig, genType const &amp;dir, genType const &amp;planeOrig, genType const &amp;planeNormal, typename genType::value_type &amp;intersectionDistance)</td></tr>
<tr class="memdesc:gad3697a9700ea379739a667ea02573488"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute the intersection of a ray and a plane.  <a href="a00331.html#gad3697a9700ea379739a667ea02573488">More...</a><br /></td></tr>
<tr class="separator:gad3697a9700ea379739a667ea02573488"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac88f8cd84c4bcb5b947d56acbbcfa56e"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gac88f8cd84c4bcb5b947d56acbbcfa56e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00331.html#gac88f8cd84c4bcb5b947d56acbbcfa56e">intersectRaySphere</a> (genType const &amp;rayStarting, genType const &amp;rayNormalizedDirection, genType const &amp;sphereCenter, typename genType::value_type const sphereRadiusSquared, typename genType::value_type &amp;intersectionDistance)</td></tr>
<tr class="memdesc:gac88f8cd84c4bcb5b947d56acbbcfa56e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute the intersection distance of a ray and a sphere.  <a href="a00331.html#gac88f8cd84c4bcb5b947d56acbbcfa56e">More...</a><br /></td></tr>
<tr class="separator:gac88f8cd84c4bcb5b947d56acbbcfa56e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad28c00515b823b579c608aafa1100c1d"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gad28c00515b823b579c608aafa1100c1d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00331.html#gad28c00515b823b579c608aafa1100c1d">intersectRaySphere</a> (genType const &amp;rayStarting, genType const &amp;rayNormalizedDirection, genType const &amp;sphereCenter, const typename genType::value_type sphereRadius, genType &amp;intersectionPosition, genType &amp;intersectionNormal)</td></tr>
<tr class="memdesc:gad28c00515b823b579c608aafa1100c1d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute the intersection of a ray and a sphere.  <a href="a00331.html#gad28c00515b823b579c608aafa1100c1d">More...</a><br /></td></tr>
<tr class="separator:gad28c00515b823b579c608aafa1100c1d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga65bf2c594482f04881c36bc761f9e946"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga65bf2c594482f04881c36bc761f9e946"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00331.html#ga65bf2c594482f04881c36bc761f9e946">intersectRayTriangle</a> (vec&lt; 3, T, Q &gt; const &amp;orig, vec&lt; 3, T, Q &gt; const &amp;dir, vec&lt; 3, T, Q &gt; const &amp;v0, vec&lt; 3, T, Q &gt; const &amp;v1, vec&lt; 3, T, Q &gt; const &amp;v2, vec&lt; 2, T, Q &gt; &amp;baryPosition, T &amp;distance)</td></tr>
<tr class="memdesc:ga65bf2c594482f04881c36bc761f9e946"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute the intersection of a ray and a triangle.  <a href="a00331.html#ga65bf2c594482f04881c36bc761f9e946">More...</a><br /></td></tr>
<tr class="separator:ga65bf2c594482f04881c36bc761f9e946"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00044.html" title="GLM_GTX_intersect ">glm/gtx/intersect.hpp</a>&gt; to use the features of this extension. </p>
<p>Add intersection functions </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga9c68139f3d8a4f3d7fe45f9dbc0de5b7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL bool glm::intersectLineSphere </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>point0</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>point1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>sphereCenter</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">typename genType::value_type&#160;</td>
          <td class="paramname"><em>sphereRadius</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType &amp;&#160;</td>
          <td class="paramname"><em>intersectionPosition1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType &amp;&#160;</td>
          <td class="paramname"><em>intersectionNormal1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType &amp;&#160;</td>
          <td class="paramname"><em>intersectionPosition2</em> = <code>genType()</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType &amp;&#160;</td>
          <td class="paramname"><em>intersectionNormal2</em> = <code>genType()</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compute the intersection of a line and a sphere. </p>
<p>From GLM_GTX_intersect extension </p>

</div>
</div>
<a class="anchor" id="ga9d29b9b3acb504d43986502f42740df4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL bool glm::intersectLineTriangle </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>orig</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>dir</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>vert0</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>vert1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>vert2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType &amp;&#160;</td>
          <td class="paramname"><em>position</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compute the intersection of a line and a triangle. </p>
<p>From GLM_GTX_intersect extension. </p>

</div>
</div>
<a class="anchor" id="gad3697a9700ea379739a667ea02573488"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL bool glm::intersectRayPlane </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>orig</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>dir</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>planeOrig</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>planeNormal</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">typename genType::value_type &amp;&#160;</td>
          <td class="paramname"><em>intersectionDistance</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compute the intersection of a ray and a plane. </p>
<p>Ray direction and plane normal must be unit length. From GLM_GTX_intersect extension. </p>

</div>
</div>
<a class="anchor" id="gac88f8cd84c4bcb5b947d56acbbcfa56e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL bool glm::intersectRaySphere </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>rayStarting</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>rayNormalizedDirection</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>sphereCenter</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">typename genType::value_type const&#160;</td>
          <td class="paramname"><em>sphereRadiusSquared</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">typename genType::value_type &amp;&#160;</td>
          <td class="paramname"><em>intersectionDistance</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compute the intersection distance of a ray and a sphere. </p>
<p>The ray direction vector is unit length. From GLM_GTX_intersect extension. </p>

</div>
</div>
<a class="anchor" id="gad28c00515b823b579c608aafa1100c1d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL bool glm::intersectRaySphere </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>rayStarting</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>rayNormalizedDirection</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>sphereCenter</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const typename genType::value_type&#160;</td>
          <td class="paramname"><em>sphereRadius</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType &amp;&#160;</td>
          <td class="paramname"><em>intersectionPosition</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType &amp;&#160;</td>
          <td class="paramname"><em>intersectionNormal</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compute the intersection of a ray and a sphere. </p>
<p>From GLM_GTX_intersect extension. </p>

</div>
</div>
<a class="anchor" id="ga65bf2c594482f04881c36bc761f9e946"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL bool glm::intersectRayTriangle </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>orig</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>dir</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v0</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; &amp;&#160;</td>
          <td class="paramname"><em>baryPosition</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>distance</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compute the intersection of a ray and a triangle. </p>
<p>Based om Tomas Möller implementation <a href="http://fileadmin.cs.lth.se/cs/Personal/Tomas_Akenine-Moller/raytri/">http://fileadmin.cs.lth.se/cs/Personal/Tomas_Akenine-Moller/raytri/</a> From GLM_GTX_intersect extension. </p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
