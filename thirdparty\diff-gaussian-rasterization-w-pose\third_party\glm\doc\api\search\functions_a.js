var searchData=
[
  ['make_5fmat2',['make_mat2',['../a00305.html#ga04409e74dc3da251d2501acf5b4b546c',1,'glm']]],
  ['make_5fmat2x2',['make_mat2x2',['../a00305.html#gae49e1c7bcd5abec74d1c34155031f663',1,'glm']]],
  ['make_5fmat2x3',['make_mat2x3',['../a00305.html#ga21982104164789cf8985483aaefc25e8',1,'glm']]],
  ['make_5fmat2x4',['make_mat2x4',['../a00305.html#ga078b862c90b0e9a79ed43a58997d8388',1,'glm']]],
  ['make_5fmat3',['make_mat3',['../a00305.html#ga611ee7c4d4cadfc83a8fa8e1d10a170f',1,'glm']]],
  ['make_5fmat3x2',['make_mat3x2',['../a00305.html#ga27a24e121dc39e6857620e0f85b6e1a8',1,'glm']]],
  ['make_5fmat3x3',['make_mat3x3',['../a00305.html#gaf2e8337b15c3362aaeb6e5849e1c0536',1,'glm']]],
  ['make_5fmat3x4',['make_mat3x4',['../a00305.html#ga05dd66232aedb993e3b8e7b35eaf932b',1,'glm']]],
  ['make_5fmat4',['make_mat4',['../a00305.html#gae7bcedb710d1446c87fd1fc93ed8ee9a',1,'glm']]],
  ['make_5fmat4x2',['make_mat4x2',['../a00305.html#ga8b34c9b25bf3310d8ff9c828c7e2d97c',1,'glm']]],
  ['make_5fmat4x3',['make_mat4x3',['../a00305.html#ga0330bf6640092d7985fac92927bbd42b',1,'glm']]],
  ['make_5fmat4x4',['make_mat4x4',['../a00305.html#ga8f084be30e404844bfbb4a551ac2728c',1,'glm']]],
  ['make_5fquat',['make_quat',['../a00305.html#ga58110d7d81cf7d029e2bab7f8cd9b246',1,'glm']]],
  ['make_5fvec1',['make_vec1',['../a00305.html#ga4135f03f3049f0a4eb76545c4967957c',1,'glm::make_vec1(vec&lt; 1, T, Q &gt; const &amp;v)'],['../a00305.html#ga13c92b81e55f201b052a6404d57da220',1,'glm::make_vec1(vec&lt; 2, T, Q &gt; const &amp;v)'],['../a00305.html#ga3c23cc74086d361e22bbd5e91a334e03',1,'glm::make_vec1(vec&lt; 3, T, Q &gt; const &amp;v)'],['../a00305.html#ga6af06bb60d64ca8bcd169e3c93bc2419',1,'glm::make_vec1(vec&lt; 4, T, Q &gt; const &amp;v)']]],
  ['make_5fvec2',['make_vec2',['../a00305.html#ga8476d0e6f1b9b4a6193cc25f59d8a896',1,'glm::make_vec2(vec&lt; 1, T, Q &gt; const &amp;v)'],['../a00305.html#gae54bd325a08ad26edf63929201adebc7',1,'glm::make_vec2(vec&lt; 2, T, Q &gt; const &amp;v)'],['../a00305.html#ga0084fea4694cf47276e9cccbe7b1015a',1,'glm::make_vec2(vec&lt; 3, T, Q &gt; const &amp;v)'],['../a00305.html#ga2b81f71f3a222fe5bba81e3983751249',1,'glm::make_vec2(vec&lt; 4, T, Q &gt; const &amp;v)'],['../a00305.html#ga81253cf7b0ebfbb1e70540c5774e6824',1,'glm::make_vec2(T const *const ptr)']]],
  ['make_5fvec3',['make_vec3',['../a00305.html#ga9147e4b3a5d0f4772edfbfd179d7ea0b',1,'glm::make_vec3(vec&lt; 1, T, Q &gt; const &amp;v)'],['../a00305.html#ga482b60a842a5b154d3eed392417a9511',1,'glm::make_vec3(vec&lt; 2, T, Q &gt; const &amp;v)'],['../a00305.html#gacd57046034df557b8b1c457f58613623',1,'glm::make_vec3(vec&lt; 3, T, Q &gt; const &amp;v)'],['../a00305.html#ga8b589ed7d41a298b516d2a69169248f1',1,'glm::make_vec3(vec&lt; 4, T, Q &gt; const &amp;v)'],['../a00305.html#gad9e0d36ff489cb30c65ad1fa40351651',1,'glm::make_vec3(T const *const ptr)']]],
  ['make_5fvec4',['make_vec4',['../a00305.html#ga600cb97f70c5d50d3a4a145e1cafbf37',1,'glm::make_vec4(vec&lt; 1, T, Q &gt; const &amp;v)'],['../a00305.html#gaa9bd116caf28196fd1cf00b278286fa7',1,'glm::make_vec4(vec&lt; 2, T, Q &gt; const &amp;v)'],['../a00305.html#ga4036328ba4702c74cbdfad1fc03d1b8f',1,'glm::make_vec4(vec&lt; 3, T, Q &gt; const &amp;v)'],['../a00305.html#gaa95cb15732f708f613e65a0578895ae5',1,'glm::make_vec4(vec&lt; 4, T, Q &gt; const &amp;v)'],['../a00305.html#ga63f576518993efc22a969f18f80e29bb',1,'glm::make_vec4(T const *const ptr)']]],
  ['mask',['mask',['../a00288.html#gad7eba518a0b71662114571ee76939f8a',1,'glm::mask(genIUType Bits)'],['../a00288.html#ga2e64e3b922a296033b825311e7f5fff1',1,'glm::mask(vec&lt; L, T, Q &gt; const &amp;v)']]],
  ['mat2x4_5fcast',['mat2x4_cast',['../a00317.html#gae99d143b37f9cad4cd9285571aab685a',1,'glm']]],
  ['mat3_5fcast',['mat3_cast',['../a00299.html#ga333ab70047fbe4132406100c292dbc89',1,'glm']]],
  ['mat3x4_5fcast',['mat3x4_cast',['../a00317.html#gaf59f5bb69620d2891c3795c6f2639179',1,'glm']]],
  ['mat4_5fcast',['mat4_cast',['../a00299.html#ga1113212d9bdefc2e31ad40e5bbb506f3',1,'glm']]],
  ['matrixcompmult',['matrixCompMult',['../a00371.html#gaf14569404c779fedca98d0b9b8e58c1f',1,'glm']]],
  ['matrixcross3',['matrixCross3',['../a00334.html#ga5802386bb4c37b3332a3b6fd8b6960ff',1,'glm']]],
  ['matrixcross4',['matrixCross4',['../a00334.html#ga20057fff91ddafa102934adb25458cde',1,'glm']]],
  ['max',['max',['../a00241.html#gae02d42887fc5570451f880e3c624b9ac',1,'glm::max(genType x, genType y)'],['../a00241.html#ga03e45d6e60d1c36edb00c52edeea0f31',1,'glm::max(vec&lt; L, T, Q &gt; const &amp;x, T y)'],['../a00241.html#gac1fec0c3303b572a6d4697a637213870',1,'glm::max(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)'],['../a00258.html#gaa20839d9ab14514f8966f69877ea0de8',1,'glm::max(T a, T b, T c)'],['../a00258.html#ga2274b5e75ed84b0b1e50d8d22f1f2f67',1,'glm::max(T a, T b, T c, T d)'],['../a00267.html#gaa45d34f6a2906f8bf58ab2ba5429234d',1,'glm::max(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, T, Q &gt; const &amp;z)'],['../a00267.html#ga94d42b8da2b4ded5ddf7504fbdc6bf10',1,'glm::max(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, T, Q &gt; const &amp;z, vec&lt; L, T, Q &gt; const &amp;w)'],['../a00321.html#ga04991ccb9865c4c4e58488cfb209ce69',1,'glm::max(T const &amp;x, T const &amp;y, T const &amp;z)'],['../a00321.html#gae1b7bbe5c91de4924835ea3e14530744',1,'glm::max(C&lt; T &gt; const &amp;x, typename C&lt; T &gt;::T const &amp;y, typename C&lt; T &gt;::T const &amp;z)'],['../a00321.html#gaf832e9d4ab4826b2dda2fda25935a3a4',1,'glm::max(C&lt; T &gt; const &amp;x, C&lt; T &gt; const &amp;y, C&lt; T &gt; const &amp;z)'],['../a00321.html#ga78e04a0cef1c4863fcae1a2130500d87',1,'glm::max(T const &amp;x, T const &amp;y, T const &amp;z, T const &amp;w)'],['../a00321.html#ga7cca8b53cfda402040494cdf40fbdf4a',1,'glm::max(C&lt; T &gt; const &amp;x, typename C&lt; T &gt;::T const &amp;y, typename C&lt; T &gt;::T const &amp;z, typename C&lt; T &gt;::T const &amp;w)'],['../a00321.html#gaacffbc466c2d08c140b181e7fd8a4858',1,'glm::max(C&lt; T &gt; const &amp;x, C&lt; T &gt; const &amp;y, C&lt; T &gt; const &amp;z, C&lt; T &gt; const &amp;w)']]],
  ['min',['min',['../a00241.html#ga6cf8098827054a270ee36b18e30d471d',1,'glm::min(genType x, genType y)'],['../a00241.html#gaa7d015eba1f9f48519251f4abe69b14d',1,'glm::min(vec&lt; L, T, Q &gt; const &amp;x, T y)'],['../a00241.html#ga31f49ef9e7d1beb003160c5e009b0c48',1,'glm::min(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)'],['../a00258.html#ga420b37cbd98c395b93dab0278305cd46',1,'glm::min(T a, T b, T c)'],['../a00258.html#ga0d24a9acb8178df77e4aff90cbb2010d',1,'glm::min(T a, T b, T c, T d)'],['../a00267.html#ga3cd83d80fd4f433d8e333593ec56dddf',1,'glm::min(vec&lt; L, T, Q &gt; const &amp;a, vec&lt; L, T, Q &gt; const &amp;b, vec&lt; L, T, Q &gt; const &amp;c)'],['../a00267.html#gab66920ed064ab518d6859c5a889c4be4',1,'glm::min(vec&lt; L, T, Q &gt; const &amp;a, vec&lt; L, T, Q &gt; const &amp;b, vec&lt; L, T, Q &gt; const &amp;c, vec&lt; L, T, Q &gt; const &amp;d)'],['../a00321.html#ga713d3f9b3e76312c0d314e0c8611a6a6',1,'glm::min(T const &amp;x, T const &amp;y, T const &amp;z)'],['../a00321.html#ga74d1a96e7cdbac40f6d35142d3bcbbd4',1,'glm::min(C&lt; T &gt; const &amp;x, typename C&lt; T &gt;::T const &amp;y, typename C&lt; T &gt;::T const &amp;z)'],['../a00321.html#ga42b5c3fc027fd3d9a50d2ccc9126d9f0',1,'glm::min(C&lt; T &gt; const &amp;x, C&lt; T &gt; const &amp;y, C&lt; T &gt; const &amp;z)'],['../a00321.html#ga95466987024d03039607f09e69813d69',1,'glm::min(T const &amp;x, T const &amp;y, T const &amp;z, T const &amp;w)'],['../a00321.html#ga4fe35dd31dd0c45693c9b60b830b8d47',1,'glm::min(C&lt; T &gt; const &amp;x, typename C&lt; T &gt;::T const &amp;y, typename C&lt; T &gt;::T const &amp;z, typename C&lt; T &gt;::T const &amp;w)'],['../a00321.html#ga7471ea4159eed8dd9ea4ac5d46c2fead',1,'glm::min(C&lt; T &gt; const &amp;x, C&lt; T &gt; const &amp;y, C&lt; T &gt; const &amp;z, C&lt; T &gt; const &amp;w)']]],
  ['mirrorclamp',['mirrorClamp',['../a00369.html#gaa6856a0a048d2749252848da35e10c8b',1,'glm']]],
  ['mirrorrepeat',['mirrorRepeat',['../a00369.html#ga16a89b0661b60d5bea85137bbae74d73',1,'glm']]],
  ['mix',['mix',['../a00241.html#ga8e93f374aae27d1a88b921860351f8d4',1,'glm::mix(genTypeT x, genTypeT y, genTypeU a)'],['../a00248.html#gafbfe587b8da11fb89a30c3d67dd5ccc2',1,'glm::mix(qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y, T a)']]],
  ['mixedproduct',['mixedProduct',['../a00342.html#gab3c6048fbb67f7243b088a4fee48d020',1,'glm']]],
  ['mod',['mod',['../a00241.html#ga9b197a452cd52db3c5c18bac72bd7798',1,'glm::mod(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)'],['../a00330.html#gaabfbb41531ab7ad8d06fc176edfba785',1,'glm::mod(int x, int y)'],['../a00330.html#ga63fc8d63e7da1706439233b386ba8b6f',1,'glm::mod(uint x, uint y)']]],
  ['modf',['modf',['../a00241.html#ga85e33f139b8db1b39b590a5713b9e679',1,'glm']]]
];
