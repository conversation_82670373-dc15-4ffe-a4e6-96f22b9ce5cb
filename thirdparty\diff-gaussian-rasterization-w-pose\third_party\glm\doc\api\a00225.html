<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: vector_relational.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">vector_relational.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00280.html">Core features</a>  
<a href="#details">More...</a></p>

<p><a href="a00225_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga87e53f50b679f5f95c5cb4780311b3dd"><td class="memTemplParams" colspan="2">template&lt;length_t L, qualifier Q&gt; </td></tr>
<tr class="memitem:ga87e53f50b679f5f95c5cb4780311b3dd"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00374.html#ga87e53f50b679f5f95c5cb4780311b3dd">all</a> (vec&lt; L, bool, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga87e53f50b679f5f95c5cb4780311b3dd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if all components of x are true.  <a href="a00374.html#ga87e53f50b679f5f95c5cb4780311b3dd">More...</a><br /></td></tr>
<tr class="separator:ga87e53f50b679f5f95c5cb4780311b3dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga911b3f8e41459dd551ccb6d385d91061"><td class="memTemplParams" colspan="2">template&lt;length_t L, qualifier Q&gt; </td></tr>
<tr class="memitem:ga911b3f8e41459dd551ccb6d385d91061"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00374.html#ga911b3f8e41459dd551ccb6d385d91061">any</a> (vec&lt; L, bool, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga911b3f8e41459dd551ccb6d385d91061"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if any component of x is true.  <a href="a00374.html#ga911b3f8e41459dd551ccb6d385d91061">More...</a><br /></td></tr>
<tr class="separator:ga911b3f8e41459dd551ccb6d385d91061"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab4c5cfdaa70834421397a85aa83ad946"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gab4c5cfdaa70834421397a85aa83ad946"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00374.html#gab4c5cfdaa70834421397a85aa83ad946">equal</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:gab4c5cfdaa70834421397a85aa83ad946"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of result x == y.  <a href="a00374.html#gab4c5cfdaa70834421397a85aa83ad946">More...</a><br /></td></tr>
<tr class="separator:gab4c5cfdaa70834421397a85aa83ad946"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadfdb8ea82deca869ddc7e63ea5a63ae4"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gadfdb8ea82deca869ddc7e63ea5a63ae4"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00374.html#gadfdb8ea82deca869ddc7e63ea5a63ae4">greaterThan</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:gadfdb8ea82deca869ddc7e63ea5a63ae4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of result x &gt; y.  <a href="a00374.html#gadfdb8ea82deca869ddc7e63ea5a63ae4">More...</a><br /></td></tr>
<tr class="separator:gadfdb8ea82deca869ddc7e63ea5a63ae4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga859975f538940f8d18fe62f916b9abd7"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga859975f538940f8d18fe62f916b9abd7"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00374.html#ga859975f538940f8d18fe62f916b9abd7">greaterThanEqual</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:ga859975f538940f8d18fe62f916b9abd7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of result x &gt;= y.  <a href="a00374.html#ga859975f538940f8d18fe62f916b9abd7">More...</a><br /></td></tr>
<tr class="separator:ga859975f538940f8d18fe62f916b9abd7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae90ed1592c395f93e3f3dfce6b2f39c6"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gae90ed1592c395f93e3f3dfce6b2f39c6"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00374.html#gae90ed1592c395f93e3f3dfce6b2f39c6">lessThan</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:gae90ed1592c395f93e3f3dfce6b2f39c6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison result of x &lt; y.  <a href="a00374.html#gae90ed1592c395f93e3f3dfce6b2f39c6">More...</a><br /></td></tr>
<tr class="separator:gae90ed1592c395f93e3f3dfce6b2f39c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab0bdafc019d227257ff73fb5bcca1718"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gab0bdafc019d227257ff73fb5bcca1718"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00374.html#gab0bdafc019d227257ff73fb5bcca1718">lessThanEqual</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:gab0bdafc019d227257ff73fb5bcca1718"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of result x &lt;= y.  <a href="a00374.html#gab0bdafc019d227257ff73fb5bcca1718">More...</a><br /></td></tr>
<tr class="separator:gab0bdafc019d227257ff73fb5bcca1718"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga610fcd175791fd246e328ffee10dbf1e"><td class="memTemplParams" colspan="2">template&lt;length_t L, qualifier Q&gt; </td></tr>
<tr class="memitem:ga610fcd175791fd246e328ffee10dbf1e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00374.html#ga610fcd175791fd246e328ffee10dbf1e">not_</a> (vec&lt; L, bool, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga610fcd175791fd246e328ffee10dbf1e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise logical complement of x.  <a href="a00374.html#ga610fcd175791fd246e328ffee10dbf1e">More...</a><br /></td></tr>
<tr class="separator:ga610fcd175791fd246e328ffee10dbf1e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga17c19dc1b76cd5aef63e9e7ff3aa3c27"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga17c19dc1b76cd5aef63e9e7ff3aa3c27"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00374.html#ga17c19dc1b76cd5aef63e9e7ff3aa3c27">notEqual</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:ga17c19dc1b76cd5aef63e9e7ff3aa3c27"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of result x != y.  <a href="a00374.html#ga17c19dc1b76cd5aef63e9e7ff3aa3c27">More...</a><br /></td></tr>
<tr class="separator:ga17c19dc1b76cd5aef63e9e7ff3aa3c27"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00280.html">Core features</a> </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.7 Vector Relational Functions</a> </dd></dl>

<p>Definition in file <a class="el" href="a00225_source.html">vector_relational.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
