<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_EXT_scalar_int_sized</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a>  </div>
  <div class="headertitle">
<div class="title">GLM_EXT_scalar_int_sized<div class="ingroups"><a class="el" href="a00285.html">Stable extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Exposes sized signed integer scalar types.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga259fa4834387bd68627ddf37bb3ebdb9"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga259fa4834387bd68627ddf37bb3ebdb9"></a>
typedef detail::int16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00260.html#ga259fa4834387bd68627ddf37bb3ebdb9">int16</a></td></tr>
<tr class="memdesc:ga259fa4834387bd68627ddf37bb3ebdb9"><td class="mdescLeft">&#160;</td><td class="mdescRight">16 bit signed integer type. <br /></td></tr>
<tr class="separator:ga259fa4834387bd68627ddf37bb3ebdb9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga43d43196463bde49cb067f5c20ab8481"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga43d43196463bde49cb067f5c20ab8481"></a>
typedef detail::int32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00260.html#ga43d43196463bde49cb067f5c20ab8481">int32</a></td></tr>
<tr class="memdesc:ga43d43196463bde49cb067f5c20ab8481"><td class="mdescLeft">&#160;</td><td class="mdescRight">32 bit signed integer type. <br /></td></tr>
<tr class="separator:ga43d43196463bde49cb067f5c20ab8481"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaff5189f97f9e842d9636a0f240001b2e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaff5189f97f9e842d9636a0f240001b2e"></a>
typedef detail::int64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">int64</a></td></tr>
<tr class="memdesc:gaff5189f97f9e842d9636a0f240001b2e"><td class="mdescLeft">&#160;</td><td class="mdescRight">64 bit signed integer type. <br /></td></tr>
<tr class="separator:gaff5189f97f9e842d9636a0f240001b2e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1b956fe1df85f3c132b21edb4e116458"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga1b956fe1df85f3c132b21edb4e116458"></a>
typedef detail::int8&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00260.html#ga1b956fe1df85f3c132b21edb4e116458">int8</a></td></tr>
<tr class="memdesc:ga1b956fe1df85f3c132b21edb4e116458"><td class="mdescLeft">&#160;</td><td class="mdescRight">8 bit signed integer type. <br /></td></tr>
<tr class="separator:ga1b956fe1df85f3c132b21edb4e116458"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Exposes sized signed integer scalar types. </p>
<p>Include &lt;<a class="el" href="a00146.html" title="GLM_EXT_scalar_int_sized ">glm/ext/scalar_int_sized.hpp</a>&gt; to use the features of this extension.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00263.html" title="Exposes sized unsigned integer scalar types. ">GLM_EXT_scalar_uint_sized</a> </dd></dl>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
