<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_matrix_query</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_matrix_query<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00106.html" title="GLM_GTX_matrix_query ">glm/gtx/matrix_query.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gaee935d145581c82e82b154ccfd78ad91"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q, template&lt; length_t, length_t, typename, qualifier &gt; class matType&gt; </td></tr>
<tr class="memitem:gaee935d145581c82e82b154ccfd78ad91"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00340.html#gaee935d145581c82e82b154ccfd78ad91">isIdentity</a> (matType&lt; C, R, T, Q &gt; const &amp;m, T const &amp;epsilon)</td></tr>
<tr class="memdesc:gaee935d145581c82e82b154ccfd78ad91"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return whether a matrix is an identity matrix.  <a href="a00340.html#gaee935d145581c82e82b154ccfd78ad91">More...</a><br /></td></tr>
<tr class="separator:gaee935d145581c82e82b154ccfd78ad91"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae785af56f47ce220a1609f7f84aa077a"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gae785af56f47ce220a1609f7f84aa077a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00340.html#gae785af56f47ce220a1609f7f84aa077a">isNormalized</a> (mat&lt; 2, 2, T, Q &gt; const &amp;m, T const &amp;epsilon)</td></tr>
<tr class="memdesc:gae785af56f47ce220a1609f7f84aa077a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return whether a matrix is a normalized matrix.  <a href="a00340.html#gae785af56f47ce220a1609f7f84aa077a">More...</a><br /></td></tr>
<tr class="separator:gae785af56f47ce220a1609f7f84aa077a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa068311695f28f5f555f5f746a6a66fb"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaa068311695f28f5f555f5f746a6a66fb"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00340.html#gaa068311695f28f5f555f5f746a6a66fb">isNormalized</a> (mat&lt; 3, 3, T, Q &gt; const &amp;m, T const &amp;epsilon)</td></tr>
<tr class="memdesc:gaa068311695f28f5f555f5f746a6a66fb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return whether a matrix is a normalized matrix.  <a href="a00340.html#gaa068311695f28f5f555f5f746a6a66fb">More...</a><br /></td></tr>
<tr class="separator:gaa068311695f28f5f555f5f746a6a66fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4d9bb4d0465df49fedfad79adc6ce4ad"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga4d9bb4d0465df49fedfad79adc6ce4ad"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00340.html#ga4d9bb4d0465df49fedfad79adc6ce4ad">isNormalized</a> (mat&lt; 4, 4, T, Q &gt; const &amp;m, T const &amp;epsilon)</td></tr>
<tr class="memdesc:ga4d9bb4d0465df49fedfad79adc6ce4ad"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return whether a matrix is a normalized matrix.  <a href="a00340.html#ga4d9bb4d0465df49fedfad79adc6ce4ad">More...</a><br /></td></tr>
<tr class="separator:ga4d9bb4d0465df49fedfad79adc6ce4ad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9790ec222ce948c0ff0d8ce927340dba"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga9790ec222ce948c0ff0d8ce927340dba"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00340.html#ga9790ec222ce948c0ff0d8ce927340dba">isNull</a> (mat&lt; 2, 2, T, Q &gt; const &amp;m, T const &amp;epsilon)</td></tr>
<tr class="memdesc:ga9790ec222ce948c0ff0d8ce927340dba"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return whether a matrix a null matrix.  <a href="a00340.html#ga9790ec222ce948c0ff0d8ce927340dba">More...</a><br /></td></tr>
<tr class="separator:ga9790ec222ce948c0ff0d8ce927340dba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae14501c6b14ccda6014cc5350080103d"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gae14501c6b14ccda6014cc5350080103d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00340.html#gae14501c6b14ccda6014cc5350080103d">isNull</a> (mat&lt; 3, 3, T, Q &gt; const &amp;m, T const &amp;epsilon)</td></tr>
<tr class="memdesc:gae14501c6b14ccda6014cc5350080103d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return whether a matrix a null matrix.  <a href="a00340.html#gae14501c6b14ccda6014cc5350080103d">More...</a><br /></td></tr>
<tr class="separator:gae14501c6b14ccda6014cc5350080103d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2b98bb30a9fefa7cdea5f1dcddba677b"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga2b98bb30a9fefa7cdea5f1dcddba677b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00340.html#ga2b98bb30a9fefa7cdea5f1dcddba677b">isNull</a> (mat&lt; 4, 4, T, Q &gt; const &amp;m, T const &amp;epsilon)</td></tr>
<tr class="memdesc:ga2b98bb30a9fefa7cdea5f1dcddba677b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return whether a matrix is a null matrix.  <a href="a00340.html#ga2b98bb30a9fefa7cdea5f1dcddba677b">More...</a><br /></td></tr>
<tr class="separator:ga2b98bb30a9fefa7cdea5f1dcddba677b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga58f3289f74dcab653387dd78ad93ca40"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q, template&lt; length_t, length_t, typename, qualifier &gt; class matType&gt; </td></tr>
<tr class="memitem:ga58f3289f74dcab653387dd78ad93ca40"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00340.html#ga58f3289f74dcab653387dd78ad93ca40">isOrthogonal</a> (matType&lt; C, R, T, Q &gt; const &amp;m, T const &amp;epsilon)</td></tr>
<tr class="memdesc:ga58f3289f74dcab653387dd78ad93ca40"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return whether a matrix is an orthonormalized matrix.  <a href="a00340.html#ga58f3289f74dcab653387dd78ad93ca40">More...</a><br /></td></tr>
<tr class="separator:ga58f3289f74dcab653387dd78ad93ca40"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00106.html" title="GLM_GTX_matrix_query ">glm/gtx/matrix_query.hpp</a>&gt; to use the features of this extension. </p>
<p>Query to evaluate matrix properties </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gaee935d145581c82e82b154ccfd78ad91"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL bool glm::isIdentity </td>
          <td>(</td>
          <td class="paramtype">matType&lt; C, R, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>epsilon</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return whether a matrix is an identity matrix. </p>
<p>From GLM_GTX_matrix_query extension. </p>

</div>
</div>
<a class="anchor" id="gae785af56f47ce220a1609f7f84aa077a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL bool glm::isNormalized </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 2, 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>epsilon</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return whether a matrix is a normalized matrix. </p>
<p>From GLM_GTX_matrix_query extension. </p>

</div>
</div>
<a class="anchor" id="gaa068311695f28f5f555f5f746a6a66fb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL bool glm::isNormalized </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 3, 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>epsilon</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return whether a matrix is a normalized matrix. </p>
<p>From GLM_GTX_matrix_query extension. </p>

</div>
</div>
<a class="anchor" id="ga4d9bb4d0465df49fedfad79adc6ce4ad"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL bool glm::isNormalized </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>epsilon</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return whether a matrix is a normalized matrix. </p>
<p>From GLM_GTX_matrix_query extension. </p>

</div>
</div>
<a class="anchor" id="ga9790ec222ce948c0ff0d8ce927340dba"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL bool glm::isNull </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 2, 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>epsilon</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return whether a matrix a null matrix. </p>
<p>From GLM_GTX_matrix_query extension. </p>

</div>
</div>
<a class="anchor" id="gae14501c6b14ccda6014cc5350080103d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL bool glm::isNull </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 3, 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>epsilon</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return whether a matrix a null matrix. </p>
<p>From GLM_GTX_matrix_query extension. </p>

</div>
</div>
<a class="anchor" id="ga2b98bb30a9fefa7cdea5f1dcddba677b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL bool glm::isNull </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>epsilon</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return whether a matrix is a null matrix. </p>
<p>From GLM_GTX_matrix_query extension. </p>

</div>
</div>
<a class="anchor" id="ga58f3289f74dcab653387dd78ad93ca40"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL bool glm::isOrthogonal </td>
          <td>(</td>
          <td class="paramtype">matType&lt; C, R, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>epsilon</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return whether a matrix is an orthonormalized matrix. </p>
<p>From GLM_GTX_matrix_query extension. </p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
