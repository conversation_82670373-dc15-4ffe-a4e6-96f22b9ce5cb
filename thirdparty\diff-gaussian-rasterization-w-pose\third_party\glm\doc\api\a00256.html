<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_EXT_quaternion_transform</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_EXT_quaternion_transform<div class="ingroups"><a class="el" href="a00285.html">Stable extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Provides transformation functions for quaternion types.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gaab2d37ef7265819f1d2939b9dc2c52ac"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaab2d37ef7265819f1d2939b9dc2c52ac"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00256.html#gaab2d37ef7265819f1d2939b9dc2c52ac">exp</a> (qua&lt; T, Q &gt; const &amp;q)</td></tr>
<tr class="memdesc:gaab2d37ef7265819f1d2939b9dc2c52ac"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a exponential of a quaternion.  <a href="a00256.html#gaab2d37ef7265819f1d2939b9dc2c52ac">More...</a><br /></td></tr>
<tr class="separator:gaab2d37ef7265819f1d2939b9dc2c52ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa5f7b20e296671b16ce25a2ab7ad5473"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaa5f7b20e296671b16ce25a2ab7ad5473"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00256.html#gaa5f7b20e296671b16ce25a2ab7ad5473">log</a> (qua&lt; T, Q &gt; const &amp;q)</td></tr>
<tr class="memdesc:gaa5f7b20e296671b16ce25a2ab7ad5473"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a logarithm of a quaternion.  <a href="a00256.html#gaa5f7b20e296671b16ce25a2ab7ad5473">More...</a><br /></td></tr>
<tr class="separator:gaa5f7b20e296671b16ce25a2ab7ad5473"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4975ffcacd312a8c0bbd046a76c5607e"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga4975ffcacd312a8c0bbd046a76c5607e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00256.html#ga4975ffcacd312a8c0bbd046a76c5607e">pow</a> (qua&lt; T, Q &gt; const &amp;q, T y)</td></tr>
<tr class="memdesc:ga4975ffcacd312a8c0bbd046a76c5607e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a quaternion raised to a power.  <a href="a00256.html#ga4975ffcacd312a8c0bbd046a76c5607e">More...</a><br /></td></tr>
<tr class="separator:ga4975ffcacd312a8c0bbd046a76c5607e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabfc57de6d4d2e11970f54119c5ccf0f5"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gabfc57de6d4d2e11970f54119c5ccf0f5"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00256.html#gabfc57de6d4d2e11970f54119c5ccf0f5">rotate</a> (qua&lt; T, Q &gt; const &amp;q, T const &amp;angle, vec&lt; 3, T, Q &gt; const &amp;axis)</td></tr>
<tr class="memdesc:gabfc57de6d4d2e11970f54119c5ccf0f5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rotates a quaternion from a vector of 3 components axis and an angle.  <a href="a00256.html#gabfc57de6d4d2e11970f54119c5ccf0f5">More...</a><br /></td></tr>
<tr class="separator:gabfc57de6d4d2e11970f54119c5ccf0f5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga64b7b255ed7bcba616fe6b44470b022e"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga64b7b255ed7bcba616fe6b44470b022e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00256.html#ga64b7b255ed7bcba616fe6b44470b022e">sqrt</a> (qua&lt; T, Q &gt; const &amp;q)</td></tr>
<tr class="memdesc:ga64b7b255ed7bcba616fe6b44470b022e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the square root of a quaternion.  <a href="a00256.html#ga64b7b255ed7bcba616fe6b44470b022e">More...</a><br /></td></tr>
<tr class="separator:ga64b7b255ed7bcba616fe6b44470b022e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Provides transformation functions for quaternion types. </p>
<p>Include &lt;<a class="el" href="a00135.html" title="GLM_EXT_quaternion_transform ">glm/ext/quaternion_transform.hpp</a>&gt; to use the features of this extension.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00252.html" title="Exposes single-precision floating point quaternion type. ">GLM_EXT_quaternion_float</a> </dd>
<dd>
<a class="el" href="a00249.html" title="Exposes double-precision floating point quaternion type. ">GLM_EXT_quaternion_double</a> </dd>
<dd>
<a class="el" href="a00251.html" title="Provides exponential functions for quaternion types. ">GLM_EXT_quaternion_exponential</a> </dd>
<dd>
<a class="el" href="a00254.html" title="Provides geometric functions for quaternion types. ">GLM_EXT_quaternion_geometric</a> </dd>
<dd>
<a class="el" href="a00255.html" title="Exposes comparison functions for quaternion types that take a user defined epsilon values...">GLM_EXT_quaternion_relational</a> </dd>
<dd>
<a class="el" href="a00257.html" title="Provides trigonometric functions for quaternion types. ">GLM_EXT_quaternion_trigonometric</a> </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gaab2d37ef7265819f1d2939b9dc2c52ac"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::exp </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns a exponential of a quaternion. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
    <tr><td class="paramname">Q</td><td>A value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="gaa5f7b20e296671b16ce25a2ab7ad5473"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::log </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns a logarithm of a quaternion. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
    <tr><td class="paramname">Q</td><td>A value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga4975ffcacd312a8c0bbd046a76c5607e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::pow </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns a quaternion raised to a power. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
    <tr><td class="paramname">Q</td><td>A value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="gabfc57de6d4d2e11970f54119c5ccf0f5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::rotate </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>axis</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Rotates a quaternion from a vector of 3 components axis and an angle. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">q</td><td>Source orientation </td></tr>
    <tr><td class="paramname">angle</td><td>Angle expressed in radians. </td></tr>
    <tr><td class="paramname">axis</td><td>Axis of the rotation</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga64b7b255ed7bcba616fe6b44470b022e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::sqrt </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the square root of a quaternion. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
    <tr><td class="paramname">Q</td><td>A value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
