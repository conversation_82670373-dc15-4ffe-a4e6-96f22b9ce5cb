<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_EXT_scalar_uint_sized</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a>  </div>
  <div class="headertitle">
<div class="title">GLM_EXT_scalar_uint_sized<div class="ingroups"><a class="el" href="a00285.html">Stable extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Exposes sized unsigned integer scalar types.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga05f6b0ae8f6a6e135b0e290c25fe0e4e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga05f6b0ae8f6a6e135b0e290c25fe0e4e"></a>
typedef detail::uint16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">uint16</a></td></tr>
<tr class="memdesc:ga05f6b0ae8f6a6e135b0e290c25fe0e4e"><td class="mdescLeft">&#160;</td><td class="mdescRight">16 bit unsigned integer type. <br /></td></tr>
<tr class="separator:ga05f6b0ae8f6a6e135b0e290c25fe0e4e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1134b580f8da4de94ca6b1de4d37975e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga1134b580f8da4de94ca6b1de4d37975e"></a>
typedef detail::uint32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00263.html#ga1134b580f8da4de94ca6b1de4d37975e">uint32</a></td></tr>
<tr class="memdesc:ga1134b580f8da4de94ca6b1de4d37975e"><td class="mdescLeft">&#160;</td><td class="mdescRight">32 bit unsigned integer type. <br /></td></tr>
<tr class="separator:ga1134b580f8da4de94ca6b1de4d37975e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab630f76c26b50298187f7889104d4b9c"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gab630f76c26b50298187f7889104d4b9c"></a>
typedef detail::uint64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a></td></tr>
<tr class="memdesc:gab630f76c26b50298187f7889104d4b9c"><td class="mdescLeft">&#160;</td><td class="mdescRight">64 bit unsigned integer type. <br /></td></tr>
<tr class="separator:gab630f76c26b50298187f7889104d4b9c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadde6aaee8457bee49c2a92621fe22b79"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gadde6aaee8457bee49c2a92621fe22b79"></a>
typedef detail::uint8&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00263.html#gadde6aaee8457bee49c2a92621fe22b79">uint8</a></td></tr>
<tr class="memdesc:gadde6aaee8457bee49c2a92621fe22b79"><td class="mdescLeft">&#160;</td><td class="mdescRight">8 bit unsigned integer type. <br /></td></tr>
<tr class="separator:gadde6aaee8457bee49c2a92621fe22b79"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Exposes sized unsigned integer scalar types. </p>
<p>Include &lt;<a class="el" href="a00151.html" title="GLM_EXT_scalar_uint_sized ">glm/ext/scalar_uint_sized.hpp</a>&gt; to use the features of this extension.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00260.html" title="Exposes sized signed integer scalar types. ">GLM_EXT_scalar_int_sized</a> </dd></dl>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
