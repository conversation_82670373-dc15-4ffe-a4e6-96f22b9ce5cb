<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: gtc Directory Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_4c6bd29c73fa4e5a2509e1c15f846751.html">gtc</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">gtc Directory Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="files"></a>
Files</h2></td></tr>
<tr class="memitem:a00009"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00009.html">bitfield.hpp</a> <a href="a00009_source.html">[code]</a></td></tr>
<tr class="memdesc:a00009"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00288.html">GLM_GTC_bitfield</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00012"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00012.html">gtc/color_space.hpp</a> <a href="a00012_source.html">[code]</a></td></tr>
<tr class="memdesc:a00012"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00289.html">GLM_GTC_color_space</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00021"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00021.html">constants.hpp</a> <a href="a00021_source.html">[code]</a></td></tr>
<tr class="memdesc:a00021"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00290.html">GLM_GTC_constants</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00024"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00024.html">epsilon.hpp</a> <a href="a00024_source.html">[code]</a></td></tr>
<tr class="memdesc:a00024"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00291.html">GLM_GTC_epsilon</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00041"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00041.html">gtc/integer.hpp</a> <a href="a00041_source.html">[code]</a></td></tr>
<tr class="memdesc:a00041"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00292.html">GLM_GTC_integer</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00058"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00058.html">matrix_access.hpp</a> <a href="a00058_source.html">[code]</a></td></tr>
<tr class="memdesc:a00058"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00293.html">GLM_GTC_matrix_access</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00100"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00100.html">matrix_integer.hpp</a> <a href="a00100_source.html">[code]</a></td></tr>
<tr class="memdesc:a00100"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00294.html">GLM_GTC_matrix_integer</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00102"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00102.html">matrix_inverse.hpp</a> <a href="a00102_source.html">[code]</a></td></tr>
<tr class="memdesc:a00102"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00295.html">GLM_GTC_matrix_inverse</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00109"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00109.html">gtc/matrix_transform.hpp</a> <a href="a00109_source.html">[code]</a></td></tr>
<tr class="memdesc:a00109"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00296.html">GLM_GTC_matrix_transform</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00112"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00112.html">noise.hpp</a> <a href="a00112_source.html">[code]</a></td></tr>
<tr class="memdesc:a00112"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00297.html">GLM_GTC_noise</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00119"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00119.html">gtc/packing.hpp</a> <a href="a00119_source.html">[code]</a></td></tr>
<tr class="memdesc:a00119"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00298.html">GLM_GTC_packing</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00125"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00125.html">gtc/quaternion.hpp</a> <a href="a00125_source.html">[code]</a></td></tr>
<tr class="memdesc:a00125"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00299.html">GLM_GTC_quaternion</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00137"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00137.html">random.hpp</a> <a href="a00137_source.html">[code]</a></td></tr>
<tr class="memdesc:a00137"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00300.html">GLM_GTC_random</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00140"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00140.html">reciprocal.hpp</a> <a href="a00140_source.html">[code]</a></td></tr>
<tr class="memdesc:a00140"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00301.html">GLM_GTC_reciprocal</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00143"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00143.html">round.hpp</a> <a href="a00143_source.html">[code]</a></td></tr>
<tr class="memdesc:a00143"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00302.html">GLM_GTC_round</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00161"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00161.html">gtc/type_aligned.hpp</a> <a href="a00161_source.html">[code]</a></td></tr>
<tr class="memdesc:a00161"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00303.html">GLM_GTC_type_aligned</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00174"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00174.html">type_precision.hpp</a> <a href="a00174_source.html">[code]</a></td></tr>
<tr class="memdesc:a00174"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00304.html">GLM_GTC_type_precision</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00175"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00175.html">type_ptr.hpp</a> <a href="a00175_source.html">[code]</a></td></tr>
<tr class="memdesc:a00175"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00305.html">GLM_GTC_type_ptr</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00182"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00182.html">ulp.hpp</a> <a href="a00182_source.html">[code]</a></td></tr>
<tr class="memdesc:a00182"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00306.html">GLM_GTC_ulp</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00183"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00183.html">vec1.hpp</a> <a href="a00183_source.html">[code]</a></td></tr>
<tr class="memdesc:a00183"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00307.html">GLM_GTC_vec1</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
